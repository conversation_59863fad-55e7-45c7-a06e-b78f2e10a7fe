{"name": "vendor.ai", "version": "1.0.0", "main": "index.js", "license": "MIT", "workspaces": ["packages/*"], "private": true, "scripts": {"start": "yarn workspace be start", "build": "yarn workspace be run build", "be_start": "yarn workspace be start", "dev": "concurrently \"yarn workspace be run dev\" \"yarn workspace fe run dev\"", "be": "yarn workspace be run dev", "fe": "yarn workspace fe run dev"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.1.4"}}