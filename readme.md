Working with Submodules:
Cloning the Monorepo with Submodules:
When cloning the monorepo for the first time, use the --recursive option to initialize and update the submodules:

bash
git clone --recursive <monorepo-url>
Updating Submodules:
If changes are made in the submodule repository, you need to update the submodule reference in the main repository:

bash
git submodule update --recursive --remote
Committing Changes in Submodule:
After making changes in the submodule, navigate to the submodule directory, commit the changes, and then commit the submodule reference in the main repository:

bash
cd frontend

# Make changes and commit

git add .
git commit -m "Changes in frontend"

# Navigate back to the main repo

cd ..
git add frontend
git commit -m "Update frontend submodule"
Cloning for Others:
When others clone your monorepo and want to include the submodule, they need to initialize and update the submodule:

bash
Copy code
git clone --recursive <monorepo-url>

# vendoai

# vendoai

kjjhgfghjgfgh
hello
hello
