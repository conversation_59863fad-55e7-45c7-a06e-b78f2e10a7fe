'use client';
import { useFormContext } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField as FF,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Form } from '../../../shared/types/Form';
import ChipInput, { ChipOption } from '../ui/chip-input';
import { useState } from 'react';
import { InputProps } from '../ui/input';

type Props = Form & InputProps & { options?: ChipOption[] };

export default function FormChipInput({
  name,
  label,
  options,
  ...otherProps
}: Props) {
  const methods = useFormContext();

  const [value, setValue] = useState('');

  return (
    <FF
      name={name}
      render={({ field: { value: fieldValue, onChange, ...rest } }) => {
        const onChipAction = (v: string) => {
          const chips: ChipOption[] = [...(fieldValue ?? []), v];
          methods.setValue(name, chips);
          setValue('');
        };

        const onChipSelect = (v: string) => {
          methods.setValue(
            name,
            ((fieldValue ?? []) as string[]).filter((value) => value !== v)
          );
        };

        const chips = ((fieldValue ?? []) as string[]).map((v) => {
          const label = options?.find((o) => o.value === v)?.label ?? v;
          return { label, value: v };
        });

        return (
          <FormItem>
            <div className='items-center flex gap-1'>
              {label && <FormLabel>{label}</FormLabel>}
            </div>
            <FormControl>
              <ChipInput
                chips={chips}
                onChipAction={onChipAction}
                onChipSelect={onChipSelect}
                value={value}
                onChange={(e) => {
                  setValue(e.target.value);
                }}
                {...rest}
                {...otherProps}
              />
            </FormControl>

            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
