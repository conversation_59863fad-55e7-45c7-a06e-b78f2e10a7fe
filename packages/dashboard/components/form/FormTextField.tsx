'use client';

import { useFormContext } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Form } from '../../../shared/types/Form';

type Props = Form & React.InputHTMLAttributes<HTMLInputElement>;

export function FormTextField({
  name,
  description,
  label,
  placeholder,
  lableClass,
  ...otherProps
}: Props) {
  const methods = useFormContext();

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel className={`${lableClass}`}>{label}</FormLabel>}
          <FormControl>
            <Textarea
              placeholder={placeholder}
              className={otherProps.className}
              {...field}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
