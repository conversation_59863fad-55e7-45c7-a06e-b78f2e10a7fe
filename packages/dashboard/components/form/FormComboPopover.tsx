'use client';

import { useFormContext } from 'react-hook-form';
import { Checkbox } from '@/components/ui/checkbox';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Form } from '../../../shared/types/Form';
import {
  ComboBoxOption,
  ComboPopoverProps,
  ComboboxPopover,
} from '../ui/form-combo-popover';

type Props = Form & { options: ComboBoxOption[] };

export function FormComboBoxPopover({
  name,
  placeholder,
  label,
  options,
  ...other
}: Props & ComboPopoverProps) {
  const methods = useFormContext();

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <div className='w-full'>
          <ComboboxPopover
            label={label || ''}
            options={options}
            value={field.value}
            onChange={field.onChange}
            placeholder={placeholder}
            allowCustomValues={false}
            {...other}
          />
          <FormMessage />
        </div>
      )}
    />
  );
}
