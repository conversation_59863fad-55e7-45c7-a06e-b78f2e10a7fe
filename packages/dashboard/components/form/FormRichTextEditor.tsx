import React, { useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Label } from '../ui/label';
import Tiptap, { useRichTextEditor } from '../tip-tap/tip-tap';

type Props = {
  name: string;
  label?: string;
};
export default function FormRichTextEditor({ name, label }: Props) {
  const methods = useFormContext();
  const editor = useRichTextEditor({
    onChange: (html) => methods.setValue(name, html),
  });

  const content = methods.watch(name);

  useEffect(() => {
    if (editor && content) editor.commands.setContent(content);
  }, [content, editor]);

  return (
    <div className='space-y-2'>
      <Label>{label}</Label>
      <Controller
        name={name}
        render={({ field: { onChange, value } }) => {
          return <Tiptap content={value} onChange={onChange} editor={editor} />;
        }}
      />
    </div>
  );
}
