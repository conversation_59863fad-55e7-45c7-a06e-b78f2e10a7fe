'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFormContext } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Form } from '../../../shared/types/Form';
import { cn } from '@/lib/utils';

type Props = Form & {
  options: {
    label: string;
    value: string;
  }[];
  optionClass?: string;
};
export function FormCheckBoxGroup({
  name,
  label,
  options,
  placeholder,
  lableClass,
  optionClass,
}: Props) {
  const methods = useFormContext();

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <div className='mb-4'>
            <FormLabel className={`text-base ${lableClass}`}>{label}</FormLabel>
            <FormDescription>{placeholder}</FormDescription>
          </div>
          {options.map(({ label, value }) => (
            <FormField
              key={value}
              name={name}
              render={({ field }) => {
                return (
                  <FormItem
                    key={value}
                    className='flex flex-row items-center space-x-3 space-y-0'
                  >
                    <FormControl>
                      <Checkbox
                        checked={field.value?.includes(value)}
                        onCheckedChange={(checked) => {
                          return checked
                            ? field.onChange([...(field.value || []), value])
                            : field.onChange(
                                field.value?.filter((v: string) => v !== value)
                              );
                        }}
                      />
                    </FormControl>
                    <FormLabel className={cn('font-normal', optionClass)}>
                      {label}
                    </FormLabel>
                  </FormItem>
                );
              }}
            />
          ))}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
