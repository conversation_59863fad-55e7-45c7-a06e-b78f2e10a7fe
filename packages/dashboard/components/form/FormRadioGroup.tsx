'use client';

import { useFormContext } from 'react-hook-form';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Form } from '../../../shared/types/Form';
import { cn } from '@/lib/utils';

type Props = Form & {
  options: {
    label: string;
    value: string;
  }[];
  className?: string;
  optionsClass?: string;
  mainClass?: string;
  optionClass?: string;
};
export function FormRadioGroup({
  name,
  label,
  options,
  lableClass,
  className,
  optionsClass,
  mainClass,
  optionClass,
}: Props) {
  const methods = useFormContext();

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem className={`${mainClass}`}>
          {label && <FormLabel className={`${lableClass}`}>{label}</FormLabel>}
          <FormControl>
            <RadioGroup
              onValueChange={field.onChange}
              defaultValue={field.value}
              className={`flex flex-col space-y-1 ${optionsClass}`}
            >
              {options.map(({ label, value }, idx) => {
                return (
                  <FormItem
                    key={idx}
                    className='flex flex-row items-center space-x-3 space-y-0'
                  >
                    <FormControl>
                      <RadioGroupItem value={value} className={className} />
                    </FormControl>
                    <FormLabel className={cn('font-normal', optionClass)}>
                      {label}
                    </FormLabel>
                  </FormItem>
                );
              })}
            </RadioGroup>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
