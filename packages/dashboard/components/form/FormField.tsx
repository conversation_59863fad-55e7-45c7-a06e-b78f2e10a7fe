'use client';
import { useFormContext } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField as FF,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Form } from '../../../shared/types/Form';

type Props = Form &
  React.InputHTMLAttributes<HTMLInputElement> & { s3FilePath?: string };

export async function uploadFile(
  url: string,
  file: File,
  filePath: string
): Promise<{ filePath: string }> {
  const myHeaders = new Headers();
  myHeaders.append('Connection', 'keep-alive');
  myHeaders.append('Sec-Fetch-Dest', 'empty');
  myHeaders.append('Sec-Fetch-Mode', 'cors');
  myHeaders.append('Sec-Fetch-Site', 'same-site');

  const formdata = new FormData();
  formdata.append('file', file, file.name);
  formdata.append('s3FilePath', filePath);

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_ENDPOINT}/file-upload`,
    {
      method: 'POST',
      headers: myHeaders,
      body: formdata,
      redirect: 'follow',
    }
  );

  if (response.status === 500) {
    // @ts-ignore
    return toast.error('Only images are allowed');
  }

  return await response.json();
}

export default function FormField({
  name,
  label,
  description,
  s3FilePath,
  ...otherProps
}: Props) {
  const methods = useFormContext();

  return (
    <FF
      control={methods.control}
      name={name}
      render={({ field: { value, ...rest } }) => {
        return (
          <FormItem>
            <div className='items-center flex'>
              {label && <FormLabel>{label}</FormLabel>}
            </div>
            <FormControl>
              <Input
                {...rest}
                {...otherProps}
                {...(otherProps.type !== 'file' ? { value } : {})}
                {...(otherProps.type === 'file' && {
                  onChange: async (event) => {
                    const file = event.target.files?.[0];
                    if (!file) return;

                    try {
                      const { filePath } = await uploadFile(
                        '/communication/upload-file',
                        file,
                        s3FilePath ?? ''
                      );
                      methods.setValue(name, filePath);
                    } catch (error) {
                      console.error('File upload failed', error);
                    }
                  },
                })}
              />
            </FormControl>
            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
