import { Question } from '../../../shared/types/Question';
import { FormSelect } from './FormSelect';
import { FormRadioGroup } from './FormRadioGroup';
import { FormCheckBoxGroup } from './FormCheckboxGroup';
import { FormSwitch } from './FormSwitch';
import { RouterOutput } from '../../../shared';
import { FormComboBoxPopover } from './FormComboPopover';
import { Input } from '../ui/input';
import { File } from 'lucide-react';
import { FormTextField } from './FormTextField';

export type Props = {
  question: Question & { _id: string };
  isCompliantQuestion?: boolean;
};

export default function FormBuilder({ question, isCompliantQuestion }: Props) {
  const { inputType, label, placeholder } = question.input;

  let input: JSX.Element;
  let fileName = `file.${String(question._id)}`;
  // if (isCompliantQuestion) return null;

  switch (inputType) {
    case 'text':
      input = (
        <FormTextField
          disabled
          name={String(question._id)}
          label={label}
          placeholder={placeholder}
          className='w-[50vw] h-[30vh] text-lg'
          lableClass='text-xl'
        />
      );
      break;

    case 'radio':
      input = (
        <div>
          <FormRadioGroup
            name={String(question._id)}
            label={label}
            placeholder={placeholder}
            options={question.input.options.map((o) => ({
              label: o,
              value: o,
            }))}
            className='bg-white border-neutral-600 text-black'
            optionsClass='flex-row'
            lableClass='mt-3.5 text-xl '
            optionClass='text-lg'
          />
        </div>
      );

      break;

    case 'checkbox':
      input = (
        <div>
          <FormCheckBoxGroup
            name={String(question._id)}
            label={label}
            placeholder={placeholder}
            options={question.input.options.map((o) => ({
              label: o,
              value: o,
            }))}
            lableClass='mt-3.5 text-xl'
            optionClass='text-lg'
          />
        </div>
      );
      break;

    case 'select':
      input = (
        <FormSelect
          disabled={!isCompliantQuestion}
          name={String(question._id)}
          label={label}
          placeholder={placeholder}
          options={question.input.options.map((o) => ({
            label: o,
            value: o,
          }))}
          lableClass='mt-3.5 text-lg'
          selectClass='text-lg '
        />
      );
      break;

    case 'switch':
      input = (
        <div className='text-xl'>
          <FormSwitch
            name={String(question._id)}
            label={label}
            placeholder={placeholder}
          />
        </div>
      );
      break;
  }

  return <div className=' p-4 '>{input}</div>;
}
