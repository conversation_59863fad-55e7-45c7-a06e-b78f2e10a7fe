import React, { useRef, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Button } from '../ui/button';
import { UploadCloud } from 'lucide-react';
import { Input } from '../ui/input';
import { FileExtensions, MimeTypes } from '../../../shared/types/File';
import toast from 'react-hot-toast';
import { toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';

export default function FormFilePicker({
  name,
  fileName,
}: {
  name: string;
  fileName: string;
}) {
  // File State

  const methods = useFormContext();

  const fileInputRef = useRef<HTMLInputElement>(null);
  const resetFile = () => {
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const createFile = trpc.file.createFile.useMutation();

  return (
    <Controller
      name={name}
      render={({ field: { onChange } }) => {
        const handleFileUpload = (
          base64: string,
          ext: FileExtensions,
          mimeType: MimeTypes
        ) => {
          if (ext && mimeType) {
            toastPromise({
              asyncFunc: createFile.mutateAsync({
                base64,
                ext,
                mimeType,
                name: fileName,
              }),
              success: 'File Selected Sucessfully',
              onSuccess(data) {
                onChange(data._id);
              },
            });
          }
        };
        const handleFileChange = async (
          event: React.ChangeEvent<HTMLInputElement>
        ) => {
          const file = event.target.files?.[0];
          if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
              const base64 = reader.result as string;
              const ext = file.name.split('.').pop() as FileExtensions;
              const mimeType = file.type as MimeTypes;
              const fileSize = Number((file.size / (1024 * 1024)).toFixed(2));

              //   File size
              const maxFileSizeInMb = 10;
              if (fileSize > maxFileSizeInMb) {
                resetFile();
                return toast.error(
                  `file size must be less than ${maxFileSizeInMb}MB`
                );
              }

              handleFileUpload(base64, ext, mimeType);
            };
            reader.readAsDataURL(file);
          }
        };

        return (
          <div className='space-y-4 flex flex-row items-center justify-between gap-2'>
            <div>
              <Input
                ref={fileInputRef}
                type='file'
                onChange={handleFileChange}
                accept=' .jpeg, .jpg, .png, .pdf, .zip, .x-zip-compressed, .vsdx '
              />
              {methods.formState?.errors?.[name] && (
                <p className='text-red-500 font-medium'>
                  {methods.formState?.errors?.[name]?.message as string}
                </p>
              )}
            </div>
            {/* <Button type='button' onClick={handleFileUpload}>
              <UploadCloud className='size-4 mr-2' />
              Save File
            </Button> */}
          </div>
        );
      }}
    />
  );
}
