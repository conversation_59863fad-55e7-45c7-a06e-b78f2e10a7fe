'use client';

import { useEditor, EditorContent, EditorOptions, Editor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Toolbar from './toolbar';
import { ScrollArea } from '@/components/ui/scroll-area';

type UseRichTextEditor = {
  editorOptions?: Partial<EditorOptions> | undefined;
  onChange?: (html: string) => void;
};

export const useRichTextEditor = (options?: UseRichTextEditor) => {
  return useEditor({
    extensions: [StarterKit, Underline],
    editorProps: {
      attributes: {
        class:
          'border-b border-r border-l border-muted-foreground text-muted-foreground rounded-bl-md rounded-br-md outline-none h-[250px]',
      },
    },
    onUpdate: ({ editor }) => {
      options?.onChange?.(editor.getHTML());
    },
    ...options?.editorOptions,
  });
};

type Props = {
  content?: string;
  onChange?: (html: string) => void;
  editor: Editor | null;
};

const Tiptap = ({
  content = '<p>Start writing here! 😷</p>',
  editor,
}: Props) => {
  return (
    <div>
      <Toolbar editor={editor} content={content} />
      <EditorContent
        style={{ whiteSpace: 'pre-line' }}
        editor={editor}
        className='max-h-[300px] overflow-y-auto'
      />
    </div>
  );
};

export default Tiptap;
