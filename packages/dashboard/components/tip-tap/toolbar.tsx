'use client';

import React from 'react';
import { type Editor } from '@tiptap/react';
import {
  Bold,
  Strikethrough,
  Italic,
  List,
  ListOrdered,
  Heading2,
  Underline,
  Quote,
  Undo,
  Redo,
  Code,
} from 'lucide-react';

type Props = {
  editor: Editor | null;
  content: string;
};

const Toolbar = ({ editor, content }: Props) => {
  if (!editor) {
    return null;
  }
  return (
    <div
      className='px-4 py-3 rounded-tl-md rounded-tr-md flex justify-between items-start
    gap-5 w-full flex-wrap border border-muted-foreground'
    >
      <div className='flex justify-start items-center gap-5 w-full lg:w-10/12 flex-wrap '>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleBold().run();
          }}
          className={
            editor.isActive('bold')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground'
          }
        >
          <Bold className='size-5' />
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleItalic().run();
          }}
          className={
            editor.isActive('italic')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground'
          }
        >
          <Italic className='size-5' />
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleUnderline().run();
          }}
          className={
            editor.isActive('underline')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground'
          }
        >
          <Underline className='size-5' />
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleStrike().run();
          }}
          className={
            editor.isActive('strike')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground'
          }
        >
          <Strikethrough className='size-5' />
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleHeading({ level: 2 }).run();
          }}
          className={
            editor.isActive('heading', { level: 2 })
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground'
          }
        >
          <Heading2 className='size-5' />
        </button>

        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleBulletList().run();
          }}
          className={
            editor.isActive('bulletList')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground'
          }
        >
          <List className='size-5' />
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleOrderedList().run();
          }}
          className={
            editor.isActive('orderedList')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground'
          }
        >
          <ListOrdered className='size-5' />
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().toggleBlockquote().run();
          }}
          className={
            editor.isActive('blockquote')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground'
          }
        >
          <Quote className='size-5' />
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().setCode().run();
          }}
          className={
            editor.isActive('code')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground'
          }
        >
          <Code className='size-5' />
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().undo().run();
          }}
          className={
            editor.isActive('undo')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground hover:bg-muted-foreground hover:text-muted p-1 hover:rounded-lg'
          }
        >
          <Undo className='size-5' />
        </button>
        <button
          onClick={(e) => {
            e.preventDefault();
            editor.chain().focus().redo().run();
          }}
          className={
            editor.isActive('redo')
              ? 'bg-muted-foreground text-muted p-2 rounded-lg'
              : 'text-muted-foreground hover:bg-muted-foreground hover:text-muted p-1 hover:rounded-lg'
          }
        >
          <Redo className='size-5' />
        </button>
      </div>
    </div>
  );
};

export default Toolbar;
