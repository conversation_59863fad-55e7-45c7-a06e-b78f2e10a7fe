'use client';

import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '../ui/badge';
import { Label } from '../ui/label';
import { Plus, X } from 'lucide-react';

export type ComboBoxOption = {
  value: string;
  label: string;
  icon?: JSX.Element; //TODO: work on icon
};

export type ComboPopoverProps =
  | {
      label: string;
      placeholder?: string;
      options: ComboBoxOption[];
      onValueSelect?: (data?: string) => void;
      onChipSelect?: (data?: string) => void;
      allowCustomValues?: boolean;
    } & (
      | {
          multiple?: true;
          value?: string[];
          onChange?: (data: string[]) => void;
        }
      | {
          multiple?: boolean;
          value?: string;
          onChange?: (data: string) => void;
        }
    );

export function ComboboxPopover({
  label,
  placeholder,
  options,
  value,
  onChange,
  onValueSelect,
  onChipSelect,
  multiple = true,
  allowCustomValues,
}: ComboPopoverProps) {
  const [open, setOpen] = React.useState(false);
  const [optionsState, setOptionsState] = React.useState(options);
  const [searchTerm, setSearchTerm] = React.useState('');

  React.useEffect(() => {
    setOptionsState(options);
  }, [options]);

  const _value = React.useMemo(
    () => (typeof value === 'string' ? [value] : value || []),
    [value]
  );

  const handleValue = React.useCallback(
    (v?: string) => {
      onChipSelect?.(v);
      setSearchTerm('');
      onValueSelect?.(v);

      const vExists = _value.some((_) => _ === v);

      if (allowCustomValues && !vExists) {
        // @ts-ignore
        setOptionsState((_) => {
          if (!_.some((_) => _.value === v)) {
            return [..._, { label: v, value: v }];
          }
          return [..._];
        });
      }

      if (multiple) {
        // @ts-ignore
        if (vExists) return onChange?.((_value || []).filter((_) => _ !== v));

        // @ts-ignore
        onChange?.([..._value, v]);
        // @ts-ignore
      } else {
        // @ts-ignore
        if (v === value) return onChange?.('');
        // @ts-ignore
        onChange?.(v);
      }
    },
    [
      _value,
      allowCustomValues,
      multiple,
      onChange,
      onChipSelect,
      onValueSelect,
      value,
    ]
  );

  return (
    <div className='w-full'>
      <Label>{label}</Label>

      <div className='border rounded-lg p-4'>
        {!!value?.length && (
          <div className='flex flex-wrap gap-1'>
            {(typeof value === 'string' ? [value] : value).map((v) => {
              return (
                <Badge
                  className='cursor-pointer'
                  key={v}
                  onClick={() => handleValue(v)}
                >
                  {
                    optionsState.find((_) => {
                      return _.value === v;
                    })?.label
                  }
                  <X className='w-4 h-4 ml-1' />
                </Badge>
              );
            })}
          </div>
        )}
        <div>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                type='button'
                variant='outline'
                size='sm'
                className='w-full justify-start mt-4'
              >
                + Select
              </Button>
            </PopoverTrigger>
            <PopoverContent className='p-0' side='right' align='start'>
              <Command>
                <CommandInput
                  placeholder={placeholder}
                  onValueChange={setSearchTerm}
                  value={searchTerm}
                />
                <CommandList>
                  <CommandEmpty className='p-4 space-y-2'>
                    {allowCustomValues && (
                      <Button
                        type='button'
                        onClick={() => handleValue(searchTerm)}
                      >
                        {searchTerm}
                        <Plus className='w-5 h-5 ml-2' />
                      </Button>
                    )}
                    <p>No results found.</p>
                  </CommandEmpty>
                  <CommandGroup>
                    {allowCustomValues &&
                      optionsState?.every((_) => {
                        return _.label !== searchTerm;
                      }) &&
                      searchTerm && (
                        <div className='pl-1 mb-2'>
                          <Button type='button'>
                            {searchTerm}
                            <Plus className='w-5 h-5 ml-2' />
                          </Button>
                        </div>
                      )}
                    {optionsState.map((option) => {
                      // if (option?.value === undefined) return null;
                      return (
                        <CommandItem
                          key={option.value}
                          value={option.label}
                          onSelect={() => handleValue(option.value)}
                        >
                          {option.icon}
                          <span>{option.label}</span>
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );
}
