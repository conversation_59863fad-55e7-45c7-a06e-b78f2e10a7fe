import { X } from 'lucide-react';
import { Badge } from './badge';
import { Input, InputProps } from './input';
import _ from 'lodash';
import { KeyboardEventHandler } from 'react';

export type ChipOption = { label: string; value: string };

function Chip({
  chip,
  onClick,
}: {
  chip: ChipOption;
  onClick?: (val: string) => void;
}) {
  return (
    <Badge className='cursor-pointer' onClick={() => onClick?.(chip.value)}>
      {chip.label}
    </Badge>
  );
}

export type ChipInputProps = InputProps & {
  chips?: ChipOption[];
  onChipAction: (val: string) => void;
  onChipSelect: (val: string) => void;
};
export default function ChipInput({
  chips,
  onChipAction,
  onChipSelect,
  ...rest
}: ChipInputProps) {
  // @ts-ignore
  const uniqueChips = _.uniqBy(chips, 'value');

  const handleChipActions: KeyboardEventHandler<HTMLInputElement> = (e) => {
    const actionKeys = ['Tab', 'Enter'];

    if (rest.value && actionKeys.includes(e.key)) {
      e.preventDefault();
      typeof rest.value === 'string' && onChipAction(rest.value);
    }
  };

  return (
    <div>
      <div className='mb-3 flex-wrap flex gap-2 w-full'>
        {uniqueChips.map((chip) => (
          <Chip key={chip.value} chip={chip} onClick={onChipSelect} />
        ))}
      </div>
      <Input {...rest} onKeyDown={handleChipActions} />
    </div>
  );
}
