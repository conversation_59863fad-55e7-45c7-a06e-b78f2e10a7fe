import { RouterOutput } from '../../shared';
import { Question, TextInput } from '../../shared/types/Question';
import * as z from 'zod';

const zodFieldBuilder = (question: Question) => {
  let validator: z.ZodSchema;
  const { inputType, schema } = question.input;

  if (inputType === 'text') {
    const { max, min, required } = schema;
    validator = z.string();
    if (max?.val) {
      validator = (validator as z.ZodString).max(max.val, max.message);
    }
    if (min?.val) {
      validator = (validator as z.ZodString).min(min.val, min.message);
    }
    if (!required) validator = (validator as z.ZodString).optional();

    return validator;
  }

  if (inputType === 'select') {
    const { required } = schema;
    validator = z.string();
    if (!required) validator = (validator as z.ZodString).optional();

    return validator;
  }

  if (inputType === 'radio') {
    const { required } = schema;
    validator = z.string();
    if (!required) validator = (validator as z.ZodString).optional();

    return validator;
  }

  if (inputType === 'checkbox') {
    const { required, max, min } = schema;
    validator = z.array(z.string());
    if (max?.val) {
      validator = (validator as z.ZodString).max(max.val, max.message);
    }
    if (min?.val) {
      validator = (validator as z.ZodString).min(min.val, min.message);
    }
    if (!required) validator = (validator as z.ZodString).optional();

    return validator;
  }

  //   if (inputType === 'switch') {
  //   }
};

export const zodSchemaBuilder = (
  questions?: RouterOutput['questions']['getQuestions']
) => {
  const objSchema: { [key: string]: z.ZodSchema } = {};
  questions?.forEach((q) => {
    // @ts-ignore
    if (zodFieldBuilder(q)) objSchema[String(q._id)] = zodFieldBuilder(q);
    // add support for files
  });
  Object.keys(objSchema).forEach(
    (key) => (objSchema[`file.${key}`] = z.array(z.string()).optional())
  );

  return z.object(objSchema);
};
