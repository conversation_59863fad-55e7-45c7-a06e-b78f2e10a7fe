import { SECTIONTYPE } from '../../shared/types/Standard';
import { RouterOutput } from '../../shared';
import { AuditorScore } from '../../shared/types/AssessmentSubmission';

export const getTotalScorableGeneralSubmissions = (
  submissions: RouterOutput['assessment']['getAssessmentSubmissions']
) => {
  return submissions.reduce((a, b) => {
    if (b.sectionType === SECTIONTYPE.INVENTORY) return a + 0;
    if (b.submission.answer) return a + 1;
    return a + 0;
  }, 0);
};

export const getTotalGeneralQuestionScore = (
  submissions: RouterOutput['assessment']['getAssessmentSubmissions']
) => {
  const totalAnsweredQuestions =
    getTotalScorableGeneralSubmissions(submissions);

  const totalScore = submissions.reduce((a, b) => {
    if (b.sectionType === SECTIONTYPE.INVENTORY) return a + 0;
    if (
      b.submission.question?.input.inputType === 'switch' &&
      b.submission.answer === '1'
    )
      return a + 1;

    if (b.audit?.score === AuditorScore?.ACCEPT) return a + 1;

    return a + 0;
  }, 0);

  return {
    totalScore,
    totalAnsweredQuestions,
    scorePercent: (totalScore / totalAnsweredQuestions) * 100,
  };
};
