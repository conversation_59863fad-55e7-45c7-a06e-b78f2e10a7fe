import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { toast } from 'react-hot-toast';
import { TRPCClientError } from '@trpc/client';

interface ToastPromiseParams {
  asyncFunc: Promise<unknown>;
  success?: string;
  error?: string;
  loading?: string;
  onSuccess?: (data: any) => void;
  onError?: (err: any) => void;
}
export function toastPromise<T>({
  asyncFunc,
  error = 'Something went wrong...',
  success = 'Success',
  loading = 'Loading...',
  onError,
  onSuccess,
}: ToastPromiseParams) {
  return toast.promise(asyncFunc, {
    loading,
    success: (data) => {
      onSuccess?.(data);
      return success;
    },
    error: (err) => {
      onError?.(err);
      if (err instanceof TRPCClientError) return err.message;
      else return error;
    },
  }) as T;
}

export function getDayHoursMins() {
  const timeOfDay = ['AM', 'PM'];
  const hours = new Array(12).fill(0).map((_, idx) => idx + 1);
  const mins = [];

  for (let i = 0; i < 60; i = i + 5) mins.push(i);

  return { timeOfDay, hours, mins };
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function nameToInitials(name: string) {
  const [firstName, lastname] = name?.split(' ') || 'PM';
  return `${firstName[0]}${lastname ? lastname[0] : firstName[1]}`;
}

export const selectOptionsParser = (
  input: any[] | undefined,
  { labelKey, valKey }: { labelKey: string; valKey: string }
): {
  value: string | number;
  label: string;
}[] => {
  return (input || []).map((_: any) => ({
    label: _[labelKey] || '',
    value: _[valKey] || '',
  }));
};

export const downloadBase64File = (
  base64: string,
  filename: string,
  mimeType: string
) => {
  // Convert base64 to binary
  const binaryString = atob(base64.split(',')[1]);

  // Create a Uint8Array from the binary string
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // Create a Blob from the Uint8Array
  const blob = new Blob([bytes], { type: mimeType });

  // Create a URL for the Blob
  const url = URL.createObjectURL(blob);

  // Create a link element
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;

  // Append the link to the body
  document.body.appendChild(link);

  // Click the link to initiate the download
  link.click();

  // Cleanup: remove the link and revoke the URL
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export function getUniqueGradient(index: number) {
  const colors = [
    ['from-red-400', 'to-yellow-400'],
    ['from-blue-400', 'to-purple-400'],
    ['from-green-400', 'to-teal-400'],
    ['from-pink-400', 'to-red-400'],
    ['from-indigo-400', 'to-blue-400'],
    ['from-orange-400', 'to-pink-400'],
    ['from-lime-400', 'to-emerald-400'],
    ['from-cyan-400', 'to-sky-400'],
    ['from-fuchsia-400', 'to-rose-400'],
    ['from-violet-400', 'to-indigo-400'],
    ['to-red-400', 'from-yellow-400'],
    ['to-blue-400', 'from-purple-400'],
    ['to-green-400', 'from-teal-400'],
    ['to-pink-400', 'from-red-400'],
    ['to-indigo-400', 'from-blue-400'],
    ['to-orange-400', 'from-pink-400'],
    ['to-lime-400', 'from-emerald-400'],
    ['to-cyan-400', 'from-sky-400'],
    ['to-fuchsia-400', 'from-rose-400'],
    ['to-violet-400', 'from-indigo-400'],
  ];

  // Generate a unique index by cycling through the colors array
  const colorIndex = index % colors.length;

  return `bg-gradient-to-r ${colors[colorIndex][0]} ${colors[colorIndex][1]} `;
}

export const toS3ImageUrl = (filePath: string) =>
  `${process.env.NEXT_PUBLIC_S3_BUCKET_BASE_URL}${filePath}`;
