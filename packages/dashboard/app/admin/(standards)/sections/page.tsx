'use client';
import { trpc } from '@/providers/Providers';
import { CreateUpdateSectionDialog } from '@/elements/section/create-update-section-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { StringParam, useQueryParam, withDefault } from 'use-query-params';
import { Label } from '@/components/ui/label';

export default function Sections() {
  const [standard, setStandard] = useQueryParam(
    'companyDialog',
    withDefault(StringParam, '')
  );

  const standards = trpc.standard.getStandards.useQuery();
  const sections = trpc.standard.getSections.useQuery({ standard });

  return (
    <div className='ph container'>
      {/* Filter */}
      <div className='flex  items-center mb-3'>
        <div>
          <Label className='font-semibold text-white'>Select Standard: </Label>
          <Select onValueChange={setStandard}>
            <SelectTrigger className='max-w-[450px]  border-2 border-neutral-400'>
              <SelectValue placeholder='Select Standard' />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Standards</SelectLabel>
                {standards.data?.map((standard) => {
                  return (
                    <SelectItem
                      key={String(standard._id)}
                      value={String(standard._id)}
                    >
                      <span className='line-clamp-1'> {standard.standard}</span>
                    </SelectItem>
                  );
                })}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        {/* Create Section */}
        <div className='mt-5 mx-4'>
          <CreateUpdateSectionDialog />
        </div>
      </div>

      {/* Data */}
      <Table>
        <TableHeader>
          <TableRow className='text-sm   font-semibold'>
            <TableHead>Section Type</TableHead>
            <TableHead>Section</TableHead>
            <TableHead>Section Tag</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {sections.data?.map((item) => {
            const {
              _id,
              description,
              section,
              standard,
              sectionType,
              sectionLable,
            } = item;

            return (
              <TableRow key={String(_id)}>
                <TableCell> {sectionType.toUpperCase()}</TableCell>
                <TableCell> {section}</TableCell>
                <TableCell>{sectionLable}</TableCell>
                <TableCell>{description}</TableCell>
                <TableCell>
                  <CreateUpdateSectionDialog
                    update={{
                      id: String(_id),
                      description,
                      section,
                      standard: standard?.map(String),
                      sectionType,
                      sectionLable,
                    }}
                  />
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
