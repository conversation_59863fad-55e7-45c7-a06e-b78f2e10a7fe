'use client';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import AssessmentDataGrid from '@/elements/assessments/assessment.datagrid';
import CreateUpdateAssessmentForm from '@/elements/assessments/create-update-assessment.form';
import { manageAssessmentValidator } from '../../../../../shared/validators/assessment.validator';
import { StringParam, useQueryParam, withDefault } from 'use-query-params';
import { string, z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ASSIGNMENT_STATUS } from '../../../../../shared/types/Assesment';
import { trpc } from '@/providers/Providers';
import { RouterOutput } from '../../../../../shared';
import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogContent,
  AlertD<PERSON>og<PERSON><PERSON><PERSON>,
  Al<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/alert-dialog';

type Form = z.infer<typeof manageAssessmentValidator>;

export default function Assessments() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [tab, setTab] = useQueryParam(
    'tab',
    withDefault(StringParam, 'assessments')
  );

  const assessments = trpc.assessment.getAssessments.useQuery();

  const methods = useForm<Form>({
    resolver: zodResolver(manageAssessmentValidator),
    defaultValues: {
      assessmentId: '',
      name: '',
      expiresAt: new Date(),
      vendor: '',
      company: '',
      assignments: [
        {
          employee: '',
          questions: [],
          sections: [],
          assignmentStatus: ASSIGNMENT_STATUS.PENDING,
        },
      ],
      companyEngagementManager: {
        countryCode: '',
        email: '',
        phone: '',
        firstName: '',
        lastName: '',
        title: '',
      },

      vendorAssessmentManager: {
        countryCode: '',
        email: '',
        firstName: '',
        phone: '',
        lastName: '',
        title: '',
      },
      sections: [],
    },
  });

  const handleUpdateAssessment = (
    assessment: RouterOutput['assessment']['getAssessments'][number]
  ) => {
    methods.setValue('assessmentId', String(assessment._id));
    methods.setValue('company', String(assessment.company?._id));
    methods.setValue('vendor', String(assessment.vendor?._id));
    methods.setValue('name', assessment.name);
    methods.setValue('expiresAt', assessment.expiresAt);
    methods.setValue(
      'companyEngagementManager',
      assessment.companyEngagementManager
    );
    methods.setValue(
      'vendorAssessmentManager',
      assessment.vendorAssessmentManager
    );

    methods.setValue(
      'sections',
      (assessment.sections || []).map((_) => _._id)
    );

    methods.setValue(
      'assignments',
      assessment.assignments.map((ass) => ({
        employee: String(ass.employee._id),
        questions: (ass.questions || []).map(String),
        sections: ass.sections.map((section) => String(section._id)),
      }))
    );
    setDialogOpen(true);
  };

  return (
    <div className='container1'>
      <AlertDialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <AlertDialogContent className='min-w-[95vw] bg-cyan-950'>
          <AlertDialogHeader>
            <AlertDialogTitle className='text-white text-3xl text-center border-b-2 rounded-lg'>
              Update Assessment
            </AlertDialogTitle>
          </AlertDialogHeader>
          <CreateUpdateAssessmentForm methods={methods} isUpdate />
        </AlertDialogContent>
      </AlertDialog>
      <Tabs defaultValue='assessments' onValueChange={setTab} value={tab}>
        <TabsList>
          <TabsTrigger value='assessments'>Assessments</TabsTrigger>
          <TabsTrigger value='manage'>Create Assessment </TabsTrigger>
        </TabsList>
        <TabsContent value='assessments'>
          <AssessmentDataGrid
            assessments={assessments.data || []}
            handleUpdate={handleUpdateAssessment}
          />
        </TabsContent>
        <TabsContent value='manage'>
          <CreateUpdateAssessmentForm methods={methods} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
