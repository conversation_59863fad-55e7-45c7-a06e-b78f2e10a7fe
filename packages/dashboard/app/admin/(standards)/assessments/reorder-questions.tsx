'use client';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ManageAssessmentForm } from '@/elements/assessments/create-update-assessment.form';
import { Reorder } from 'framer-motion';
import _ from 'lodash';
import { RouterOutput } from '../../../../../shared';
import { UseFieldArrayReturn, UseFormReturn } from 'react-hook-form';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

export function ReorderAssessmentQuestions({
  methods,
  fieldArray: { fields },
  questions: questionsData,
  sections: sectionsData,
}: {
  fieldArray: UseFieldArrayReturn<ManageAssessmentForm, 'assignments'>;
  sections: RouterOutput['standard']['getSections'];
  methods: UseFormReturn<ManageAssessmentForm>;
  questions: RouterOutput['questions']['getQuestions'];
}) {
  return fields.map((field, idx) => {
    const questions = methods.watch(`assignments.${idx}.questions`);
    const sections = methods.watch(`assignments.${idx}.sections`);

    return (
      <div key={field.id}>
        <Reorder.Group
          axis='y'
          values={sections}
          className=''
          onReorder={(v) => {
            methods.setValue(`assignments.${idx}.sections`, v);
          }}
        >
          <Label className='mb-2 text-xl font-semibold text-white'>
            Sections:
          </Label>
          <Separator className='mt-4 mb-2' />
          {sections.map((section, sectionIdx) => {
            const sectionObject = sectionsData.find(
              (q) => String(q._id) === section
            );

            return (
              <Reorder.Item
                value={section}
                key={section}
                className='border mt-4 p-4 rounded-lg bg-white'
                style={{ y: 10 }}
              >
                <Accordion type='single' collapsible>
                  <AccordionItem value={`${sectionObject?.section}`}>
                    <AccordionTrigger>
                      <div className='flex flex-row items-center justify-between gap-2  '>
                        <div className='flex gap-2'>
                          <span>{sectionIdx + 1}.</span>
                          {sectionObject?.section}
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <Reorder.Group
                        axis='y'
                        values={questions}
                        className='mt-2'
                        onReorder={(v) => {
                          methods.setValue(`assignments.${idx}.questions`, v);
                        }}
                      >
                        <Label className='mt-2 text-xl text-white font-semibold'>
                          Questions:
                        </Label>
                        <Separator className='mt-4 mb-2' />
                        {questions.map((question, questionIdx) => {
                          const questionObject = questionsData.find(
                            (q) => String(q._id) === question
                          );
                          return (
                            <Reorder.Item
                              value={question}
                              key={question}
                              className='border mt-4 p-4 rounded-lg bg-white'
                              style={{ y: 10 }}
                            >
                              <div className='space-y-2'>
                                <div className='underline font-semibold'>
                                  <span>Section: </span>
                                  {questionObject?.section.section}
                                </div>
                                <div className='flex flex-row items-center justify-between gap-2  '>
                                  <div className='flex gap-2'>
                                    <span>{questionIdx + 1}.</span>
                                    {questionObject?.input.label}-
                                  </div>
                                </div>
                              </div>
                            </Reorder.Item>
                          );
                        })}
                      </Reorder.Group>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </Reorder.Item>
            );
          })}
        </Reorder.Group>
      </div>
    );
  });
}
