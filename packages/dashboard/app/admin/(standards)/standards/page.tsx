'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import CreateUpdateStandardDialog from '@/elements/standard/create-update-standard-dialog';
import { trpc } from '@/providers/Providers';

export default function Standards() {
  const standards = trpc.standard.getStandards.useQuery();

  return (
    <div className='ph container'>
      <div className=' mr-4 mb-5'>
        <CreateUpdateStandardDialog />
      </div>

      <Table>
        <TableHeader>
          <TableRow className='text-sm   font-semibold'>
            <TableCell>Standard</TableCell>
            <TableCell>Description</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHeader>

        <TableBody>
          {standards.data?.map(({ _id, description, standard }) => {
            return (
              <TableRow key={String(_id)}>
                <TableCell>{standard}</TableCell>
                <TableCell>{description}</TableCell>
                <TableCell>
                  <CreateUpdateStandardDialog
                    //@ts-ignore
                    update={{ standard, description, id: String(_id) }}
                  />
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
