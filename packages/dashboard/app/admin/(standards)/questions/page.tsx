'use client';
import ManageQuestionForm from '@/elements/question/manage-question.form';
import QuestionsDataGrid from '@/elements/question/questions.datagrid';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';

import { StringParam, useQueryParam, withDefault } from 'use-query-params';
import { useManageQuestionMethods } from '@/hooks/forms/questions';
import { RouterOutput } from '../../../../../shared';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useState } from 'react';

export default function Question() {
  const [open, setOpen] = useState(false);
  const [tab, setTab] = useQueryParam(
    'tab',
    withDefault(StringParam, 'create question')
  );

  const methods = useManageQuestionMethods();

  const onQuestionSelect = ({
    _id,
    input,
    section,
    standard,
    subSection,
    weightage,
    canAttachDocument,
  }: RouterOutput['questions']['getQuestions'][number]) => {
    const { inputType, label, schema, placeholder } = input;
    methods.setValue('questionId', String(_id));
    methods.setValue('section', String(section._id));
    methods.setValue('standard', String(standard));
    methods.setValue('subSection', String(subSection));
    methods.setValue('weightage', weightage);
    methods.setValue('canAttachDocument', canAttachDocument);

    // @ts-ignore
    const inputValues = {
      inputType,
      label,
      schema,
      placeholder,
      // @ts-ignore
      schema: { max: schema.max, min: schema.min, required: schema.required },
    };
    // @ts-ignore
    if (input.options) inputValues.options = input.options;
    // @ts-ignore
    methods.setValue('input', inputValues);

    setOpen(true);
  };

  return (
    <div className='px-10'>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Manage Question</DialogTitle>
          </DialogHeader>
          <div>
            <ManageQuestionForm methods={methods} />
          </div>
        </DialogContent>
      </Dialog>
      <Tabs defaultValue='create question' onValueChange={setTab} value={tab}>
        <TabsList className='bg-cyan-500 text-black '>
          <TabsTrigger value='create question'>Create Question</TabsTrigger>
          <TabsTrigger value='questions'>Update Questions</TabsTrigger>
        </TabsList>
        <TabsContent value='create question' className=' container'>
          <ManageQuestionForm methods={methods} />
        </TabsContent>
        <TabsContent value='questions'>
          <ScrollArea className='h-[700px] w-full rounded-md  p-4'>
            <QuestionsDataGrid onClick={onQuestionSelect} />
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
}
