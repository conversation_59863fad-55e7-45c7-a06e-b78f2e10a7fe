'use client';
import { trpc } from '@/providers/Providers';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { CreateUpdateSubSectionDialog } from '@/elements/sub-section/create-update-subSection-dialog';
import { StringParam, useQueryParam } from 'use-query-params';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function SubSections() {
  // get standards
  // get sections

  const [section, setSection] = useQueryParam('companyDialog', StringParam);

  const sections = trpc.standard.getSections.useQuery();
  const subSections = trpc.standard.getSubSections.useQuery(
    { section: section as string },
    { enabled: !!section }
  );

  return (
    <div className='ph container'>
      {/* Filter*/}
      <div className='flex items-center mb-3'>
        <div>
          <Label>Select Section: </Label>
          <Select onValueChange={setSection}>
            <SelectTrigger className='max-w-[450px]'>
              <SelectValue placeholder='Select Section' />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Sections</SelectLabel>
                {sections.data?.map((section) => {
                  return (
                    <SelectItem
                      key={String(section._id)}
                      value={String(section._id)}
                    >
                      <span className='line-clamp-1'> {section.section}</span>
                    </SelectItem>
                  );
                })}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        {/* Create Subsectio  */}
        <div className=' mt-5 mx-4'>
          <CreateUpdateSubSectionDialog defaultSection={section as string} />
        </div>
      </div>

      {/* Data */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableCell>Sub-Section</TableCell>
            <TableCell>Description</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHeader>

        <TableBody>
          {subSections.data?.map(
            ({ _id, description, section, subSection }) => {
              return (
                <TableRow key={String(_id)}>
                  <TableCell>{subSection}</TableCell>
                  <TableCell>{description}</TableCell>
                  <TableCell>
                    <CreateUpdateSubSectionDialog
                      update={{
                        id: String(_id),
                        description,
                        subSection,
                        section: String(section),
                      }}
                    />
                  </TableCell>
                </TableRow>
              );
            }
          )}
        </TableBody>
      </Table>
    </div>
  );
}
