'use client';
import ManageUserForm from '@/elements/user/manage-user.form';
import UserDatagrid from '@/elements/user/user.datagrid';
import { zodResolver } from '@hookform/resolvers/zod';
import { CircleUserRound, HardDriveDownload, List } from 'lucide-react';
import { USER_ROLE } from '../../../../shared/types/User';
import { manageUserValidator } from '../../../../shared/validators/user.validator';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useEffect } from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useMangeUserForm } from '@/hooks/forms/manage-user';
import { StringParam, useQueryParam, withDefault } from 'use-query-params';

type CreateForm = z.infer<typeof manageUserValidator>;

export default function UserManagement() {
  const { methods } = useMangeUserForm();
  const [tab, setTab] = useQueryParam(
    'tab',
    withDefault(StringParam, 'manageUser')
  );

  return (
    <div className='container1 ph'>
      <Tabs defaultValue='manageUser' value={tab} onValueChange={setTab}>
        <TabsList className='bg-cyan-700'>
          <TabsTrigger value='manageUser' className='text-black text-md '>
            Create User
          </TabsTrigger>
          <TabsTrigger value='datagrid' className='text-black text-md '>
            Manage User
          </TabsTrigger>
        </TabsList>
        <TabsContent value='manageUser' className='w-[70vw]'>
          <ManageUserForm methods={methods} />
        </TabsContent>
        <TabsContent value='datagrid'>
          <UserDatagrid />
        </TabsContent>
      </Tabs>
    </div>
  );
}
