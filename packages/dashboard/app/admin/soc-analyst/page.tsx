'use client';
import React from 'react';
import { trpc } from '@/providers/Providers';
import { USER_ROLE } from '../../../../shared/types/User';
import { CompanyType } from '../../../../shared/types/Company';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Pencil } from 'lucide-react';

export default function SocAnalyst() {
  const socAnalyst = trpc.user.getUsers.useQuery();

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead> Email</TableHead>
          <TableHead>First Name</TableHead>
          <TableHead>Last Name</TableHead>
          <TableHead>Country Code</TableHead>
          <TableHead>Phone</TableHead>
          <TableHead>Vendors</TableHead>
          <TableHead>Role</TableHead>
          <TableHead>Action</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {socAnalyst.data?.map((soc) => {
          if (
            soc.role === USER_ROLE.SOC_ANALYST ||
            soc.role === USER_ROLE.LEAD_SOC_ANALYST
          )
            return (
              <TableRow key={String()}>
                <TableCell>{soc.email}</TableCell>
                <TableCell>{soc.contact.firstName}</TableCell>
                <TableCell>{soc.contact.lastName}</TableCell>
                <TableCell>{soc.contact.countryCode}</TableCell>
                <TableCell>{soc.contact.phone}</TableCell>
                <TableCell>
                  {soc.companies.map((c, idx) => {
                    return <div key={idx}>{c.company}</div>;
                  })}
                </TableCell>
                <TableCell>{soc.role}</TableCell>
                <TableCell>
                  <Button>
                    <Pencil />
                  </Button>
                </TableCell>
              </TableRow>
            );
        })}
      </TableBody>
    </Table>
  );
}
