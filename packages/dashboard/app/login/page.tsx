'use client';
import <PERSON><PERSON>ield from '@/components/form/FormField';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { trpc } from '@/providers/Providers';
import { signIn, useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { loginValidator } from '../../../shared/validators/user.validator';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';
import { toastPromise } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import Image from 'next/image';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { motion } from 'framer-motion';
import Warning from '@/elements/Warning';

type Form = z.infer<typeof loginValidator>;

export default function Login() {
  const imageVariants = {
    hidden: {
      opacity: 0,
      scale: 0.5,
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
      },
    },
  };
  const router = useRouter();
  const { executeRecaptcha } = useGoogleReCaptcha();

  const methods = useForm<Form>({
    resolver: zodResolver(loginValidator),
    defaultValues: {
      email: '',
      password: '',
      recaptchaToken: '',
    },
  });

  const loginUser = trpc.user.login.useMutation();

  const login = methods.handleSubmit(async (data) => {
    // Generate ReCaptcha token
    if (!executeRecaptcha) return;
    const recaptchaToken = await executeRecaptcha('form_submit');

    toastPromise({
      asyncFunc: loginUser.mutateAsync({ ...data, recaptchaToken }),
      success: 'Authenticated Successfully',
      onSuccess: (data) => {
        signIn('credentials', {
          redirect: false,
          response: JSON.stringify(data),
        });
        methods.reset();
      },
    });
  });

  const { status } = useSession();

  useEffect(() => {
    if (status === 'authenticated') router.replace('/');
  }, [router, status]);

  if (status === 'authenticated') return null;
  return (
    <div className='flex flex-1 flex-col p-4 justify-center items-center  '>
      <FormProvider {...methods}>
        <motion.div initial='hidden' animate='visible' variants={imageVariants}>
          <Image
            src='/images/logo.png'
            alt='logo'
            height={50}
            width={250}
            className='mb-10 border-b-2 shadow-lg shadow-black p-4 rounded-3xl bg-stone-50'
          />
        </motion.div>
        <Card className='w-[550px]'>
          <CardHeader>
            <CardTitle className='self-center text-3xl '>Login</CardTitle>
            <CardDescription className='self-center'>
              Enter details to login
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form>
              <motion.div
                className='grid w-full items-center gap-4'
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <FormField
                  type='email'
                  name='email'
                  placeholder='Enter Email'
                  label='Enter Email'
                />

                <FormField
                  type='password'
                  name='password'
                  placeholder='Enter password'
                  label='Enter password'
                />
              </motion.div>
            </form>
          </CardContent>
          <CardFooter>
            <div className='flex flex-col w-full gap-4'>
              <Button disabled={loginUser.isLoading} onClick={login}>
                Login
              </Button>
            </div>
          </CardFooter>
        </Card>
      </FormProvider>
      <Warning className='' />
    </div>
  );
}
