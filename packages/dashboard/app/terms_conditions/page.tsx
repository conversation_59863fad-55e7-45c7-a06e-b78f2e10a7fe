export default function TNC() {
  return (
    <div className="container mx-auto flex flex-col gap-6">
      <h1
        style={{ color: "#000" }}
        className="text-center text-2xl text-border text-black underline my-20 font-bold"
      >
        Terms and Condtions
      </h1>
      <section>
        <h1 className="font-bold text-lg">1. Acceptance of Terms:</h1>
        <p>
          a. Users must explicitly agree to the terms and conditions by clicking
          a checkbox or taking a similar action before accessing the PM security
          app.
        </p>
        <p>
          b. By using the app, users acknowledge that they have read,
          understood, and accepted the terms and conditions.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">2. User Accounts:</h1>
        <p>
          a. Users are responsible for creating a secure password and are
          encouraged to use multifactor authentication.
        </p>
        <p>
          b. Users must promptly report any suspected unauthorized access or
          security breaches to the app administrator.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">3. Data Security:</h1>
        <p>
          a. The app employs encryption protocols to secure user data during
          transmission and storage.
        </p>
        <p>
          b. Users should not share their login credentials, and any suspicious
          activity should be reported immediately.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">4. Usage Policies:</h1>
        <p>
          a. Define prohibited activities, such as attempting to gain
          unauthorized access, disrupting the app&aposs functionality, or
          engaging in any form of hacking.
        </p>
        <p>
          b. Users are prohibited from using the app for any unlawful or
          unethical purposes.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">5. Intellectual Property:</h1>
        <p>
          a. The app developer retains all intellectual property rights,
          including copyrights and trademarks.
        </p>
        <p>
          b. Users are granted a limited, non-exclusive license to use the app,
          and any unauthorized reproduction or distribution is strictly
          prohibited.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">6. Privacy Policy:</h1>
        <p>
          a. Clearly explain the types of user data collected, including
          personal and non-personal information.
        </p>
        <p>
          b. Detail how user data is stored, who has access to it, and the
          purposes for which it will be used.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">7. Project Data:</h1>
        <p>
          a. Users maintain ownership of all project-related data entered into
          the app.
        </p>
        <p>
          b. The app only processes project data for the purpose of providing
          the specified services and does not claim ownership.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">8. Third-Party Services:</h1>
        <p>
          a. Specify any external services integrated into the app and provide
          information on how these services adhere to privacy and security
          standards.
        </p>
        <p>
          b. Users should review and agree to the terms and conditions of any
          third-party services.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">9. Updates and Modifications:</h1>
        <p>
          a. The app may undergo periodic updates to improve functionality and
          security.
        </p>
        <p>
          b. Users will be notified of significant changes through the app&aposs
          communication channels.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">10. Termination of Service:</h1>
        <p>
          a. The app developer reserves the right to terminate the service for
          any user violating the terms and conditions.
        </p>
        <p>
          b. Users may request data retrieval or export options within a
          specified timeframe after termination.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">11. Disclaimers:</h1>
        <p>
          a. The app is provided without any explicit or implied warranties,
          including fitness for a particular purpose.
        </p>
        <p>
          b. The app developer disclaims responsibility for any interruptions,
          errors, or defects in the app&aposs operation.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">12. Limitation of Liability:</h1>
        <p>
          a. The app developer&aposs liability is limited to the maximum extent
          permitted by law.
        </p>
        <p>
          b. In no event shall the app developer be liable for consequential,
          incidental, or punitive damages.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">13. Indemnification:</h1>
        <p>
          a. Users agree to indemnify and hold the app developer harmless from
          any claims, damages, or losses.
        </p>
        <p>
          b. Indemnification applies in cases of user misconduct, violation of
          terms, or legal disputes.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">14. Governing Law:</h1>
        <p>
          a. Specify the laws of the jurisdiction that will govern the
          interpretation and enforcement of the terms.
        </p>
        <p>
          b. Designate the location for dispute resolution, such as arbitration
          or mediation.
        </p>
      </section>

      <section>
        <h1 className="font-bold text-lg">15. Miscellaneous:</h1>
        <p>
          a. Include clauses addressing any unique features of the app, such as
          specific functionalities or integrations.
        </p>
        <p>
          b. Provide clear contact information for users to reach out for
          support, questions, or concerns.
        </p>
      </section>
    </div>
  );
}
