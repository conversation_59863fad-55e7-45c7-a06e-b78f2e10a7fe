'use client';

import { USER_ROLE } from '../../shared/types/User';
import SockAnalystDashboard from '@/elements/home/<USER>';
import EmployeeDashboard from '@/elements/home/<USER>';
import AuditorDashboard from '@/elements/home/<USER>';
import CompanyDashboard from '@/elements/home/<USER>';
import { useMe } from '@/hooks/useMe';
import Image from 'next/image';
import { Quote } from 'lucide-react';
import { motion } from 'framer-motion';

const content = [
  'Vendorsecurity.ai is a comprehensive platform that offers vendor security management integrated with SOC support',
  ' and a powerful dashboard. It is designed to help companies effectively address and resolve complex vendor compliance ',
  '   issues   while allowing them to build a comprehensive risk profile across all their vendors.',
];

export default function Home() {
  const { data: session, isLoading } = useMe();

  if (isLoading) return null;
  switch (session?.role) {
    case USER_ROLE.SUPERADMIN:
      return (
        <div className='flex flex-col items-center  bg-neutral-700 bg-opacity-25 ph w-full'>
          <div className='flex flex-row items-center justify-center  h-[500px]   0  '>
            <div className='w-[550px]'>
              <p className='text-2xl text-white font-semibold '>
                Never miss Vendor Critical Vulnerability Management with SOC
                Support by Vendorsecurity.ai
              </p>
              <span className='text-lg text-white font-semibold '>
                The Vendor Security Management Platform for security Adherence &
                Compliance
              </span>
            </div>
            <Image src='/images/vsoc.png' width={600} height={30} alt='vsoc' />
          </div>
          <div className=' bg-slate-50 w-full py-5  '>
            <div className='flex flex-1 ml-[300px] text-xl text-black font-semi gap-4  '>
              <Quote className='bg-trnasparent ' color='black' size={30} />
              <div>
                {content.map((text, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.5, duration: 1 }}
                  >
                    {text}
                  </motion.div>
                ))}
              </div>
              <Quote
                className='bg-trnasparent mt-12  '
                color='black'
                size={30}
              />
            </div>
          </div>
        </div>
      );
    case USER_ROLE.SOC_ANALYST:
      return <SockAnalystDashboard />;
    case USER_ROLE.LEAD_SOC_ANALYST:
      return <SockAnalystDashboard />;
    case USER_ROLE.EMPLOYEE:
      return <EmployeeDashboard />;
    case USER_ROLE.VENDOR:
      return <EmployeeDashboard />;
    case USER_ROLE.AUDITOR:
      return <AuditorDashboard />;
    case USER_ROLE.COMPANY:
      return <CompanyDashboard />;
    case USER_ROLE.COMPANY_ADMIN:
      return (
        <div className='flex flex-col items-center  bg-neutral-700 bg-opacity-25 ph w-full'>
          <div className='flex flex-row items-center justify-center  h-[500px]   0  '>
            <div className='w-[550px]'>
              <p className='text-2xl text-white font-semibold '>
                Never miss Vendor Critical Vulnerability Management with SOC
                Support by Vendorsecurity.ai
              </p>
              <span className='text-lg text-white font-semibold '>
                The Vendor Security Management Platform for security Adherence &
                Compliance
              </span>
            </div>
            <Image src='/images/vsoc.png' width={600} height={30} alt='vsoc' />
          </div>
          <div className=' bg-slate-50 w-full py-5  '>
            <div className='flex flex-1 ml-[300px] text-xl text-black font-semi gap-4  '>
              <Quote className='bg-trnasparent ' color='black' size={30} />
              <div>
                {content.map((text, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.5, duration: 1 }}
                  >
                    {text}
                  </motion.div>
                ))}
              </div>
              <Quote
                className='bg-trnasparent mt-12  '
                color='black'
                size={30}
              />
            </div>
          </div>
        </div>
      );

    default:
      break;
  }
}
