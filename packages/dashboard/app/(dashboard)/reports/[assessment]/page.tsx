'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ScoreReportChart } from '@/elements/charts/score-report-chart';
import { cn, getUniqueGradient } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import _ from 'lodash';
import { FileDownIcon } from 'lucide-react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useState } from 'react';

export default function AssessmentScoreReport() {
  const { data: session } = useSession();
  const assessment = useParams<{ assessment: string }>();
  const assessmentDetails = trpc.assessment.getAssessment.useQuery({
    assessment: assessment.assessment,
  });

  const report = trpc.assessment.getAssessmentSubmissions.useQuery({
    assessment: assessment.assessment,
    user: session?.user._id,
  });

  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const sectionsData = _.groupBy(report.data, (r) => {
    const group = r?.submission?.question?.section.section;
    return group;
  });

  const toggleDialog = (section: string) => {
    setSelectedSection(section);
    setIsDialogOpen(true);
  };

  const calculateSectionScorePercentage = (section: string) => {
    const sectionItems = sectionsData[section];

    const totalPossibleScore = sectionItems.length; // Each correct answer is worth 1 point
    const userScore = sectionItems.reduce((acc, curr) => {
      // @ts-ignore
      return acc + (curr?.audit?.score ?? 0);
    }, 0);
    return (userScore / totalPossibleScore) * 100;
  };

  // Prepare data for the chart
  const chartData = Object.keys(sectionsData).map((section, idx) => ({
    section,
    percentage: calculateSectionScorePercentage(section),
  }));

  return (
    <div className='p-2 m-2  '>
      <div className='bg-gradient-to-tr from-slate-400 via-red-100 to-cyan-400 rounded-lg flex flex-col '>
        <Label className='w-full flex flex-col justify-center items-center my-2 text-2xl font-bold gap-5'>
          <div className='text-3xl'>
            Vendor Industry Best Practices Dashboard
          </div>
          <div className='text-sky-700'>{assessmentDetails.data?.name}</div>
        </Label>
        <div className='grid grid-cols-6 mt-10  '>
          {Object.keys(sectionsData).map((section, idx) => {
            const sectionScore = sectionsData[section].reduce((acc, curr) => {
              // @ts-ignore
              return acc + (curr?.audit?.score || 0);
            }, 0);

            const sectionPercentage = calculateSectionScorePercentage(section);

            const uniqueGradient = getUniqueGradient(idx);

            return (
              <div
                key={section}
                className='flex flex-col  items-center justify-center'
              >
                <div
                  className={cn(
                    `cursor-pointer  border-2  font-semibold rounded-full  flex items-center justify-center shadow-xl ${uniqueGradient}`,
                    sectionPercentage === 100
                      ? 'size-20'
                      : sectionPercentage < 100 && sectionPercentage > 75
                      ? 'size-16'
                      : sectionPercentage < 75 && sectionPercentage > 50
                      ? 'size-14'
                      : sectionPercentage < 50 && sectionPercentage > 25
                      ? 'size-12'
                      : 'size-11 mt-10'
                  )}
                  onClick={() => toggleDialog(section)}
                >
                  {sectionScore}
                </div>
                <Separator
                  orientation='vertical'
                  className='h-[16px] bg-stone-800'
                />
                <div
                  className='capitalize text-base bg-transparent'
                  style={{ textShadow: '1px 1px 3px rgba(0, 0, 0, 0.3)' }}
                >
                  {section}
                </div>
              </div>
            );
          })}
        </div>
        <Separator className=' bg-stone-500 p-[2px] max-w-[90vw] self-center border rounded-2xl my-10' />
        <ScoreReportChart sectionsData={chartData} />
      </div>
      {/* Dialog to display section details */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className='min-w-[70%] rounded-xl bg-cyan-900 overflow-auto'>
          <DialogHeader>
            <DialogTitle className='text-white text-center text-xl'>
              {selectedSection}
            </DialogTitle>
          </DialogHeader>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Question</TableHead>
                <TableHead>Your Answer</TableHead>
                <TableHead>Actual Answer</TableHead>
                <TableHead>Score</TableHead>
                <TableHead>Attachments</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {selectedSection &&
                sectionsData[selectedSection].map((item, index) => {
                  const fileUrl = `https://vendorai--dev.s3.us-east-2.amazonaws.com/${item?.submission?.question?.file}`;
                  return (
                    <TableRow
                      key={index}
                      className={cn(
                        // @ts-ignore
                        item?.audit?.score === 0 &&
                          'bg-red-500/50 text-black hover:text-white hover:bg-red-500/80'
                      )}
                    >
                      <TableCell>
                        {item?.submission?.question?.input.label}
                      </TableCell>
                      <TableCell
                        className={cn(
                          item?.submission?.answer === 'code:v-E42GGKAS3' &&
                            'text-center text-orange-500/50 '
                        )}
                      >
                        {item?.submission?.answer === 'code:v-E42GGKAS3'
                          ? 'Compliance'
                          : item?.submission?.answer}
                      </TableCell>
                      <TableCell>
                        {/* @ts-ignore */}
                        {item?.submission?.question?.input.answer ?? '-'}
                      </TableCell>
                      <TableCell>
                        {/* @ts-ignore */}
                        {item?.audit?.score ?? 'No score available'}
                      </TableCell>
                      <TableCell>
                        {item?.submission?.question?.file ? (
                          <Link href={fileUrl}>
                            <FileDownIcon />
                          </Link>
                        ) : (
                          'No file attached'
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
            </TableBody>
          </Table>
        </DialogContent>
      </Dialog>
    </div>
  );
}
