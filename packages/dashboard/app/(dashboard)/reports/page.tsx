'use client';
import { <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsTrigger } from '@/components/ui/tabs';
import CreditReportsDataGrid from '@/elements/reports/credit-reports.datagrid';
import ManageCreditReportForm from '@/elements/reports/manage-credit-report.form';
import { useGetCreditReportFilterMethods } from '@/hooks/filters/credit-report';
import { useRole } from '@/hooks/useRole';
import { trpc } from '@/providers/Providers';
import { useSession } from 'next-auth/react';
import { StringParam, useQueryParam, withDefault } from 'use-query-params';
import CreditReportsFilter from '@/elements/reports/credit-reports.filter';
import { CompanyType } from '../../../../shared/types/Company';

export default function Reports() {
  const { isLeadSoc } = useRole();
  const [tab, setTab] = useQueryParam(
    'tab',
    withDefault(StringParam, 'reports')
  );

  const { data: session } = useSession();
  const vendors = trpc.company.getCompanies.useQuery(
    { companies: session?.user.companies, type: [CompanyType.VENDOR] },
    { enabled: !!session?.user.companies }
  );

  const filter = useGetCreditReportFilterMethods();
  const selectedCategory = filter.watch('category');
  const selectedVendor = filter.watch('vendor');
  const selectedSubCategory = filter.watch('subCategory');
  const selectedFromDate = filter.watch('fromDate');
  const selectedToDate = filter.watch('toDate');

  const creditReports = trpc.creditReport.getCreditReports.useQuery(
    {
      vendor: selectedVendor,
      category: selectedCategory,
      fromDate: selectedFromDate,
      toDate: selectedToDate,
      subCategory: selectedSubCategory,
    },
    { enabled: !!selectedVendor }
  );
  return (
    <div className='ph container'>
      <div className='float-right'>
        {isLeadSoc && <ManageCreditReportForm />}
      </div>
      <Tabs defaultValue='reports' value={tab} onValueChange={setTab}>
        <TabsList>
          <TabsTrigger value='reports'>Reports</TabsTrigger>
          <TabsTrigger value='credit-reports'>Credit Reports</TabsTrigger>
        </TabsList>
        <TabsContent value='reports'>Assessment reports datagrid</TabsContent>
        <TabsContent value='credit-reports'>
          <CreditReportsFilter methods={filter} vendors={vendors.data || []} />
          <CreditReportsDataGrid creditReports={creditReports.data || []} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
