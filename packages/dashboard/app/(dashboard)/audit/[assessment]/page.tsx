'use client';
import { trpc } from '@/providers/Providers';
import { useParams } from 'next/navigation';
import { SECTIONTYPE } from '../../../../../shared/types/Standard';
import { USER_ROLE } from '../../../../../shared/types/User';
import React, { useMemo, useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Label } from '@/components/ui/label';
import { SubmitHandler } from 'react-hook-form';
import { AuditorForm, useAuditorScoreForm } from '@/hooks/forms/auditor';
import { toastPromise } from '@/lib/utils';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Pen, Pencil } from 'lucide-react';
import AuditorScoreForm from '@/elements/assessments/auditor-score.form';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export default function AuditAssessmentScore() {
  const [auditorDialog, setAuditorDialog] = useState(false);
  const [submissionId, setSubmissionId] = useState<string>();

  const { assessment } = useParams<{ assessment: string }>();
  const auditorMethods = useAuditorScoreForm();

  const assessmentData = trpc.assessment.getAssessment.useQuery({ assessment });

  const updateAuditorScore =
    trpc.assessment.manageAuditScoreByAuditor.useMutation();

  const assessmentSubmissions =
    trpc.assessment.getAssessmentSubmissions.useQuery({ assessment });

  const textQuestionsFilter = assessmentSubmissions.data?.filter(
    (_) =>
      _.submission.question?.input.inputType === 'text' &&
      _.sectionType === SECTIONTYPE.GENERAL
  );

  const handleSubmissionId = (id: string) => {
    setAuditorDialog(true);
    auditorMethods.setValue('submissionId', id);
  };

  const handleAuditorSubmit: SubmitHandler<AuditorForm> = (data) => {
    toastPromise({
      asyncFunc: updateAuditorScore.mutateAsync(data),
      success: 'score update successfully',
      onSuccess: () => {
        assessmentSubmissions.refetch();
        auditorMethods.reset();
      },
    });
  };

  return (
    <div className='container1 ph'>
      <Label className='my-4 text-2xl text-white font-semibold flex items-center justify-center'>
        {assessmentData.data?.name}
      </Label>
      <Dialog open={auditorDialog} onOpenChange={setAuditorDialog}>
        <DialogContent>
          <AuditorScoreForm
            methods={auditorMethods}
            handleSubmit={handleAuditorSubmit}
          />
        </DialogContent>
      </Dialog>
      <div className='flex gap-4'>
        <Card className='bg-gradient-to-tr from-blue-200 to-cyan-200  border-2 border-cyan-100  size-72 flex flex-col  items-start justify-center'>
          <CardHeader>
            <CardTitle>Compliances</CardTitle>
            <CardDescription>Compliances list</CardDescription>
          </CardHeader>
          <CardContent>
            {assessmentData.data?.assignments.map((ad, idx) => {
              return (
                <div key={idx}>
                  {/* @ts-ignore */}
                  {ad?.files?.map((fil) => {
                    const fileUrl = `https://vendorai--dev.s3.us-east-2.amazonaws.com/${fil.file}`;
                    return (
                      <div
                        key={fil.file}
                        className='grid grid-cols-2 gap-2 mt-2'
                      >
                        <p>{fil.compliance}</p>
                        <Button asChild>
                          <Link href={fileUrl}>Download</Link>
                        </Button>
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </CardContent>
        </Card>

        <Table>
          <TableHeader className='bg-cyan-700 font-semibold  text-lg text-white text-center '>
            <TableRow>
              <TableHead>Question</TableHead>
              <TableHead>Answer</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {textQuestionsFilter?.map((t, idx) => (
              <TableRow key={idx}>
                <TableCell>{t.submission.question?.input.label}</TableCell>
                <TableCell>{t.submission.answer}</TableCell>
                <TableCell>
                  <Button
                    //@ts-ignore
                    onClick={() => handleSubmissionId(t._id)}
                    // @ts-ignore
                    disabled={t?.audit?.score}
                  >
                    <Pencil />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
