'use client';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AssessmentDataGrid from '@/elements/assessments/assessment.datagrid';
import CreditReportsDataGrid from '@/elements/reports/credit-reports.datagrid';
import CreditReportsFilter from '@/elements/reports/credit-reports.filter';
import { useGetCreditReportFilterMethods } from '@/hooks/filters/credit-report';
import { trpc } from '@/providers/Providers';
import { useSession } from 'next-auth/react';
import { useParams } from 'next/navigation';
import { CompanyType } from '../../../../../shared/types/Company';
import React from 'react';
import { StringParam, useQueryParam, withDefault } from 'use-query-params';
import { Label } from '@/components/ui/label';

export default function VendorDetails() {
  const { data: session } = useSession();
  const { vendorId } = useParams<{ vendorId: string }>();
  const [tab, setTab] = useQueryParam(
    'tab',
    withDefault(StringParam, 'assessments')
  );

  const vendor = trpc.company.getCompanies.useQuery(
    { companies: [vendorId] },
    {
      select(data) {
        return data.at(0);
      },
    }
  );

  const vendors = trpc.company.getCompanies.useQuery(
    {
      reportsTo: session?.user.companies?.at(0),
    },
    {
      enabled: !!session?.user.companies?.length,
    }
  );

  const vendorIds = (vendors.data || []).map((_) => String(_._id));

  const assessments = trpc.assessment.getAssessments.useQuery(
    { vendor: vendorIds },
    { enabled: !!vendorIds?.length }
  );

  const filter = useGetCreditReportFilterMethods();
  const selectedVendor = filter.watch('vendor');

  const creditReports = trpc.creditReport.getCreditReports.useQuery(
    { vendor: selectedVendor },
    { enabled: !!selectedVendor }
  );

  if (!vendor.data) return null;
  return (
    <div className='ph container1 flex gap-4'>
      <Tabs defaultValue='assessments' value={tab} onValueChange={setTab}>
        <TabsList>
          <TabsTrigger value='credit-reports'>Vendor Details</TabsTrigger>
          <TabsTrigger value='assessments'>Assessments</TabsTrigger>
        </TabsList>
        <TabsContent value='credit-reports'>
          {vendor.data?.type === CompanyType.VENDOR && (
            <Table className=''>
              <TableBody>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>Vendor</TableCell>
                  <TableCell>{vendor.data.company}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>Sector</TableCell>
                  <TableCell>{vendor.data.sector}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>
                    Address
                  </TableCell>
                  <TableCell>{vendor.data.address}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>City</TableCell>
                  <TableCell>{vendor.data.city}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>State</TableCell>
                  <TableCell>{vendor.data.state}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>
                    Location
                  </TableCell>
                  <TableCell>{vendor.data.location}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>
                    Company Engagement Manager
                  </TableCell>
                  <TableCell>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className='text-center  font-semibold underline'>
                            Email
                          </TableHead>
                          <TableHead className='text-center  font-semibold underline'>
                            Country Code
                          </TableHead>
                          <TableHead className='text-center  font-semibold underline'>
                            Contact
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {/* @ts-ignore */}
                        {vendor.data.companyEngagementManager.map((c, idx) => {
                          return (
                            <TableRow key={idx}>
                              <TableCell>{c.email}</TableCell>
                              <TableCell className='text-center'>
                                {c.countryCode}
                              </TableCell>
                              <TableCell>{c.phone}</TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>
                    Vendor Assessment Manager
                  </TableCell>
                  <TableCell>
                    <Table className=''>
                      <TableHeader>
                        <TableRow>
                          <TableHead className='text-center font-semibold underline'>
                            Email
                          </TableHead>
                          <TableHead className='text-center font-semibold underline'>
                            Country Code
                          </TableHead>
                          <TableHead className='text-center font-semibold underline'>
                            Contact
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {vendor.data.vendorAssesmentManager?.map((c, idx) => (
                          <TableRow key={idx}>
                            <TableCell>{c.email}</TableCell>
                            <TableCell className='text-center'>
                              {c.countryCode}
                            </TableCell>
                            <TableCell>{c.phone}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>
                    Priority
                  </TableCell>
                  <TableCell>{vendor.data.priorityLevel}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className='font-bold border-r-2'>
                    Criticality Levels
                  </TableCell>
                  <TableCell>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className='border-2 font-semibold underline'>
                            Level
                          </TableHead>
                          <TableHead className='border-2 font-semibold underline'>
                            Duration in days
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {vendor.data.criticalityLevels.map((c, idx) => {
                          return (
                            <TableRow
                              key={idx}
                              className='border-2 bg-amber-100'
                            >
                              <TableCell className='border-2'>
                                {c.level.toUpperCase()}
                              </TableCell>
                              <TableCell className='text-center bg-amber-100'>
                                {c.timeDuration}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          )}
        </TabsContent>
        <TabsContent value='assessments'>
          <div className=''>
            <Label className='text-white font-semibold text-xl text-center  '>
              Assessment Details
            </Label>
            <AssessmentDataGrid assessments={assessments.data || []} />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
