'use client';
import { But<PERSON> } from '@/components/ui/button';
import { toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import { KeyRound } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';

export default function ResetPage() {
  const { resetId } = useParams<{ resetId: string }>();
  const resetAccount = trpc.user.resetAccount.useMutation();

  const router = useRouter();

  const handleReset = () =>
    toastPromise({
      asyncFunc: resetAccount.mutateAsync({ accountResetId: resetId }),
      success: 'account reset successfully',
      onSuccess() {
        router.replace('/login');
      },
    });

  return (
    <div className='container p-10 flex flex-col pt-10'>
      <div className='self-center flex items-center gap-4 mt-40'>
        <KeyRound className='text-primary' size={100} />
        <p className='font-semibold text-5xl text-black/80'>Reset Account</p>
      </div>

      <div className='self-center'>
        <Button onClick={handleReset}>Reset Account</Button>
      </div>
    </div>
  );
}
