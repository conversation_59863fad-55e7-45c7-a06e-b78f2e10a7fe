'use client';
import { But<PERSON> } from '@/components/ui/button';
import { toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import { LogIn } from 'lucide-react';
import { signIn } from 'next-auth/react';
import { useParams } from 'next/navigation';

export default function SSL() {
  const loginWithToken = trpc.user.loginWithToken.useMutation();
  const { jwt } = useParams<{ jwt: string }>();

  return (
    <div className='flex flex-col ph justify-center items-center container'>
      <div className='flex items-center gap-4'>
        <Button
          onClick={async () => {
            toastPromise({
              asyncFunc: loginWithToken.mutateAsync({ token: jwt }),
              success: 'SSL login sucessfull',
              onSuccess(data) {
                signIn('credentials', {
                  response: JSON.stringify(data),
                  redirect: true,
                  callbackUrl: '/',
                });
              },
            });
          }}
        >
          <LogIn className='size-4 mr-2' />
          <p>SINGLE SIGNON</p>
        </Button>
      </div>
    </div>
  );
}
