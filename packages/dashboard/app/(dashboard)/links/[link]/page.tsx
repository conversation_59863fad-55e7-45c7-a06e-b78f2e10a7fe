'use client';
import { trpc } from '@/providers/Providers';
import { isAfter, isBefore } from 'date-fns';
import { TriangleAlert } from 'lucide-react';
import { signIn, useSession } from 'next-auth/react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { USER_ROLE } from '../../../../../shared/types/User';
import { useEffect } from 'react';
import { jwtDecode } from 'jwt-decode';

export default function Link() {
  const { data: session } = useSession();
  const router = useRouter();
  const { link } = useParams<{ link: string }>();

  const { data: linkDetails, isLoading } = trpc.link.getLink.useQuery({ link });
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  // useEffect(() => {
  //   if (token)
  //     signIn('credentials', {
  //       redirect: false,
  //       response: JSON.stringify({ ...jwtDecode(token), token }),
  //     });
  // }, [token]);

  useEffect(() => {
    if (!token) return;
    const decoded = jwtDecode(token);
    signIn('credentials', {
      redirect: false,
      response: JSON.stringify({ ...jwtDecode(token), token }),
    });
    if (
      linkDetails?.expiryDate &&
      isBefore(new Date(), linkDetails.expiryDate)
    ) {
      const params = new URLSearchParams();
      const questions = linkDetails.metadata.questions;
      if (questions?.length)
        questions.forEach((q) => params.append('question', q));
      if (
        linkDetails.metadata.role === USER_ROLE.EMPLOYEE &&
        // @ts-ignore
        linkDetails.metadata.role === (decoded.role as unknown as string)
      )
        return router.replace(
          `/assessment/${
            linkDetails.metadata.assessment
          }/section-questions?${params.toString()}`
        );

      router.replace(
        `/assessment/${linkDetails.metadata.assessment}?${params.toString()}`
      );
    }
  }, [
    linkDetails?.expiryDate,
    linkDetails?.metadata.assessment,
    linkDetails?.metadata.questions,
    linkDetails?.metadata.role,
    router,
    token,
  ]);

  if (isLoading || !linkDetails) return null;
  return (
    <div className='container flex flex-col'>
      {linkDetails.expiryDate &&
        isAfter(new Date(), linkDetails.expiryDate) && (
          <div className='p-4 flex items-center gap-6 self-center'>
            <TriangleAlert className='text-amber-500' size={200} />
            <div className='text-5xl font-bold'>Oops link expired</div>
          </div>
        )}
    </div>
  );
}
