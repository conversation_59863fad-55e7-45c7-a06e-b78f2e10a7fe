'use client';
import { useSession } from 'next-auth/react';
import { USER_ROLE } from '../../../../../shared/types/User';
import React from 'react';
import EmployeeAssessmentDetails from './employee-assessment-details';
import CompanyAssessmentApprovals from './company-assessment-approvals';
import AssessmentDetails from './assessment-details';

export default function Assessment() {
  const { data: session } = useSession();

  switch (session?.user.role) {
    case USER_ROLE.VENDOR:
      return <EmployeeAssessmentDetails />;
    case USER_ROLE.EMPLOYEE:
      return <EmployeeAssessmentDetails />;
    case USER_ROLE.SOC_ANALYST:
      return <AssessmentDetails userRole={session.user.role} />;
    case USER_ROLE.LEAD_SOC_ANALYST:
      return <AssessmentDetails userRole={session.user.role} />;
    case USER_ROLE.AUDITOR:
      return <AssessmentDetails userRole={session.user.role} />;
    case USER_ROLE.COMPANY:
      return <CompanyAssessmentApprovals />;
    default:
      return null;
  }
}
