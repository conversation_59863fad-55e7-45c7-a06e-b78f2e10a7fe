"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { trpc } from "@/providers/Providers";
import { useParams, useRouter, useSearchParams } from "next/navigation";

import { sendSubmissionScoreCardValidator } from "../../../../../shared/validators/assessment-submission.validator";
import { z } from "zod";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { cn, toastPromise } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calculator, CheckCheck, Download, Flag, Mail } from "lucide-react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";

import { FormTextField } from "@/components/form/FormTextField";
import { useMemo, useState } from "react";
import { SECTIONTYPE } from "../../../../../shared/types/Standard";

import { RouterOutput } from "../../../../../shared";
import VulnerabilityScoreForm from "@/elements/assessments/vulnerability-score.form";
import {
  VulnerabilityForm,
  useVulnerabilityScoreForm,
} from "@/hooks/forms/vulnerability";
import { USER_ROLE } from "../../../../../shared/types/User";
import { AuditorForm, useAuditorScoreForm } from "@/hooks/forms/auditor";
import { useRole } from "@/hooks/useRole";
import { ScrollArea } from "@/components/ui/scroll-area";
import { VulnerabilityDetailsSheet } from "@/sheets/assessment.sheet";
import { VulnerabilityResponseSheet } from "@/sheets/assessment.sheet/VulnerabilityResponseSheet";
import { VulnerabilityResolveAndExceptionSheet } from "@/sheets/assessment.sheet/VulnerabilityResolveAndExceptionSheet";
import {
  VendorAcceptanceStatus,
  VulnerabilityClosureStatus,
  VulnerabilityResolveStatus,
} from "../../../../../shared/types/AssessmentSubmission";
import CompanyClosureApprovalForm from "@/elements/assessments/company-closure-approval.form";
import { useDownload } from "@/hooks/useDownload";
import VulnerabilityCloseSheet from "@/sheets/assessment.sheet/VulnerabilityCloseSheet";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { format, sub } from "date-fns";
import { CRITICALITY_LEVELS } from "packages/shared/types/Company";
import VendorCalculatedData from "@/elements/vendor-calculated/calculated-data";
type VulnerabilityScoreForm = z.infer<typeof sendSubmissionScoreCardValidator>;

type Props = {
  userRole:
    | USER_ROLE.LEAD_SOC_ANALYST
    | USER_ROLE.SOC_ANALYST
    | USER_ROLE.AUDITOR
    | USER_ROLE.COMPANY;
};

const sectionTypeByUserRole = {
  [USER_ROLE.SOC_ANALYST]: [SECTIONTYPE.INVENTORY],
  [USER_ROLE.LEAD_SOC_ANALYST]: [SECTIONTYPE.INVENTORY],
  [USER_ROLE.AUDITOR]: [SECTIONTYPE.GENERAL],
  [USER_ROLE.COMPANY]: [SECTIONTYPE.GENERAL, SECTIONTYPE.INVENTORY],
} as const;

export default function AssessmentDetails({ userRole }: Props) {
  const roles = useRole();
  const { isAuditor, isLeadSoc, isSoc } = roles;
  const [socDialog, setSocDialog] = useState(false);
  const router = useRouter();

  const [auditorDialog, setAuditorDialog] = useState(false);

  const [selectedQuestion, setSelectedQuestion] = useState<string>();
  const [showHistorySheet, setShowHistorySheet] = useState(false);

  const [answer, setAnswer] = useState<string>();

  const [exceptionDialog, setExceptionDialog] = useState(false);
  const [closureDialog, setClosureDialog] = useState(false);

  const vulnerabilityMethods = useVulnerabilityScoreForm();
  const auditorMethods = useAuditorScoreForm();

  const { assessment } = useParams<{ assessment: string }>();

  const params = useSearchParams();

  const questions = params.getAll("question");

  const vScoreFormMethods = useForm<VulnerabilityScoreForm>({
    defaultValues: { assessment, message: "", vendor: "" },
    resolver: zodResolver(sendSubmissionScoreCardValidator),
  });

  const download = useDownload();

  const _assessment = trpc.assessment.getAssessment.useQuery({ assessment });

  const questionsFilter = useMemo(() => {
    return (_assessment.data?.assignments || [])
      .map((_) =>
        (_.questions || [])
          .filter((question) =>
            sectionTypeByUserRole[userRole].includes(
              // @ts-ignore
              question?.section?.sectionType || ""
            )
          )
          .map((_) => String(_._id))
      )
      .flat();
  }, [_assessment.data?.assignments, userRole]);

  const assessmentSubmissions =
    trpc.assessment.getAssessmentSubmissions.useQuery(
      { assessment, questions: questionsFilter },
      {
        enabled: !!questionsFilter.length,
        select(data) {
          // only show non bool questions to auditor
          if (userRole === USER_ROLE.AUDITOR) {
            return data.filter((items) => {
              return items.submission.question?.input.inputType !== "switch";
            });
          }
          return data;
        },
        onSuccess(data) {
          if (data.at(0)?.vendor)
            vScoreFormMethods.setValue(
              "vendor",
              // @ts-ignore
              String(data.at(0)?.vendor._id)
            );
        },
      }
    );

  const updateSocScore = trpc.assessment.manageVulnerabilityScore.useMutation();
  const updateAuditorScore =
    trpc.assessment.manageAuditScoreByAuditor.useMutation();

  const onSocModalOpen = (
    submission: RouterOutput["assessment"]["getAssessmentSubmissions"][number]
  ) => {
    const { _id } = submission;
    vulnerabilityMethods.setValue("submissionId", String(_id));
    setSocDialog(true);
  };

  const onAuditorModalOpen = (
    submission: RouterOutput["assessment"]["getAssessmentSubmissions"][number]
  ) => {
    const { _id } = submission;
    auditorMethods.setValue("submissionId", String(_id));
    setAuditorDialog(true);
  };

  const handleSocVulnerabilityScoreSubmit: SubmitHandler<VulnerabilityForm> = (
    data
  ) => {
    toastPromise({
      asyncFunc: updateSocScore.mutateAsync(data),
      success: "score update successfully",
      onSuccess: () => {
        assessmentSubmissions.refetch();
        vulnerabilityMethods.reset();
        setSocDialog(false);
      },
    });
  };

  const handleVulnerabilityUpdateRating = ({
    submission,
    assessment,
    vulnerability,
    _id,
  }: RouterOutput["assessment"]["getAssessmentSubmissions"][number]) => {
    vulnerabilityMethods.setValue(
      "criticalityLevel",
      vulnerability.criticalityLevel
    );
    vulnerabilityMethods.setValue("cvssScore", vulnerability.cvssScore);
    vulnerabilityMethods.setValue("remarks", vulnerability.remarks);
    vulnerabilityMethods.setValue("score", vulnerability.score);
    vulnerabilityMethods.setValue("submissionId", String(_id));
    setSocDialog(true);
  };

  // TODO: Refactor the code duplication
  const handleSocSubmitAndEmail: SubmitHandler<VulnerabilityForm> = (data) => {
    toastPromise({
      asyncFunc: updateSocScore.mutateAsync({ ...data, sendEmail: true }),
      success: "score update & mail sent successfully",
      onSuccess: () => {
        assessmentSubmissions.refetch();
        vulnerabilityMethods.reset();
        setSocDialog(false);
      },
    });
  };

  const handleAuditorSubmit: SubmitHandler<AuditorForm> = (data) => {
    toastPromise({
      asyncFunc: updateAuditorScore.mutateAsync(data),
      success: "score update successfully",
      onSuccess: () => {
        assessmentSubmissions.refetch();
        auditorMethods.reset();
      },
    });
  };

  const sendVcard = trpc.assessment.sendVulnerabilityScoreCard.useMutation();
  const handleSendVCardMail = vScoreFormMethods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: sendVcard.mutateAsync(data),
      success: "Mail sent successfully",
    });
  });

  const submissionHistory =
    trpc.assessment.getAssessmentSubmissionHistory.useQuery({
      question: selectedQuestion,
    });

  const showHistory = (questionId: string, answer: string) => {
    setSelectedQuestion(questionId);
    setShowHistorySheet(true);
    setAnswer(answer);
  };

  const filterHistory = submissionHistory.data?.filter(
    (h) => h.submissionSnapshot.submission.answer === answer
  );

  const handleRoute = (data: string) => {
    router.push(
      `/assessment/${assessment}/inventory?asset=${data}&company=${company}&vendor=${vendor}`
    );
  };

  const company = _assessment?.data?.company;
  const vendor = _assessment?.data?.vendor;

  return (
    <div className="space-y-4 container1 ">
      {/* Soc Form */}
      <VendorCalculatedData
        assessment={assessment}
        // @ts-expect-error
        company={company}
        // @ts-expect-error
        vendor={vendor}
      />
      <Dialog open={socDialog} onOpenChange={setSocDialog}>
        <DialogContent className="min-w-[1000px]">
          <VulnerabilityScoreForm
            methods={vulnerabilityMethods}
            handleSubmit={handleSocVulnerabilityScoreSubmit}
            handleSocSubmitAndEmail={handleSocSubmitAndEmail}
          />
        </DialogContent>
      </Dialog>
      <Sheet open={showHistorySheet} onOpenChange={setShowHistorySheet}>
        <SheetContent className="min-w-[70vw]">
          <ScrollArea className="ph">
            <SheetHeader>
              <SheetTitle>Submission history</SheetTitle>
            </SheetHeader>

            <div>
              <Accordion type="single" collapsible>
                {(filterHistory || []).map((history, idx) => {
                  return (
                    <AccordionItem
                      value={String(history._id)}
                      key={String(history._id)}
                    >
                      <AccordionTrigger>
                        {format(history?.createdAt, "MMM d,yyyy - h:mm a")}
                      </AccordionTrigger>
                      <AccordionContent>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <TableHead> Sender</TableHead>
                              <TableCell>
                                {history?.actionTaker?.email}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Reciepient</TableHead>

                              <TableCell>
                                {history?.actionReciver?.email}
                              </TableCell>
                            </TableRow>

                            <TableRow>
                              <TableHead>Vendor</TableHead>
                              <TableCell>{history.vendor?.company}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Inventory</TableHead>
                              <TableCell>
                                {
                                  history?.submissionSnapshot?.submission
                                    ?.question?.input.label
                                }
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Inventory Details</TableHead>
                              <TableCell>
                                {history.submissionSnapshot.submission.answer}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table>
                          <TableHeader>
                            <TableRow className="text-center">
                              <TableHead className="text-black font-semibold  ">
                                Cvss Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                VendorAi Score
                              </TableHead>
                              <TableHead className="text-black font-semibold">
                                Criticality
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            <TableRow className="text-center">
                              <TableCell>
                                {
                                  history.submissionSnapshot.vulnerability
                                    ?.cvssScore
                                }
                              </TableCell>
                              <TableCell>
                                {
                                  history.submissionSnapshot.vulnerability
                                    ?.score
                                }
                              </TableCell>
                              <TableCell
                                className={cn(
                                  "font-bold",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.HIGH &&
                                    "text-rose-600 w",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.CRITICAL &&
                                    "text-red-700",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.MEDIUM &&
                                    "text-orange-600",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.LOW && "text-yellow-600",
                                  // @ts-ignore
                                  history.submissionSnapshot?.vulnerability
                                    ?.criticalityLevel ===
                                    CRITICALITY_LEVELS.VERYLOW &&
                                    "text-green-700"
                                )}
                              >
                                {history.submissionSnapshot?.vulnerability?.criticalityLevel.toUpperCase()}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Created Date :
                              </TableHead>
                              <TableCell>
                                {/* @ts-ignore */}
                                {history.submissionSnapshot.vulnerability
                                  ?.createdAt
                                  ? format(
                                      // @ts-ignore
                                      history.submissionSnapshot.vulnerability
                                        ?.createdAt,
                                      "MMM-d-yyyy - h:mm a"
                                    )
                                  : "-"}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead>Vulnerability Description</TableHead>
                              <TableCell>
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html:
                                      history?.submissionSnapshot?.vulnerability
                                        ?.remarks ?? "-",
                                  }}
                                />
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Acceptance by Vendor:
                              </TableHead>
                              <TableCell>
                                {history?.submissionSnapshot
                                  ?.vendorAcceptanceStatus ===
                                VendorAcceptanceStatus.ACCEPT
                                  ? "Vendor acknowledged for remediation(s)"
                                  : history.submissionSnapshot
                                      ?.vendorAcceptanceStatus ===
                                    VendorAcceptanceStatus.REJECT
                                  ? " Vendor identified false positive"
                                  : null}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Acceptance Description:
                              </TableHead>
                              <TableCell>
                                {
                                  history?.submissionSnapshot
                                    ?.vendorRejectReason
                                }
                              </TableCell>
                            </TableRow>
                            {history?.submissionSnapshot.vulnerability
                              ?.resolveStatus ===
                            VulnerabilityResolveStatus.RESOLVED ? (
                              <>
                                <TableRow>
                                  <TableHead className="text-black font-semibold  ">
                                    Vulnerability Resolved Date:
                                  </TableHead>
                                  <TableCell>
                                    {history?.submissionSnapshot?.vulnerability
                                      ?.resolvedDate
                                      ? format(
                                          history?.submissionSnapshot
                                            ?.vulnerability?.resolvedDate,
                                          "MMM - d -yyyy - h:mm a"
                                        )
                                      : null}
                                  </TableCell>
                                </TableRow>
                              </>
                            ) : (
                              ""
                            )}
                            {history?.submissionSnapshot.vulnerability
                              ?.resolveStatus ===
                            VulnerabilityResolveStatus.EXCEPTION ? (
                              <>
                                <TableRow>
                                  <TableHead className="text-black font-semibold  ">
                                    Vendor Exception Reason:
                                  </TableHead>
                                  <TableCell>
                                    {
                                      history?.submissionSnapshot.vulnerability
                                        ?.resolveDescription
                                    }
                                  </TableCell>
                                </TableRow>
                                {/* <TableRow>
                                  <TableHead className='text-black font-semibold  '>
                                    Soc Exception Reason:
                                  </TableHead>
                                  <TableCell>
                                    {
                                      history?.submissionSnapshot.vulnerability
                                        ?.vulnerabilityExceptionApprovalDescriptionBySoc
                                    }
                                  </TableCell>
                                </TableRow> */}

                                {/* <TableRow>
                                  <TableHead className='text-black font-semibold  '>
                                    Soc Exception Evidence:
                                  </TableHead>
                                  <TableCell>
                                    <Button>
                                      <Download
                                        onClick={() =>
                                          download({
                                            fileId: history?.submissionSnapshot
                                              .vulnerability
                                              ?.vulnerabilityExceptionApprovalEvidenceBySoc as string,
                                          })
                                        }
                                      />
                                    </Button>
                                  </TableCell>
                                </TableRow> */}
                              </>
                            ) : null}

                            {history?.submissionSnapshot.vulnerability
                              ?.vulnerabilityClosureDescriptionBySoc ? (
                              <>
                                <TableRow>
                                  <TableHead className="text-black font-semibold  ">
                                    Vulnerability Closer Description By Soc:
                                  </TableHead>
                                  <TableCell>
                                    {
                                      history?.submissionSnapshot.vulnerability
                                        ?.vulnerabilityClosureDescriptionBySoc
                                    }
                                  </TableCell>
                                </TableRow>
                              </>
                            ) : null}
                            <TableRow>
                              <TableHead className="text-black font-semibold  ">
                                Vulnerability Closure status by Customer:
                              </TableHead>
                              <TableCell>
                                {history?.submissionSnapshot.vulnerability
                                  ?.vulnerabilityClosureCompanyApprovalStatus ===
                                VulnerabilityClosureStatus.CLOSED
                                  ? "Approved for closure"
                                  : history?.submissionSnapshot.vulnerability
                                      ?.vulnerabilityClosureCompanyApprovalStatus ===
                                    VulnerabilityClosureStatus.OPEN
                                  ? "Required reassessment"
                                  : "Pending"}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </AccordionContent>
                    </AccordionItem>
                  );
                })}
              </Accordion>
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
      {/* Auditor Form */}
      {/* <Dialog open={auditorDialog} onOpenChange={setAuditorDialog}>
        <DialogContent>
          <AuditorScoreForm
            methods={auditorMethods}
            handleSubmit={handleAuditorSubmit}
          />
        </DialogContent>
      </Dialog> */}
      {/* Exception conformation */}

      {/* Vulnerability Closer */}
      <Button
        onClick={() =>
          router.push(
            `/assessment/${assessment}/calculator?company=${company}&vendor=${vendor}`
          )
        }
      >
        <Calculator /> Calculate V.AI Score
      </Button>

      <ScrollArea className="ph1">
        <Table>
          <TableHeader className="bg-cyan-700 font-semibold  text-lg text-white text-center ">
            {userRole === USER_ROLE.SOC_ANALYST && (
              <>
                <TableHead className="font-semibold  text-lg text-white text-center">
                  Inventory
                </TableHead>
                <TableHead className="font-semibold text-lg text-white text-center">
                  Vendor Vulnerability Status
                </TableHead>
                <TableHead className="font-semibold text-lg text-white text-center">
                  Vendor Vulnerability Response
                </TableHead>
                <TableHead className="font-semibold text-lg text-white text-center">
                  VSOC Vendor Vulnerability Resolve / Exception State{" "}
                </TableHead>

                <TableHead className="font-semibold text-lg text-white text-center">
                  Customer Vulnerability Close Status
                </TableHead>
                <TableHead className="font-semibold text-lg text-white text-center flex flex-row-reverse items-center justify-center mt-4 ">
                  <Flag size={50} />
                  <p>VSOC Vulnerability Close Status</p>
                </TableHead>
                <TableHead>History</TableHead>
                <TableHead>CVE</TableHead>
              </>
            )}
            {userRole === USER_ROLE.AUDITOR && (
              <>
                <TableHead>Status</TableHead>
                <TableHead>Action</TableHead>
              </>
            )}
          </TableHeader>
          <TableBody className="overflow-y-auto">
            {(assessmentSubmissions.data || [])
              .filter((isb) =>
                !questions.length
                  ? true
                  : questions.includes(String(isb.submission.question?._id))
              )
              .map((submission) => {
                const question = submission.submission.question;
                if (!question) return null;

                return (
                  <TableRow key={String(submission._id)}>
                    <TableCell width={"600px"}>
                      {submission.submission.answer}
                    </TableCell>

                    <TableCell className=" ">
                      <VulnerabilityDetailsSheet
                        submission={submission}
                        role={roles}
                        onVulnerabilityPress={
                          submission.sectionType === SECTIONTYPE.INVENTORY
                            ? onSocModalOpen
                            : onAuditorModalOpen
                        }
                        onUpdateVulnerabilityPress={
                          handleVulnerabilityUpdateRating
                        }
                      />
                    </TableCell>

                    {/* TODO: Show employee acceptance status */}
                    <TableCell className="pl-10 ">
                      <VulnerabilityResponseSheet
                        role={roles}
                        submission={submission}
                        onUpdateVulnerabilityPress={
                          handleVulnerabilityUpdateRating
                        }
                      />
                    </TableCell>
                    {/* Employee Resolved Vulnerability */}
                    <TableCell className="pl-10">
                      <VulnerabilityResolveAndExceptionSheet
                        role={roles}
                        submission={submission}
                        onUpdateVulnerabilityPress={
                          handleVulnerabilityUpdateRating
                        }
                      />
                    </TableCell>
                    {/* Exception */}
                    {/* <TableCell>
                    <Button onClick={() => setExceptionDialog(true)}>
                      <Plus />
                      Exception
                    </Button>
                  </TableCell> */}
                    {/* Closure mail*/}
                    {/* <TableCell>
                    <Button onClick={() => setClosureDialog(true)}>
                      <Plus />
                      Request Closure
                    </Button>
                  </TableCell> */}
                    {/* Closure Response by company */}
                    <TableCell className="pl-10 ">
                      <VulnerabilityCloseSheet
                        role={roles}
                        submission={submission}
                      />
                    </TableCell>
                    <TableCell className="flex flex-col items-center">
                      {submission.vulnerability?.vulnerabilityClosureStatus ===
                      VulnerabilityClosureStatus.OPEN ? (
                        <Flag
                          color="red"
                          size={50}
                          className="shadow-md shadow-red-900 border-red-900 border rounded-full p-2 "
                        />
                      ) : submission.vulnerability
                          ?.vulnerabilityClosureStatus ===
                        VulnerabilityClosureStatus.CLOSED ? (
                        <CheckCheck color="green" size={30} />
                      ) : null}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        onClick={() =>
                          showHistory(
                            submission.submission.question?._id as string,
                            submission.submission.answer as string
                          )
                        }
                      >
                        View History
                      </Button>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        onClick={() =>
                          handleRoute(submission.submission.answer as string)
                        }
                      >
                        CVE Calculation
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
          </TableBody>
          {(isSoc || isLeadSoc || isAuditor) && (
            <TableFooter className="sticky bottom-0 bg-slate-200">
              <TableCell colSpan={1}></TableCell>
              <TableCell className="text-right">
                Send All Vulnerabilities:
              </TableCell>
              <TableCell colSpan={6}>
                {/* Send Email Trigger */}
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>
                      <Mail className="size-4 mr-2" />
                      Send Email To All
                    </Button>
                  </DialogTrigger>

                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Send Score Card</DialogTitle>
                    </DialogHeader>

                    <form onSubmit={handleSendVCardMail}>
                      <FormProvider {...vScoreFormMethods}>
                        <FormTextField
                          name="message"
                          label="Remarks"
                          placeholder="Write any optional remarks"
                        />
                        <div className="flex gap-4 mt-4 justify-end">
                          <DialogClose asChild>
                            <Button type="button" variant="destructive">
                              Close
                            </Button>
                          </DialogClose>
                          <Button>Send</Button>
                        </div>
                      </FormProvider>
                    </form>
                  </DialogContent>
                </Dialog>
              </TableCell>
            </TableFooter>
          )}
        </Table>
      </ScrollArea>
    </div>
  );
}
