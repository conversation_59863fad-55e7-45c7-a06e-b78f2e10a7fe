import { Button } from '@/components/ui/button';
import { Table, TableCell, TableHead, TableRow } from '@/components/ui/table';
import { Download } from 'lucide-react';
import { RouterOutput } from '../../../../../shared';
import React from 'react';
import {
  VendorAcceptanceStatus,
  VulnerabilityResolveStatus,
} from '../../../../../shared/types/AssessmentSubmission';
import { useDownload } from '@/hooks/useDownload';
import { format } from 'date-fns';
import { Label } from '@/components/ui/label';

export default function VulnerabilityResolveDataGrid({
  vulnerability,
  isRejected,
}: {
  vulnerability: RouterOutput['assessment']['getAssessmentSubmissions'][number]['vulnerability'];
  isRejected: VendorAcceptanceStatus;
}) {
  const download = useDownload();
  if (isRejected === VendorAcceptanceStatus.REJECT) return null;
  return (
    <div>
      <div className='mb-10 space-y-3'>
        <Label className='text-2xl font-semibold capitalize'>
          Vendor {vulnerability?.resolveStatus} Details
        </Label>
        <Table className='min-w-[1200px]'>
          <TableRow>
            <TableHead className='capitalize'>
              Vulnerability {vulnerability?.resolveStatus} State
            </TableHead>
            <TableCell className='uppercase'>
              {vulnerability?.resolveStatus}
            </TableCell>
          </TableRow>
          <TableRow>
            <TableHead className='capitalize'>
              {vulnerability?.resolveStatus} Description
            </TableHead>
            <TableCell>{vulnerability?.resolveDescription}</TableCell>
          </TableRow>
          <TableRow>
            <TableHead className='capitalize'>
              Vendor {vulnerability?.resolveStatus} Requested Date
            </TableHead>
            <TableCell>
              {vulnerability?.resolvedDate
                ? format(vulnerability?.resolvedDate, 'MMM d,yyy')
                : '-'}
            </TableCell>
          </TableRow>
          <TableRow>
            <TableHead>
              Vulnerability &nbsp;
              {vulnerability?.resolveStatus ===
                VulnerabilityResolveStatus.EXCEPTION && 'Exception Evidence'}
              {vulnerability?.resolveStatus ===
                VulnerabilityResolveStatus.RESOLVED && 'Resolved Evidence'}
            </TableHead>
            <TableCell>
              <Button size={'icon'}>
                <Download
                  onClick={() =>
                    download({
                      fileId:
                        vulnerability?.vulnerabilityResolvedOrExceptionEvidence as string,
                    })
                  }
                />
              </Button>
            </TableCell>
          </TableRow>
        </Table>
      </div>
      {vulnerability?.resolveStatus === VulnerabilityResolveStatus.EXCEPTION &&
      vulnerability?.companyExceptionApproval ? (
        <div className='space-y-3'>
          <Label className='text-2xl font-semibold '>
            Company Exception Details
          </Label>
          <Table>
            <TableRow>
              <TableHead>companyExceptionApproval</TableHead>
              <TableCell>
                {vulnerability?.companyExceptionApproval.toUpperCase()}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableHead>ApprovalDescripton</TableHead>
              <TableCell>
                {vulnerability?.companyExceptionApprovalDescripton}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableHead>ApprovalEvidence</TableHead>
              <TableCell>
                <Button size={'icon'}>
                  <Download
                    onClick={() =>
                      download({
                        fileId:
                          vulnerability?.companyExceptionApprovalEvidence as string,
                      })
                    }
                  />
                </Button>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableHead>ExceptionEndDate</TableHead>
              <TableCell>
                {/* @ts-ignore */}
                {vulnerability?.companyExceptionEndDate
                  ? format(vulnerability?.companyExceptionEndDate, 'MMM d,yyyy')
                  : null}
              </TableCell>
            </TableRow>
          </Table>
        </div>
      ) : null}
    </div>
  );
}
