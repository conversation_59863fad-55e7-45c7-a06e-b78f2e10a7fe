/* eslint-disable jsx-a11y/alt-text */
'use client';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTrigger,
} from '@/components/ui/dialog';
import CreateFileForm from '@/elements/files/create-file.form';
import { trpc } from '@/providers/Providers';
import { DialogTitle } from '@radix-ui/react-dialog';
import {
  Download,
  FileArchive,
  FileAudio2,
  FileText,
  FileVideo,
  Folder,
  Image,
  Trash,
  UploadCloudIcon,
} from 'lucide-react';
import { RouterOutput } from '../../../../shared';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/ui/context-menu';
import { downloadBase64File, toastPromise } from '@/lib/utils';
import { MimeTypes } from '../../../../shared/types/File';
import { useSession } from 'next-auth/react';

// @ts-ignore
const fileIcons: Record<MimeTypes, JSX.Element> = {
  [MimeTypes.PDF]: (
    <FileText className='size-14 text-amber-500' strokeWidth={1} />
  ),
  [MimeTypes.JPEG]: (
    <Image className='size-14 text-amber-500' strokeWidth={1} />
  ),
  // @ts-ignore
  [MimeTypes.JPG]: <Image className='size-14 text-amber-500' strokeWidth={1} />,
  [MimeTypes.GIF]: <Image className='size-14 text-amber-500' strokeWidth={1} />,
  [MimeTypes.PNG]: <Image className='size-14 text-amber-500' strokeWidth={1} />,
  [MimeTypes.ZIP]: (
    <FileArchive className='size-14 text-amber-500' strokeWidth={1} />
  ),
  [MimeTypes.ZIP_COMPRESSED]: (
    <FileArchive className='size-14 text-amber-500' strokeWidth={1} />
  ),
  [MimeTypes.MP3]: (
    <FileAudio2 className='size-14 text-amber-500' strokeWidth={1} />
  ),
  [MimeTypes.MP4]: (
    <FileVideo className='size-14 text-amber-500' strokeWidth={1} />
  ),
};

function File({
  _id,
  base64,
  mimeType,
  name,
  handleDownload,
  ext,
  handleDelete,
}: RouterOutput['file']['getFiles'][number] & {
  handleDelete: (id: string) => void;
  handleDownload: (base64: string, filename: string, mime: string) => void;
}) {
  return (
    <div className='border p-4 rounded-lg border-dashed'>
      <ContextMenu>
        <ContextMenuTrigger>
          <div className='flex flex-col items-center justify-center'>
            {fileIcons[mimeType as MimeTypes] ? (
              fileIcons[mimeType as MimeTypes]
            ) : (
              <Folder className='size-14 text-amber-500' strokeWidth={1} />
            )}
            <div className='line-clamp-1 text-center text-sm mt-3'>{name}</div>
          </div>
        </ContextMenuTrigger>
        <ContextMenuContent>
          <div className='w-[300px]'>
            <p className='p-1 px-3 font-medium'>{name}</p>
            <div className='border-b' />
          </div>
          <ContextMenuItem asChild>
            <p className='mt-4' onClick={() => handleDelete(String(_id))}>
              <Trash className='size-4 mr-2' /> delete
            </p>
          </ContextMenuItem>
          <ContextMenuItem asChild>
            <p
              className='mt-4'
              onClick={() => handleDownload(base64, `${name}.${ext}`, mimeType)}
            >
              <Download className='size-4 mr-2' /> download
            </p>
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    </div>
  );
}

export default function Files() {
  const { data: session } = useSession();

  const files = trpc.file.getFiles.useQuery(
    { uploadedBy: session?.user._id as string },
    { enabled: !!session?.user._id }
  );

  const deleteFile = trpc.file.deleteFile.useMutation();
  const handleDelete = (id: string) => {
    toastPromise({
      asyncFunc: deleteFile.mutateAsync({ fileId: id }),
      success: 'deleted successfully',
      onSuccess() {
        files.refetch();
      },
    });
  };

  return (
    <div className='container'>
      <div className='flex justify-end'>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <UploadCloudIcon className='size-4 mr-2' />
              Upload File
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create File</DialogTitle>
            </DialogHeader>

            <CreateFileForm />
          </DialogContent>
        </Dialog>
      </div>
      <div className='grid grid-cols-10 gap-4'>
        {files.data?.map((_) => (
          <File
            key={String(_._id)}
            {..._}
            handleDelete={handleDelete}
            handleDownload={downloadBase64File}
          />
        ))}
      </div>
    </div>
  );
}
