import type { Metadata } from 'next';
import { Montserrat } from 'next/font/google';
import './globals.css';
import Providers from '@/providers/Providers';
import { Toaster } from 'react-hot-toast';
import { getServerSession } from 'next-auth';
import SessionProvider from '../providers/SessionProvider';
import NavBar from '@/elements/Navbar';
import { BreadCrumb } from '@/elements/Breadcrumb';
import { useSession } from 'next-auth/react';
import Warning from '@/elements/Warning';
import { cn } from '@/lib/utils';
import { ThemeProvider } from '@/components/theme/themeprovider';

const inter = Montserrat({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Vendor AI',
  description: '',
};
export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession();

  return (
    <html lang='en' suppressHydrationWarning>
      <body
        className={cn(inter.className, 'relative bg-sky-950')}
        // style={{
        //   backgroundImage: `url(/images/imagebg.png)`,
        //   backgroundSize: 'cover',
        //   backgroundRepeat: 'no-repeat',
        //   backgroundPosition: 'center',
        // }}
      >
        {' '}
        <SessionProvider session={session}>
          <Providers>
            <Toaster position='top-right' />
            <NavBar />
            <main className='flex flex-col flex-1 min-h-[calc(100vh-110px)] '>
              <div className='flex flex-col flex-1'>{children}</div>
            </main>
          </Providers>
        </SessionProvider>
      </body>
    </html>
  );
}
