"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createTRPCReact, httpBatchLink, loggerLink } from "@trpc/react-query";
import { AppRouter } from "../../shared";
import { getSession } from "next-auth/react";
import SuperJSO<PERSON> from "superjson";
import NextAdapterApp from "next-query-params/app";
import { QueryParamProvider } from "use-query-params";
import SessionProvider from "../providers/SessionProvider";
import { TooltipProvider } from "@/components/ui/tooltip";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";
import useSession from "@/hooks/useSession";

// @ts-ignore
export const trpc = createTRPCReact<AppRouter>();

// trpc client
const trpcClient = trpc.createClient({
  links: [
    loggerLink({
      enabled: (opts) =>
        (process.env.NODE_ENV === "development" &&
          typeof window !== "undefined") ||
        (opts.direction === "down" && opts.result instanceof Error),
    }),
    httpBatchLink({
      url: process.env.NEXT_PUBLIC_TRPC_URL!,
      headers: async () => {
        const session = await getSession();
        return { "x-auth-token": session?.user.token };
      },
    }),
  ],
  transformer: SuperJSON,
});
// query client
const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      onError: (err) => {
        // if (err instanceof Error) toast.error(err.message);
      },
    },
  },
});

export default function Providers({ children }: React.PropsWithChildren) {
  useSession();
  return (
    <QueryParamProvider
      adapter={NextAdapterApp}
      options={{ updateType: "replace" }}
    >
      <trpc.Provider client={trpcClient} queryClient={queryClient}>
        <QueryClientProvider client={queryClient}>
          <GoogleReCaptchaProvider
            reCaptchaKey={process.env.NEXT_PUBLIC_GOOGLE_RECAPTCHA!}
          >
            <TooltipProvider delayDuration={100}>{children}</TooltipProvider>
          </GoogleReCaptchaProvider>
        </QueryClientProvider>
      </trpc.Provider>
    </QueryParamProvider>
  );
}
