import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';
import { uploadAssignmentFilevalidator } from '../../../shared/validators/assessment.validator';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { trpc } from '@/providers/Providers';
import { toastPromise } from '@/lib/utils';
import { useState } from 'react';

export type ComplianceForm = z.infer<typeof uploadAssignmentFilevalidator>;

export type ComplianceFormProps = {
  methods: UseFormReturn<ComplianceForm>;
  onSubmit: SubmitHandler<ComplianceForm>;
  assessmentId: string;
  assignmentIndex: number;
};

export const useComplianceForm = () => {
  const [success, setSuccess] = useState(false);

  const [successCompleted, setSuccessCompleted] = useState(true);

  const uploadAssignmentFile =
    trpc.assessment.uploadAssignmentFile.useMutation();
  const methods = useForm<ComplianceForm>({
    defaultValues: { assessmentId: '' },
    resolver: zodResolver(uploadAssignmentFilevalidator),
  });

  const onSubmit: SubmitHandler<ComplianceForm> = (data) => {
    toastPromise({
      asyncFunc: uploadAssignmentFile.mutateAsync(data),
      success: 'file uploaded succesfully',
      onSuccess() {
        setSuccess(true);
        setSuccessCompleted(false);
      },
    });
  };

  return { methods, success, onSubmit, successCompleted };
};
