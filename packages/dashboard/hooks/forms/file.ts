import { UseFormReturn, useForm } from 'react-hook-form';
import { createFileValidator } from '../../../shared/validators/file.validator';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { FileExtensions, MimeTypes } from '../../../shared/types/File';

export type FileForm = z.infer<typeof createFileValidator>;
export type CreateFileFormProps = { methods: UseFormReturn<FileForm> };

export const useFileMethods = () =>
  useForm<FileForm>({
    resolver: zodResolver(createFileValidator),
    defaultValues: {
      base64: '',
      ext: '' as unknown as FileExtensions,
      name: '',
      mimeType: '' as unknown as MimeTypes,
    },
  });
