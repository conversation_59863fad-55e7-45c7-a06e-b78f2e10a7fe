import { UseFormReturn, useForm } from 'react-hook-form';
import { createCreditReportValidator } from '../../../shared/validators/credit-report.validator';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Quarter } from '../../../shared/types/CreditReport';

export type CreditReportForm = z.infer<typeof createCreditReportValidator>;
export type CreditReportFormProps = {
  methods: UseFormReturn<CreditReportForm>;
};

export const useCreditReportMethods = () =>
  useForm<CreditReportForm>({
    resolver: zodResolver(createCreditReportValidator),
    defaultValues: {
      date: '' as unknown as Date,
      quarter: '' as unknown as Quarter,
      rating: '' as unknown as number,
      remarks: '',
      vendor: '',
    },
  });
