import { z } from 'zod';
import { updateVulnerabilityResolveStatusByEmployeeValidator } from '../../../shared/validators/assessment-submission.validator';
import { SubmitHandler, UseFormReturn, useForm } from 'react-hook-form';
import { VulnerabilityResolveStatus } from '../../../shared/types/AssessmentSubmission';
import { zodResolver } from '@hookform/resolvers/zod';

export type EmployeeResolveOrExceptionForm = z.infer<
  typeof updateVulnerabilityResolveStatusByEmployeeValidator
>;

export type EmployeeResolveOrExceptionFormProps = {
  methods: UseFormReturn<EmployeeResolveOrExceptionForm>;
  handleSubmit: SubmitHandler<EmployeeResolveOrExceptionForm>;
};

export const useEmployeeResolveOrExceptionFormMethods = () =>
  useForm<EmployeeResolveOrExceptionForm>({
    resolver: zodResolver(updateVulnerabilityResolveStatusByEmployeeValidator),
    defaultValues: {
      resolveDescription: '',
      resolveStatus: '' as unknown as VulnerabilityResolveStatus,
      submissionId: '',
    },
  });
