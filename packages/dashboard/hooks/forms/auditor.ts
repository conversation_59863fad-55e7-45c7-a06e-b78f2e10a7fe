import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, UseFormReturn, useForm } from 'react-hook-form';
import { z } from 'zod';
import { manageAuditorScoreValdator } from '../../../shared/validators/assessment-submission.validator';
import { AuditorScore } from '../../../shared/types/AssessmentSubmission';

// for Auditor
export type AuditorForm = z.infer<typeof manageAuditorScoreValdator>;
export type AuditorFormProps = {
  methods: UseFormReturn<AuditorForm>;
  handleSubmit: SubmitHandler<AuditorForm>;
};

export const useAuditorScoreForm = () => {
  return useForm<AuditorForm>({
    resolver: zodResolver(manageAuditorScoreValdator),
    defaultValues: {
      submissionId: '',
      remarks: '',
      score: '',
    },
  });
};
