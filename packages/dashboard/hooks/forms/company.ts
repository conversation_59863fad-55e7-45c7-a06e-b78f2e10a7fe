import { zodResolver } from '@hookform/resolvers/zod';
import {
  manageCompanyValidator,
  manageVendorValidator,
} from '../../../shared/validators/company.validator';
import {
  UseFieldArrayReturn,
  UseFormProps,
  UseFormReturn,
  useFieldArray,
  useForm,
} from 'react-hook-form';
import { CompanyType } from '../../../shared/types/Company';
import { z } from 'zod';

/**Mange company Form */
export type MangeCompanyForm = z.infer<typeof manageCompanyValidator>;
export type ManageCompanyFormProps = {
  methods: UseFormReturn<MangeCompanyForm>;
  fieldArray: UseFieldArrayReturn<MangeCompanyForm, 'primaryContact'>;
  type: CompanyType;
};

/**Manage Company Form*/
export const useManageCompanyForm = () => {
  const methods = useForm<MangeCompanyForm>({
    resolver: zodResolver(manageCompanyValidator),
    defaultValues: {
      type: CompanyType.COMPANY,
      primaryContact: [
        {
          countryCode: '',
          email: '',
          phone: '',
          firstName: '',
          lastName: '',
          title: '',
        },
      ],
    },
  });

  const fieldArray = useFieldArray({
    control: methods.control,
    name: 'primaryContact',
  });

  return { methods, fieldArray };
};

// Manage Vendor Form
export type ManageVendorForm = z.infer<typeof manageVendorValidator>;

export type ManageVendorFormProps = {
  methods: UseFormReturn<ManageVendorForm>;
  criticalityFieldArray: UseFieldArrayReturn<
    ManageVendorForm,
    'criticalityLevels'
  >;
  vendorAssesmentFieldArray: UseFieldArrayReturn<
    ManageVendorForm,
    'vendorAssessmentManager'
  >;
  engagementManagerFieldArray: UseFieldArrayReturn<
    ManageVendorForm,
    'companyEngagementManager'
  >;
  type: CompanyType;
};

export const useManageVendorForm = () => {
  const methods = useForm<ManageVendorForm>({
    resolver: zodResolver(manageVendorValidator),
    defaultValues: {
      criticalityLevels: [{ timeDuration: undefined, level: undefined }],
      vendorAssessmentManager: [{ countryCode: '', email: '', phone: '' }],
      companyEngagementManager: [{ countryCode: '', email: '', phone: '' }],
      type: CompanyType.VENDOR,
    },
  });

  const criticalityFieldArray = useFieldArray({
    control: methods.control,
    name: 'criticalityLevels',
  });
  const vendorAssesmentFieldArray = useFieldArray({
    control: methods.control,
    name: 'vendorAssessmentManager',
  });
  const engagementManagerFieldArray = useFieldArray({
    control: methods.control,
    name: 'companyEngagementManager',
  });

  return {
    methods,
    criticalityFieldArray,
    vendorAssesmentFieldArray,
    engagementManagerFieldArray,
  };
};
