import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { SubmitHandler, UseFormReturn, useForm } from "react-hook-form";
import { CRITICALITY_LEVELS } from "../../../shared/types/Company";
import { z } from "zod";
import {
  manageVulnerabilityScoreValidator,
  vendorAcceptanceValidator,
} from "../../../shared/validators/assessment-submission.validator";
import { VendorAcceptanceStatus } from "../../../shared/types/AssessmentSubmission";
import { RouterOutput } from "../../../shared";

// Soc analyst score-form

export type VulnerabilityForm = z.infer<
  typeof manageVulnerabilityScoreValidator
>;

export type VulnerabilityFormProps = {
  methods: UseFormReturn<VulnerabilityForm>;
  handleSubmit: SubmitHandler<VulnerabilityForm>;
  handleSocSubmitAndEmail: SubmitHandler<VulnerabilityForm>;
  assessment: string;
  vendor: string;
  company: string;
  asset?: string;
};
export const useVulnerabilityScoreForm = () =>
  useForm<VulnerabilityForm>({
    resolver: zodResolver(manageVulnerabilityScoreValidator),
    defaultValues: {
      remarks: "",
      score: "",
      submissionId: "",
      criticalityLevel: CRITICALITY_LEVELS.VERYLOW,
      cvssScore: "",
      cvssComparison: "",
    },
  });

/**Vendor acceptance validator */
export type VendorAcceptanceForm = z.infer<typeof vendorAcceptanceValidator>;
export type VendorAcceptanceFormProps = {
  methods: UseFormReturn<VendorAcceptanceForm>;
};

export const useVendorAcceptanceForm = () =>
  useForm<VendorAcceptanceForm>({
    resolver: zodResolver(vendorAcceptanceValidator),
    defaultValues: {
      submissionId: "",
      vendorAcceptanceStatus: "" as unknown as VendorAcceptanceStatus,
      vendorRejectReason: "",
      rejectionEvidence: "",
    },
  });
