import { z } from 'zod';
import { manageQuestionsValidator } from '../../../shared/validators/question.validator';
import { UseFormReturn, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { QUESTIONS_WEIGHTAGE } from '../../../shared/types/Question';

export type ManageQuestionsForm = z.infer<typeof manageQuestionsValidator>;
export type ManageQuestionsFormProps = {
  methods: UseFormReturn<ManageQuestionsForm>;
};

export const useManageQuestionMethods = () =>
  useForm<ManageQuestionsForm>({
    resolver: zodResolver(manageQuestionsValidator),
    defaultValues: {
      questionId: '',
      section: '',
      standard: '',
      subSection: '',
      file: '',
      canAttachDocument: false,
      weightage: QUESTIONS_WEIGHTAGE.A,
      input: {
        inputType: 'text',
        label: '',
        placeholder: '',
        schema: { max: undefined, min: undefined, required: true },
      },
    },
  });
