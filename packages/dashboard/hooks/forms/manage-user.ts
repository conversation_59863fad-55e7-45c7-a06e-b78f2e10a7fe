import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { manageUserValidator } from '../../../shared/validators/user.validator';
import { USER_ROLE } from '../../../shared/types/User';

/**Manage user form */

export type MangeUserForm = z.infer<typeof manageUserValidator>;

export const useMangeUserForm = () => {
  const methods = useForm<MangeUserForm>({
    resolver: zodResolver(manageUserValidator),
    defaultValues: {
      email: '',
      password: '',
      role: '' as USER_ROLE,
      companies: [],
      contact: {
        countryCode: '',
        firstName: '',
        lastName: '',
        phone: '',
        title: '',
      },
    },
  });

  return { methods };
};
