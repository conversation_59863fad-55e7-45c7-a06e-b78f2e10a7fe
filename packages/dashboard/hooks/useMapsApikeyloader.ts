import { useJsApiLoader, Libraries } from "@react-google-maps/api";

const libraries: Libraries = [
  "geometry",
  "drawing",
  "maps",
  "marker",
  "routes",
  "core",
  "geocoding",
  "places",
];

export default function useMapsApiKeyLoader() {
  return useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: process.env.NEXT_PUBLIC_MAPS_API!,
    libraries,
    language: "en",
  });
}
