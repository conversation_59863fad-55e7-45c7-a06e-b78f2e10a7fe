import {
  Business_Credit_sub_rating,
  CreditRatingCategory,
  Employee_satisfaction_sub_rating,
  Quarter,
  Socialmedia_sub_rating,
} from '../../../shared/types/CreditReport';
import { getCreditReportValidator } from '../../../shared/validators/credit-report.validator';
import { UseFormReturn, useForm } from 'react-hook-form';
import { z } from 'zod';

export type GetCreditReportFilter = z.infer<typeof getCreditReportValidator>;
export type GetCreditReportFilterProps = {
  methods: UseFormReturn<GetCreditReportFilter>;
};

export const useGetCreditReportFilterMethods = () =>
  useForm<GetCreditReportFilter>({
    defaultValues: {
      vendor: '',
      category: '' as unknown as CreditRatingCategory,
      fromDate: new Date(),
      toDate: new Date(),
      subCategory: '' as unknown as
        | Business_Credit_sub_rating
        | Employee_satisfaction_sub_rating
        | Socialmedia_sub_rating,
    },
  });
