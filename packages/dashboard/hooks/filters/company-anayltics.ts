import { UseFormReturn, useForm } from 'react-hook-form';
import { Quarter } from '../../../shared/types/CreditReport';
import { z } from 'zod';
import { RouterOutput } from '../../../shared';

export type CompanyAnalyticsFilter = { vendor: string; quarter?: Quarter };

export type CompanyAnalyticsFilterProps = {
  methods: UseFormReturn<CompanyAnalyticsFilter>;
  vendors: RouterOutput['company']['getCompanies'];
};

export const useCompanyAnalyticsFilter = () =>
  useForm<CompanyAnalyticsFilter>({
    defaultValues: {
      quarter: '' as unknown as Quarter,
      vendor: '',
    },
  });
