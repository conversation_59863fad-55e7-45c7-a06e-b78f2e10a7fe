import { useSession } from 'next-auth/react';
import { USER_ROLE } from '../../shared/types/User';

export const useRole = () => {
  const { data: session } = useSession();
  const isAuditor = session?.user.role === USER_ROLE.AUDITOR;
  const isCompany = session?.user.role === USER_ROLE.COMPANY;
  const isCompanyAdmin = session?.user.role === USER_ROLE.COMPANY_ADMIN;
  const isEmployee = session?.user.role === USER_ROLE.EMPLOYEE;
  const isLeadSoc = session?.user.role === USER_ROLE.LEAD_SOC_ANALYST;
  const isSoc = session?.user.role === USER_ROLE.SOC_ANALYST;
  const isSuperAdmin = session?.user.role === USER_ROLE.SUPERADMIN;
  const isVendor = session?.user.role === USER_ROLE.VENDOR;

  return {
    isAuditor,
    isCompany,
    isCompany<PERSON>dmin,
    isEmployee,
    isLead<PERSON>oc,
    isSoc,
    isSuper<PERSON>dmin,
    isVendor,
  };
};
