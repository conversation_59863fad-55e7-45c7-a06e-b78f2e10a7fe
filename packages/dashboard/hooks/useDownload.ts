import { downloadBase64File, toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import toast from 'react-hot-toast';

export const useDownload = () => {
  const dowloadFile = trpc.file.getFile.useMutation();

  return ({ fileId }: { fileId?: string }) => {
    if (!fileId) return toast.error('file not found');

    toastPromise({
      asyncFunc: dowloadFile.mutateAsync({ fileId }),
      success: 'file downloaded successfully',
      onSuccess(data) {
        downloadBase64File(data.base64, data.name, data.mimeType);
      },
    });
  };
};
