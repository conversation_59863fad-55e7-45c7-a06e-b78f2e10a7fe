import userAtom from '@/atoms/userAtom';
import { trpc } from '@/providers/Providers';
import { useAtom } from 'jotai';

export const useMe = () => trpc.user.me.useQuery(undefined, { retry: false });

// export const useMe = () => {
//   const [user, setUser] = useAtom(userAtom);
//   trpc.user.me.useQuery(undefined, {
//     onSuccess: (data) => {
//       console.log(data);
//       // @ts-expect-error
//       setUser((prevData) => {
//         return { ...prevData, ...data };
//       });
//     },
//   });
// };
