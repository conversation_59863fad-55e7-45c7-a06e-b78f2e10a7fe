import { useState } from "react";

type Props = {
  maxSteps: number;
  defaultActiveStep?: number;
  onEndReached?: (step: number) => void;
};
export default function useStepper({
  maxSteps,
  defaultActiveStep,
  onEndReached,
}: Props) {
  const [activeStep, setActiveStep] = useState(defaultActiveStep || 1);
  const next = () => setActiveStep((_) => Math.min(maxSteps, _ + 1));
  const back = () => {
    setActiveStep((_) => {
      const step = Math.max(1, _ - 1);
      if (step === 1) onEndReached?.(step);
      return step;
    });
  };

  return { activeStep, next, back };
}
