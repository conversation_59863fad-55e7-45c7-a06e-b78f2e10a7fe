{"name": "fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@blocknote/core": "^0.14.0", "@blocknote/mantine": "^0.14.0", "@blocknote/react": "^0.14.0", "@custom-react-hooks/use-idle": "^1.4.19", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@react-google-maps/api": "^2.19.2", "@rive-app/canvas": "^2.11.1", "@rive-app/react-canvas": "^4.8.3", "@tanstack/react-query": "^4.0.0", "@tanstack/react-table": "^8.17.3", "@tiptap/pm": "^2.4.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@trpc/client": "^10.44.1", "@trpc/react": "^9.27.4", "@trpc/react-query": "^10.44.1", "@trpc/server": "^10.44.1", "@types/lodash": "^4.14.202", "add": "^2.0.6", "apexcharts": "^3.49.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "file-saver": "^2.0.5", "framer-motion": "^11.2.11", "geolib": "^3.3.4", "jotai": "^2.8.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.407.0", "next": "14.0.3", "next-auth": "^4.24.7", "next-query-params": "^5.0.0", "next-themes": "^0.3.0", "qrcode": "^1.5.3", "react": "^18", "react-apexcharts": "^1.4.1", "react-day-picker": "^8.9.1", "react-dom": "^18", "react-google-recaptcha": "^3.1.0", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.52.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-input-mask": "^2.0.4", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.3", "react-select-country-list": "^2.2.3", "react-time-input": "^1.0.1", "recharts": "^2.12.7", "separator": "^0.1.0", "shadcn-ui": "^0.8.0", "sharp": "0.33.1", "superjson": "1.13.3", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "use-query-params": "^2.2.1", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^18", "@types/react-big-calendar": "^1.8.8", "@types/react-dom": "^18", "@types/react-select-country-list": "^2.2.3", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}