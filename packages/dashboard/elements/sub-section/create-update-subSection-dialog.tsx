'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Close,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  createSubSectionValidator,
  updateSubSectionValidator,
} from '../../../shared/validators/standard.validator';
import FormField from '@/components/form/FormField';
import { FormTextField } from '@/components/form/FormTextField';
import { FormProvider, useForm } from 'react-hook-form';
import { toastPromise } from '@/lib/utils';
import { Edit } from 'lucide-react';
import { useMemo, useState } from 'react';
import { RouterInput } from '../../../shared';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { trpc } from '@/providers/Providers';
import { FormSelect } from '@/components/form/FormSelect';
import { StringParam, useQueryParam, withDefault } from 'use-query-params';

type CreateForm = z.infer<typeof createSubSectionValidator>;
type UpdateForm = z.infer<typeof updateSubSectionValidator>;

export const CreateUpdateSubSectionDialog = ({
  update: update,
  defaultSection,
}: {
  update?: RouterInput['standard']['updateSubSection'];
  defaultSection?: string;
}) => {
  const [open, setOpen] = useState(false);

  const sections = trpc.standard.getSections.useQuery();

  const createMethods = useForm<CreateForm>({
    defaultValues: { description: '', subSection: '', section: '' },
    resolver: zodResolver(createSubSectionValidator),
  });

  const updateMethods = useForm<UpdateForm>({
    defaultValues: update,
    resolver: zodResolver(updateSubSectionValidator),
  });

  const createSubSectionMutation = trpc.standard.createSubSection.useMutation();
  const updateSubSectionMutation = trpc.standard.updateSubSection.useMutation();

  const utils = trpc.useUtils();
  const handleSubmit = update
    ? updateMethods.handleSubmit((data) => {
        toastPromise({
          asyncFunc: updateSubSectionMutation.mutateAsync(data),
          success: 'SubSection updated successfully',
          onSuccess: () => {
            updateMethods.reset();
            utils.standard.invalidate();
            setOpen(false);
          },
        });
      })
    : createMethods.handleSubmit((data) => {
        toastPromise({
          asyncFunc: createSubSectionMutation.mutateAsync(data),
          success: 'SubSection Created successfully',
          onSuccess: () => {
            createMethods.reset();
            utils.standard.invalidate();
            setOpen(false);
          },
        });
      });

  const selectOptions = useMemo(
    () =>
      (sections.data || []).map((s) => {
        return { value: String(s._id), label: s.section };
      }),
    [sections.data]
  );

  return (
    <Dialog
      open={open}
      onOpenChange={(v) => {
        defaultSection && createMethods.setValue('section', defaultSection);
        setOpen(v);
      }}
    >
      <DialogTrigger asChild>
        <Button>
          {!update ? (
            'Create Sub-Section'
          ) : (
            <>
              <Edit className='w-4 h-4 mr-2' />
              Update
            </>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {update ? 'Update Sub-Section' : 'Create Sub_section'}
          </DialogTitle>
        </DialogHeader>

        <form className='space-y-3'>
          {/* @ts-ignore */}
          <FormProvider {...(update ? updateMethods : createMethods)}>
            <FormSelect
              disabled={!!update}
              name='section'
              label='Section'
              placeholder='Select section'
              showSearch
              options={selectOptions}
            />

            <FormField
              name='subSection'
              label='Sub-Section'
              placeholder='Enter Sub-section'
            />
            <FormTextField
              name='description'
              label='Description'
              placeholder='Enter Description'
            />
          </FormProvider>
        </form>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant='destructive'>Close</Button>
          </DialogClose>
          <Button onClick={handleSubmit}>
            {update ? 'Update Sub-Section' : 'Create Sub-Section'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
