'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  createStandardValidator,
  updateStandardValidator,
} from '../../../shared/validators/standard.validator';
import { z } from 'zod';
import { FormProvider, useForm } from 'react-hook-form';
import { trpc } from '@/providers/Providers';
import { toastPromise } from '@/lib/utils';
import FormField from '@/components/form/FormField';
import { FormTextField } from '@/components/form/FormTextField';
import { zodResolver } from '@hookform/resolvers/zod';
import { RouterInput } from '../../../shared';
import { useState } from 'react';
import { Edit } from 'lucide-react';

type CreateForm = z.infer<typeof createStandardValidator>;
type UpdateForm = z.infer<typeof updateStandardValidator>;

export default function CreateUpdateStandardDialog({
  update: update,
}: {
  update?: RouterInput['standard']['updateSubSection'];
}) {
  const [open, setOpen] = useState(false);

  const createMethods = useForm<CreateForm>({
    defaultValues: { description: '', standard: '' },
    resolver: zodResolver(createStandardValidator),
  });

  const updateMethods = useForm<UpdateForm>({
    defaultValues: update,
    resolver: zodResolver(updateStandardValidator),
  });

  const createStandardMutation = trpc.standard.createStandard.useMutation();
  const updateStandardMutation = trpc.standard.updateStandard.useMutation();

  const utils = trpc.useUtils();

  const handleSubmit = update
    ? updateMethods.handleSubmit((data) => {
        toastPromise({
          asyncFunc: updateStandardMutation.mutateAsync(data),
          success: 'Standard updated successfully',
          onSuccess: () => {
            updateMethods.reset();
            utils.standard.invalidate();
            setOpen(false);
          },
        });
      })
    : createMethods.handleSubmit((data) => {
        toastPromise({
          asyncFunc: createStandardMutation.mutateAsync(data),
          success: 'Standard created successfully',
          onSuccess: () => {
            createMethods.reset();
            utils.standard.invalidate();
            setOpen(false);
          },
        });
      });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          {!update ? (
            'Create Compliance Standard'
          ) : (
            <>
              <Edit className='w-4 h-4 mr-2' />
              Update
            </>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create Compliance Standard</DialogTitle>
        </DialogHeader>

        <form className='space-y-3'>
          {/* @ts-ignore */}
          <FormProvider {...(update ? updateMethods : createMethods)}>
            <FormField
              name='standard'
              label='Standard'
              placeholder='Enter standard'
            />
            <FormTextField
              name='description'
              label='Description'
              placeholder='Enter Description'
            />
          </FormProvider>
        </form>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant='destructive'>Close</Button>
          </DialogClose>
          <Button onClick={handleSubmit}>
            {update ? 'update standard' : 'create standard'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
