'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { trpc } from '@/providers/Providers';
import { Edit2 } from 'lucide-react';
import { RouterOutput } from '../../../shared';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useState } from 'react';

type Props = {
  onClick?: (
    question: RouterOutput['questions']['getQuestions'][number]
  ) => void;
};

export default function QuestionsDataGrid({ onClick }: Props) {
  const [selectedSection, setSelectedSection] = useState<string>('');
  const questions = trpc.questions.getQuestions.useQuery();

  const sections = trpc.standard.getSections.useQuery();

  const data = selectedSection
    ? questions.data?.filter((q) => q.section.section === selectedSection)
    : questions.data;

  return (
    <div>
      <div className='flex gap-2 my-5'>
        <Select onValueChange={(value) => setSelectedSection(value)}>
          <SelectTrigger className='w-[180px]'>
            <SelectValue placeholder='Section' />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {sections?.data?.map((s, idx) => {
                return (
                  <SelectItem value={s.section} key={String(s._id)}>
                    {s.section}
                  </SelectItem>
                );
              })}
            </SelectGroup>
          </SelectContent>
        </Select>
        <Button variant={'destructive'} onClick={() => setSelectedSection('')}>
          Reset
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>No</TableHead>
            <TableHead>Section</TableHead>
            <TableHead>Domain</TableHead>
            <TableHead>Audit Question</TableHead>
            <TableHead>Input type</TableHead>
            <TableHead>File Attachment</TableHead>
            <TableHead>Answers</TableHead>
            <TableHead>Compliance</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.map((question, idx) => {
            return (
              <TableRow key={String(question._id)}>
                <TableCell>{idx + 1}</TableCell>

                <TableCell>{question.section.section}</TableCell>
                <TableCell className='capitalize'>
                  {question.section.sectionType}
                </TableCell>
                <TableCell>{question.input.label}</TableCell>
                <TableCell className='capitalize'>
                  {question.input.inputType}
                </TableCell>

                <TableCell>
                  {question.canAttachDocument ? 'Required' : 'Not Required'}
                </TableCell>

                <TableCell className='uppercase'>
                  {/* @ts-ignore */}
                  {String(question.input.answer ?? '-')}
                </TableCell>
                <TableCell>
                  {Array.isArray(question.complience)
                    ? question.complience.map((complience, index) => (
                        <div key={index}>{complience.toUpperCase()},</div>
                      ))
                    : null}
                </TableCell>
                <TableCell>
                  <Button onClick={() => onClick?.(question)}>
                    <Edit2 className='size-4 mr-2' />
                    Edit
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
