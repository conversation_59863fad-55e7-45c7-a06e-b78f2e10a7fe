"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form<PERSON>rovider } from "react-hook-form";
import <PERSON>Field from "@/components/form/FormField";
import { FormSelect } from "@/components/form/FormSelect";
import { Label } from "@/components/ui/label";
import { FormCheckBox } from "@/components/form/FormCheckBox";
import { trpc } from "@/providers/Providers";
import { selectOptionsParser, toastPromise } from "@/lib/utils";
import FormChipInput from "@/components/form/FormChipInput";
import { QUESTIONS_WEIGHTAGE } from "../../../shared/types/Question";
import { ManageQuestionsFormProps } from "@/hooks/forms/questions";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FormSwitch } from "@/components/form/FormSwitch";
import { FormComboBox } from "@/components/form/FormComboBox";
import { FormComboBoxPopover } from "@/components/form/FormComboPopover";
import { FormCheckBoxGroup } from "@/components/form/FormCheckboxGroup";
import Form<PERSON>ilePicker from "@/components/form/FormFilePicker";

export default function ManageQuestionForm({
  methods,
}: ManageQuestionsFormProps) {
  const standards = trpc.standard.getStandards.useQuery();
  const sections = trpc.standard.getSections.useQuery(
    { standard: methods.watch("standard") },
    { enabled: !!methods.watch("standard") }
  );
  const subSections = trpc.standard.getSubSections.useQuery(
    { section: methods.watch("section") },
    { enabled: !!methods.watch("section") }
  );

  const inputType = methods.watch("input.inputType");

  const createQuestion = trpc.questions.manageQuestion.useMutation();

  const utils = trpc.useUtils();

  const handleCreateQuestion = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: createQuestion.mutateAsync(data),
      success: "question created successfully",
      onSuccess: () => {
        methods.reset();
        utils.questions.getQuestions.invalidate();
      },
    });
  });

  const selectAnswerOptions = methods.watch("input.options");

  return (
    <ScrollArea className="ph1 ">
      <form
        className="space-y-4 p-6 rounded-lg bg-white "
        onSubmit={handleCreateQuestion}
      >
        <FormProvider {...methods}>
          <div>
            <label className="font-semibold   ">Select Standard</label>
            <FormSelect
              name="standard"
              showSearch
              placeholder="Select Standard"
              options={selectOptionsParser(standards.data, {
                labelKey: "standard",
                valKey: "_id",
              })}
            />
          </div>
          <div>
            <label className="font-semibold   ">Select Section</label>
            <FormSelect
              name="section"
              showSearch
              placeholder="Select Section"
              options={selectOptionsParser(sections.data, {
                labelKey: "section",
                valKey: "_id",
              })}
            />
          </div>
          <div>
            <label className="font-semibold   ">Select Sub Section</label>
            <FormSelect
              name="subSection"
              showSearch
              placeholder="Select Sub Section"
              options={selectOptionsParser(subSections.data, {
                labelKey: "subSection",
                valKey: "_id",
              })}
            />
          </div>

          <div>
            <label className="font-semibold   ">Question</label>
            <FormField name="input.label" placeholder="Enter your question" />
          </div>

          <div>
            <label className="font-semibold   ">
              Question difficulty level
            </label>
            <FormSelect
              name="weightage"
              placeholder="Select Question Level"
              options={[
                { label: QUESTIONS_WEIGHTAGE.A, value: QUESTIONS_WEIGHTAGE.A },
                { label: QUESTIONS_WEIGHTAGE.B, value: QUESTIONS_WEIGHTAGE.B },
                { label: QUESTIONS_WEIGHTAGE.C, value: QUESTIONS_WEIGHTAGE.C },
                { label: QUESTIONS_WEIGHTAGE.D, value: QUESTIONS_WEIGHTAGE.D },
              ]}
            />
          </div>
          <div>
            <label className="font-semibold   ">Select Question Type</label>
            <FormSelect
              name="input.inputType"
              options={[
                {
                  label: "Descriptive",
                  value: "text",
                },
                {
                  label: "Select",
                  value: "select",
                },
                {
                  label: "Radio",
                  value: "radio",
                },
                {
                  label: "Checkbox",
                  value: "checkbox",
                },
              ]}
              placeholder="Select type of input"
            />
          </div>

          {/* Text Input */}
          {inputType === "text" && (
            <>
              {/* Schema */}
              <div className="bg-neutral-50 p-4 border rounded-lg shadow-sm">
                <div className="space-y-2">
                  <FormCheckBox
                    name="input.schema.required"
                    label="Required?"
                    description="If not required un-check this"
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <FormField
                      name="input.schema.min.val"
                      type="number"
                      label="Min characters"
                      placeholder="Enter min characters"
                    />
                    <FormField
                      name="input.schema.min.message"
                      label="Error Message"
                      placeholder="Enter error message"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <FormField
                      name="input.schema.max.val"
                      type="number"
                      label="Max characters"
                      placeholder="Enter max characters"
                    />
                    <FormField
                      name="input.schema.max.message"
                      label="Error Message"
                      placeholder="Enter error message"
                    />
                  </div>
                </div>
              </div>
            </>
          )}
          {/* Select Input */}
          {inputType === "select" && (
            <>
              <div className="bg-neutral-50 p-4 border rounded-lg shadow-sm">
                {/* Options */}
                <FormChipInput
                  name="input.options"
                  placeholder="Enter Options"
                  label="Enter Options"
                />
                {/* Schema */}
                <div className="space-y-2">
                  <FormCheckBox
                    name="input.schema.required"
                    label="Required?"
                    description="If not required un-check this"
                  />
                </div>
                {/* Answer */}
                <div>
                  {selectAnswerOptions && (
                    <FormSelect
                      name="input.answer"
                      label="Answer"
                      placeholder="Select Answer"
                      options={Object.values(selectAnswerOptions).map(
                        (selected) => ({
                          label: selected,
                          value: selected,
                        })
                      )}
                    />
                  )}
                </div>
              </div>
            </>
          )}
          {/* Radio Input */}
          {inputType === "radio" && (
            <>
              <div className="bg-neutral-50 p-4 border rounded-lg shadow-sm">
                {/* Options */}
                <FormChipInput
                  name="input.options"
                  placeholder="Enter Options"
                  label="Enter Options"
                />

                {/* Schema */}
                <div className="space-y-2">
                  <FormCheckBox
                    name="input.schema.required"
                    label="Required?"
                    description="If not required un-check this"
                  />
                </div>
                {/* Answer */}
                {selectAnswerOptions && (
                  <FormSelect
                    name="input.answer"
                    label="Answer"
                    placeholder="Select Answer"
                    options={Object.values(selectAnswerOptions).map(
                      (selected) => ({
                        label: selected,
                        value: selected,
                      })
                    )}
                  />
                )}
              </div>
            </>
          )}
          {/* Checkbox Input */}
          {inputType === "checkbox" && (
            <>
              <div className="bg-neutral-50 p-4 border rounded-lg shadow-sm">
                {/* Options */}
                <FormChipInput
                  name="input.options"
                  placeholder="Enter Options"
                  label="Enter Options"
                />

                {/* Schema */}
                <div className="space-y-2">
                  <FormCheckBox
                    name="input.schema.required"
                    label="Required?"
                    description="If not required un-check this"
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <FormField
                      name="input.schema.min.val"
                      type="number"
                      label="Min Options To Select"
                      placeholder="Enter min no.of options to select"
                    />
                    <FormField
                      name="input.schema.min.message"
                      label="Error Message"
                      placeholder="Enter error message"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <FormField
                      name="input.schema.max.val"
                      type="number"
                      label="Max Options To Select"
                      placeholder="Enter max no.of options to select"
                    />
                    <FormField
                      name="input.schema.max.message"
                      label="Error Message"
                      placeholder="Enter error message"
                    />
                  </div>
                  {/* Answer */}
                  <div>
                    {selectAnswerOptions && (
                      <FormComboBoxPopover
                        name="input.answer"
                        label="Answer"
                        placeholder="Select Answer"
                        options={Object.values(selectAnswerOptions).map(
                          (selected) => ({
                            label: selected,
                            value: selected,
                          })
                        )}
                      />
                    )}
                  </div>
                </div>
              </div>
            </>
          )}
          {/* Switch Input */}

          <div>
            <label className="font-semibold   ">Response instructions</label>
            <FormField
              name="input.placeholder"
              placeholder="Enter response instructions "
            />
          </div>
          <div>
            <label className="font-semibold   ">Attach File</label>
            <FormCheckBox
              name="canAttachDocument"
              description="unckeck if file attachment is not required"
            />
          </div>
          <div>
            <label className="font-semibold   ">
              Review answer/solution attachment
            </label>
            <FormField
              name="file"
              s3FilePath="questions"
              type="file"
              description="This file attachment is for review answer/solution"
            />
          </div>
          <div>
            <label className="font-semibold   ">Compliance Standard</label>
            <FormComboBoxPopover
              name="complience"
              label=""
              placeholder="Select compliences"
              options={[
                {
                  label:
                    "The Payment Card Industry Data Security Standard (PCI DSS)",
                  value: "PCI_DSS",
                },
                {
                  label:
                    "The International Organization for Standardization (ISO)",
                  value: "ISO_27001",
                },
                {
                  label:
                    "The Health Insurance Portability and Accountability Act (HIPAA)",
                  value: "HIPAA",
                },
                {
                  label: "The General Data Protection Regulation (GDPR)",
                  value: "GDPR",
                },
                { label: "Sarbanes-Oxley Act (SOX) 1", value: "SOX_1" },
                { label: "Sarbanes-Oxley Act (SOX)2", value: "SOX_2" },
                { label: "Gramm- Leach-Bliley Act (GLBA)", value: "GLBA" },
                {
                  label:
                    "The Federal Information Security Management Act (FISMA)",
                  value: "FISMA",
                },
                {
                  label: "NIST SP 800-53 Rev. 5",
                  value: "NIST SP 800-53 Rev. 5",
                },
                {
                  label: "ISO/IEC 27001:2022",
                  value: "ISO/IEC 27001:2022",
                },
                {
                  label: "CIS Controls v8",
                  value: "CIS Controls v8",
                },
                {
                  label: "COBIT 2019",
                  value: "COBIT 2019",
                },
                {
                  label: "HIPAA Security Rule",
                  value: "HIPAA Security Rule",
                },
                {
                  label: "HITRUST CSF",
                  value: "HITRUST CSF",
                },
                {
                  label: "NIST CSF",
                  value: "NIST CSF",
                },
                {
                  label: "FedRAMP",
                  value: "FedRAMP",
                },
                {
                  label: "GDPR",
                  value: "GDPR",
                },
                {
                  label: "MITRE ATT&CK",
                  value: "MITRE ATT&CK",
                },
                {
                  label: "HITRUST CSF",
                  value: "HITRUST CSF",
                },
              ]}
            />
          </div>
        </FormProvider>
        <Button
          className="w-40 bg-cyan-700 hover:bg-cyan-600 "
          onClick={handleCreateQuestion}
        >
          Save Question
        </Button>
      </form>
    </ScrollArea>
  );
}
