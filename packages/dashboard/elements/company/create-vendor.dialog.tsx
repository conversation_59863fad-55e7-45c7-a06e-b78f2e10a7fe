import { Button } from '@/components/ui/button';
import { AlertDialogProps } from '@radix-ui/react-alert-dialog';
import { manageCompanyValidator } from '../../../shared/validators/company.validator';
import { z } from 'zod';
import { FormProvider, useFieldArray, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import FormField from '@/components/form/FormField';
import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import { FormSelect } from '@/components/form/FormSelect';
import { FormTextField } from '@/components/form/FormTextField';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { useMemo } from 'react';
import { CompanyType } from '../../../shared/types/Company';

type Form = z.infer<typeof manageCompanyValidator>;

export default function CreateVendorDialog(props: AlertDialogProps) {
  const methods = useForm<Form>({
    resolver: zodResolver(manageCompanyValidator),
    defaultValues: {
      type: CompanyType.VENDOR,
    },
  });

  const companies = trpc.company.getCompanies.useQuery({
    type: [CompanyType.COMPANY],
  });

  const createCompany = trpc.company.manageCompany.useMutation();

  const handleSubmit = methods.handleSubmit((data) =>
    toastPromise({
      asyncFunc: createCompany.mutateAsync(data),
      success: 'vendor created successfully',
      onSuccess: () => {
        methods.reset();
        props.onOpenChange?.(false);
      },
    })
  );
  const selectOptions = useMemo(
    () =>
      (companies.data || []).map((c) => {
        return { value: String(c._id), label: c.company };
      }),
    [companies.data]
  );

  if (companies.isLoading) return null;

  return null;
  return (
    <Dialog {...props} modal={false}>
      <DialogContent className='min-w-[45vw]' tabIndex={-1}>
        <DialogHeader>
          <DialogTitle>Create Vendor</DialogTitle>
        </DialogHeader>
        <ScrollArea className='h-[80vh]'>
          <form className='space-y-4 p-4'>
            <FormProvider {...methods}>
              <FormSelect
                label='Select Client'
                placeholder='Select client'
                name='reporter'
                options={selectOptions}
              />
              <FormField
                name='company'
                placeholder='Enter vendor name'
                label='Vendor Name'
              />
              <FormSelect
                name='sector'
                placeholder='Select Sector'
                label='Sector'
                options={[
                  { label: 'IT', value: 'IT' },
                  { label: 'Networking', value: 'Networking' },
                ]}
                showSearch
                allowAddItem
              />
              <FormTextField
                name='address'
                label='Address'
                placeholder='Enter Address'
              />
              <FormField
                name='country'
                label='Country'
                placeholder='Enter Country'
              />
              <FormField name='state' label='State' placeholder='Enter State' />
              <FormField name='city' label='City' placeholder='Enter City' />
              <FormField
                name='location'
                label='Location'
                placeholder='Enter Location'
              />
              <FormField
                name='zipcode'
                label='Zip'
                placeholder='Enter Zip'
                type='number'
              />
              <FormField
                name='assesmentDurationInDays'
                label='Assessment Duration In days'
                placeholder='Enter Assesment Duration in days'
                type='number'
              />
              <FormComboBoxPopover
                allowCustomValues
                multiple
                name='emails'
                options={[]}
                placeholder='Enter Emails'
                label='Emails'
              />
              {/* Contact*/}
              <div>
                <Label className='underline'>Contact</Label>
                <div className='flex items-end gap-3'>
                  <FormComboBoxPopover
                    allowCustomValues
                    multiple
                    name='contact.contact'
                    options={[]}
                    placeholder='Enter contact numbers'
                    label='Contact Numbers'
                  />
                  <FormComboBoxPopover
                    allowCustomValues
                    multiple
                    name='contact.email'
                    options={[]}
                    placeholder='Enter emails'
                    label='Emails'
                  />
                </div>
              </div>
              <FormField
                name='assessmentName'
                label='Assessment Name'
                placeholder='Enter Assesment Name'
              />
              <FormField
                name='numOfUsersPerAssessment'
                label='No of users per assessment'
                placeholder='Enter no.of users per assessment'
                type='number'
              />

              <div>
                <Label className='underline'>Assessment Manager</Label>
                <div className='flex items-end gap-3'>
                  <FormComboBoxPopover
                    allowCustomValues
                    multiple
                    name='assessmentManager.contact'
                    options={[]}
                    placeholder='Enter contact numbers'
                    label='Contact Numbers'
                  />
                  <FormComboBoxPopover
                    allowCustomValues
                    multiple
                    name='assessmentManager.email'
                    options={[]}
                    placeholder='Enter emails'
                    label='Emails'
                  />
                </div>
              </div>
            </FormProvider>
          </form>
        </ScrollArea>
        <DialogFooter>
          <Button onClick={handleSubmit}>Create Company</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
