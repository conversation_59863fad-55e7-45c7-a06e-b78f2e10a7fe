'use client';
import { z } from 'zod';
import { manageVendorValidator } from '../../../shared/validators/company.validator';
import { Button } from '@/components/ui/button';
import { AlertDialogProps } from '@radix-ui/react-alert-dialog';
import { useFieldArray, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Edit } from 'lucide-react';
import { RouterInput } from '../../../shared';
import { useMemo, useState } from 'react';
import { CompanyType } from '../../../shared/types/Company';
import ManageVendorForm from './manage.vendor.form';
import { ManageVendorFormProps } from '@/hooks/forms/company';

export default function ManageVendorDialog({
  methods,
  criticalityFieldArray,
  engagementManagerFieldArray,
  vendorAssesmentFieldArray,
  type,
  ...dialogProps
}: ManageVendorFormProps & AlertDialogProps) {
  const [open, setOpen] = useState(false);
  const utils = trpc.useUtils();

  const manageVendor = trpc.company.manageVendor.useMutation();

  const companies = trpc.company.getCompanies.useQuery({
    type: [CompanyType.COMPANY],
  });

  const handleSubmit = methods.handleSubmit((data) =>
    toastPromise({
      asyncFunc: manageVendor.mutateAsync(data),
      onSuccess: () => {
        methods.reset();
        dialogProps.onOpenChange?.(false) || setOpen(false);
        utils.company.invalidate();
      },
    })
  );

  return (
    <Dialog open={open} onOpenChange={setOpen} {...dialogProps}>
      <DialogContent className='min-w-[70vw]'>
        <DialogHeader>
          <DialogTitle className='underline'>CREATE {type}</DialogTitle>
        </DialogHeader>
        <ManageVendorForm
          methods={methods}
          vendorAssessmentManager={vendorAssesmentFieldArray}
          companyEngagementManager={engagementManagerFieldArray}
          criticalityLevels={criticalityFieldArray}
          companies={companies.data || []}
        />

        <DialogFooter>
          <DialogClose onClick={handleSubmit}>
            <Button>Save</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
