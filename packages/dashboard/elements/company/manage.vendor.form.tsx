'use client';
import FormField from '@/components/form/FormField';
import { FormSelect } from '@/components/form/FormSelect';
import { Button } from '@/components/ui/button';
import {
  CRITICALITY_LEVELS,
  PRIORITY_LEVEL,
} from '../../../shared/types/Company';
import { manageVendorValidator } from '../../../shared/validators/company.validator';
import React from 'react';
import {
  FormProvider,
  UseFieldArrayReturn,
  UseFormReturn,
} from 'react-hook-form';
import { z } from 'zod';

import { RouterOutput } from '../../../shared';
import { useSession } from 'next-auth/react';
import { USER_ROLE } from '../../../shared/types/User';

type Form = z.infer<typeof manageVendorValidator>;

type Props = {
  methods: UseFormReturn<Form>;
  vendorAssessmentManager: UseFieldArrayReturn<Form, 'vendorAssessmentManager'>;
  companyEngagementManager: UseFieldArrayReturn<
    Form,
    'companyEngagementManager'
  >;
  criticalityLevels: UseFieldArrayReturn<Form, 'criticalityLevels'>;
  companies: RouterOutput['company']['getCompanies'];
};

export default function ManageVendorForm({
  methods,
  vendorAssessmentManager: {
    append: vendorAssesmentAppend,
    fields: vendorAssesmentFields,
    remove: vendorAssesmentRemove,
  },
  companyEngagementManager: {
    append: engagementManagerAppend,
    fields: engagementManagerFields,
    remove: engagementManagerRemove,
  },
  criticalityLevels: {
    append: criticalityAppend,
    fields: criticalityFields,
    remove: criticalityRemove,
  },
  companies,
}: Props) {
  const { data: sessionData } = useSession();
  const companiesByUser = sessionData?.user.companies || [];
  const isCompanyAdmin = sessionData?.user.role === USER_ROLE.COMPANY_ADMIN;

  const filteredAdminCompanies = companies?.filter((c) =>
    companiesByUser.includes(String(c._id))
  );

  const finalData = isCompanyAdmin ? filteredAdminCompanies : companies;

  return (
    <form className='space-y-4 p-4 '>
      {/* @ts-ignore */}
      <FormProvider {...methods}>
        <div>
          <label className='font-semibold   '>Company</label>
          <FormSelect
            name='reportsTo'
            placeholder='Select company'
            options={finalData.map((c) => {
              return { label: c.company, value: String(c._id) };
            })}
          />
        </div>
        <div>
          <label className='font-semibold   '>Vendor Name</label>
          <FormField name='company' placeholder='Enter Vendor name' />
        </div>
        <div>
          <label className='font-semibold   '>Sector</label>
          <FormField name='sector' placeholder='Enter sector' />
        </div>

        <div className='flex flex-col gap-2'>
          <label htmlFor='' className='font-semibold'>
            Vendor Assessment Manager
          </label>
          {vendorAssesmentFields.map(({ id }, idx) => {
            return (
              <div
                key={id}
                className='border-2 rounded-lg p-2 grid grid-cols-2 grid-rows-2 gap-2'
              >
                <FormField
                  name={`vendorAssessmentManager.${idx}.firstName`}
                  label='First Name'
                  placeholder='Enter first name'
                />
                <FormField
                  name={`vendorAssessmentManager.${idx}.lastName`}
                  label='Last Name'
                  placeholder='Enter last name'
                />
                <FormField
                  name={`vendorAssessmentManager.${idx}.title`}
                  label='Title'
                  placeholder='Enter title'
                />
                <FormField
                  name={`vendorAssessmentManager.${idx}.email`}
                  label='Email'
                  placeholder='Add email'
                  type='email'
                />
                <FormField
                  name={`vendorAssessmentManager.${idx}.countryCode`}
                  label='Country Code'
                  placeholder='Enter country code'
                  type='number'
                />
                <FormField
                  name={`vendorAssessmentManager.${idx}.phone`}
                  label='Contact Number'
                  placeholder='Add contact number'
                  type='number'
                />
              </div>
            );
          })}
          <div className='flex flex-row gap-3'>
            <Button
              type='button'
              onClick={() =>
                vendorAssesmentAppend({
                  countryCode: '',
                  email: '',
                  phone: '',
                  firstName: '',
                  lastName: '',
                  title: '',
                })
              }
            >
              Add
            </Button>
            <Button
              type='button'
              onClick={() =>
                vendorAssesmentFields.length > 1 &&
                vendorAssesmentRemove(vendorAssesmentFields.length - 1)
              }
              variant={'destructive'}
            >
              Remove
            </Button>
          </div>
        </div>
        <div className='flex flex-col gap-2'>
          <label htmlFor='' className='font-semibold'>
            Company Engagement Manager
          </label>
          {engagementManagerFields.map(({ id }, idx) => {
            return (
              <div
                key={id}
                className='border-2 rounded-lg p-2 grid grid-cols-2 grid-rows-2 gap-2'
              >
                <FormField
                  name={`companyEngagementManager.${idx}.firstName`}
                  label='First Name'
                  placeholder='Enter first name'
                />
                <FormField
                  name={`companyEngagementManager.${idx}.lastName`}
                  label='Last Name'
                  placeholder='Enter last name'
                />
                <FormField
                  name={`companyEngagementManager.${idx}.title`}
                  label='Title'
                  placeholder='Enter title'
                />
                <FormField
                  name={`companyEngagementManager.${idx}.email`}
                  label='Email'
                  placeholder='Add email'
                  type='email'
                />
                <FormField
                  name={`companyEngagementManager.${idx}.countryCode`}
                  label='Country Code'
                  placeholder='Enter country code'
                  type='number'
                />
                <FormField
                  name={`companyEngagementManager.${idx}.phone`}
                  label='Contact Number'
                  placeholder='Add contact number'
                  type='number'
                />
              </div>
            );
          })}
          <div className='flex flex-row gap-3'>
            <Button
              type='button'
              onClick={() =>
                engagementManagerAppend({
                  countryCode: '',
                  email: '',
                  phone: '',
                  firstName: '',
                  lastName: '',
                  title: '',
                })
              }
            >
              Add
            </Button>
            <Button
              type='button'
              onClick={() =>
                engagementManagerFields.length > 1 &&
                engagementManagerRemove(engagementManagerFields.length - 1)
              }
              variant={'destructive'}
            >
              Remove
            </Button>
          </div>
        </div>
        <div>
          <label className='font-semibold   '> Vendor Address</label>
          <FormField name='address' placeholder='Enter address' />
        </div>

        <div>
          <label className='font-semibold   '>City</label>
          <FormField name='city' placeholder='Enter city' />
        </div>
        <div>
          <label className='font-semibold   '>State</label>
          <FormField name='state' placeholder='Enter state' />
        </div>
        <div>
          <label className='font-semibold   '>Zipcode</label>
          <FormField name='zipcode' placeholder='Enter zipcode' />
        </div>
        <div>
          <label className='font-semibold   '>Country</label>
          <FormField name='country' placeholder='Enter country ' />
        </div>

        <div>
          <label className='font-semibold   '>Location</label>
          <FormField name='location' placeholder='Enter location' />
        </div>
        <div>
          <label className='font-semibold   '>Vendor Risk Priority Level</label>
          <FormSelect
            name='priorityLevel'
            placeholder='Select Priority level'
            options={[
              { label: PRIORITY_LEVEL.P1, value: PRIORITY_LEVEL.P1 },
              { label: PRIORITY_LEVEL.P2, value: PRIORITY_LEVEL.P2 },
              { label: PRIORITY_LEVEL.P3, value: PRIORITY_LEVEL.P3 },
              { label: PRIORITY_LEVEL.P4, value: PRIORITY_LEVEL.P4 },
            ]}
          />
        </div>
        <div className='flex flex-col gap-2'>
          <label htmlFor='' className='font-semibold   '>
            Configure Vulnerability Response Time Based On MSA/SOW
          </label>
          {criticalityFields.map(({ id }, idx) => {
            return (
              <div
                key={id}
                className='flex flex-row items-center justify-start gap-2'
              >
                <div className='w-[200px] mb-[10px]'>
                  <FormSelect
                    name={`criticalityLevels.${idx}.level`}
                    label=' Level'
                    placeholder='Select crtiticality level'
                    options={[
                      {
                        label: CRITICALITY_LEVELS.CRITICAL.toUpperCase(),
                        value: CRITICALITY_LEVELS.CRITICAL,
                      },
                      {
                        label: CRITICALITY_LEVELS.HIGH.toUpperCase(),
                        value: CRITICALITY_LEVELS.HIGH,
                      },
                      {
                        label: CRITICALITY_LEVELS.MEDIUM.toUpperCase(),
                        value: CRITICALITY_LEVELS.MEDIUM,
                      },
                      {
                        label: CRITICALITY_LEVELS.LOW.toUpperCase(),
                        value: CRITICALITY_LEVELS.LOW,
                      },
                      {
                        label: CRITICALITY_LEVELS.VERYLOW.toUpperCase(),
                        value: CRITICALITY_LEVELS.VERYLOW,
                      },
                    ]}
                  />
                </div>
                <FormField
                  name={`criticalityLevels.${idx}.timeDuration`}
                  label='Time Duration '
                  placeholder='Enter time duration in days'
                  type='number'
                  className=''
                />
              </div>
            );
          })}
          <div className='flex flex-row gap-3'>
            <Button
              type='button'
              disabled={criticalityFields.length >= 5}
              onClick={() =>
                criticalityAppend({
                  //@ts-ignore
                  contact: {
                    countryCode: '',
                    email: '',
                    phone: '',
                    name: '',
                  },
                })
              }
            >
              Add
            </Button>
            <Button
              type='button'
              onClick={() =>
                criticalityFields.length > 1 &&
                criticalityRemove(criticalityFields.length - 1)
              }
              variant={'destructive'}
            >
              Remove
            </Button>
          </div>
        </div>
      </FormProvider>
    </form>
  );
}
