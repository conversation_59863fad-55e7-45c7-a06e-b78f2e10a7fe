'use client';
import { But<PERSON> } from '@/components/ui/button';
import { AlertDialogProps } from '@radix-ui/react-alert-dialog';
import { manageCompanyValidator } from '../../../shared/validators/company.validator';
import { z } from 'zod';
import { useFieldArray, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Edit } from 'lucide-react';
import { RouterInput } from '../../../shared';
import { useMemo, useState } from 'react';
import { CompanyType } from '../../../shared/types/Company';
import countryList from 'react-select-country-list';
import ManageCompanyForm from './manage.company.form';
import { ManageCompanyFormProps } from '@/hooks/forms/company';

export default function ManageCompanyDialog({
  methods,
  fieldArray,
  type,
  ...dialogProps
}: ManageCompanyFormProps & AlertDialogProps) {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState('');

  const options = useMemo(() => countryList().getData(), []);

  const utils = trpc.useUtils();

  const manageCompany = trpc.company.manageCompany.useMutation();

  const changeHandler = (value: string) => {
    setValue(value);
  };

  const handleSubmit = methods.handleSubmit((data) =>
    toastPromise({
      asyncFunc: manageCompany.mutateAsync(data),
      onSuccess: () => {
        methods.reset();
        dialogProps.onOpenChange?.(false) || setOpen(false);
        utils.company.invalidate();
      },
    })
  );

  return (
    <Dialog open={open} onOpenChange={setOpen} {...dialogProps}>
      <DialogContent className='min-w-[70vw] '>
        <DialogHeader>
          <DialogTitle className='underline'>CREATE {type}</DialogTitle>
        </DialogHeader>
        <ManageCompanyForm
          changeHandler={changeHandler}
          //@ts-ignore
          options={options}
          value={value}
          methods={methods}
          primaryContactFieldArray={fieldArray}
        />

        <DialogFooter>
          <DialogClose onClick={handleSubmit}>
            <Button>Save</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
