import FormField from '@/components/form/FormField';
import { Button } from '@/components/ui/button';
import { manageCompanyValidator } from '../../../shared/validators/company.validator';
import {
  FormProvider,
  UseFieldArrayReturn,
  UseFormReturn,
} from 'react-hook-form';
import { z } from 'zod';
import { FormSelect } from '@/components/form/FormSelect';

type Form = z.infer<typeof manageCompanyValidator>;

type Props = {
  methods: UseFormReturn<Form>;
  primaryContactFieldArray: UseFieldArrayReturn<Form, 'primaryContact'>;
  value: string;
  options: { value: string; lable: string }[];
  changeHandler: (value: any) => void;
};
export default function ManageCompanyForm({
  methods,
  primaryContactFieldArray: { append, fields, remove },
  changeHandler,
  options,
  value,
}: Props) {
  return (
    <form className='space-y-4 p-4'>
      {/* @ts-ignore */}
      <FormProvider {...methods}>
        <div>
          <label className='font-semibold   '>Company Details</label>
          <FormField name='company' placeholder='Enter company name' />
        </div>
        <div>
          <label className='font-semibold   '>Sector</label>

          <FormField name='sector' placeholder='Enter sector' className='' />
        </div>
        <div className='space-y-4'>
          <label className='font-semibold   '>Primary Contact Details</label>
          {fields.map(({ id }, idx) => {
            return (
              <div
                key={id}
                className='border-2 rounded-lg p-2 grid grid-cols-2 grid-rows-2 gap-2'
              >
                <FormField
                  name={`primaryContact.${idx}.firstName`}
                  label='First Name'
                  placeholder='Enter first name'
                />
                <FormField
                  name={`primaryContact.${idx}.lastName`}
                  label='Last Name'
                  placeholder='Enter last name'
                />
                <FormField
                  name={`primaryContact.${idx}.title`}
                  label='Title'
                  placeholder='Enter title'
                />
                <FormField
                  name={`primaryContact.${idx}.email`}
                  label='Email'
                  placeholder='Add email'
                  type='email'
                />
                <FormField
                  name={`primaryContact.${idx}.countryCode`}
                  label='Country Code'
                  placeholder='Enter country code'
                  type='number'
                />
                <FormField
                  name={`primaryContact.${idx}.phone`}
                  label='Contact Number'
                  placeholder='Add contact number'
                  type='number'
                />
              </div>
            );
          })}
        </div>
        <div className='space-x-2'>
          <Button
            type='button'
            onClick={() =>
              append({
                countryCode: '',
                email: '',
                phone: '',
                firstName: '',
                lastName: '',
                title: '',
              })
            }
          >
            Add
          </Button>
          <Button
            type='button'
            onClick={() => fields.length > 1 && remove(fields.length - 1)}
            variant={'destructive'}
          >
            Remove
          </Button>
        </div>

        <div>
          <label className='font-semibold   '>Address</label>
          <FormField name='address' placeholder='Enter address' />
        </div>
        <div>
          <label className='font-semibold   '>City</label>
          <FormField name='city' placeholder='Enter city' />
        </div>
        <div>
          <label className='font-semibold   '>State</label>
          <FormField name='state' placeholder='Enter state' />
        </div>
        <div>
          <label className='font-semibold   '>Zipcode</label>
          <FormField name='zipcode' placeholder='Enter zipcode' />
        </div>
        <div>
          <label className='font-semibold   '>Country</label>
          <FormSelect
            name='country'
            //@ts-ignore
            options={options}
            placeholder='Enter country'
          />
        </div>

        <div>
          <label className='font-semibold   '>No of licensed vendor(s)</label>
          <FormField
            name='numOfLicensedVendors'
            placeholder='Enter no of licensed vendors'
            type='number'
          />
        </div>
      </FormProvider>
    </form>
  );
}
