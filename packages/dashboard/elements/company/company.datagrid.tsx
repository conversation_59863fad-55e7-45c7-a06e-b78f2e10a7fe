'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from '@/components/ui/sheet';

import { trpc } from '@/providers/Providers';
import { Edit, Pencil } from 'lucide-react';
import { CompanyType } from '../../../shared/types/Company';
import ManageCompanyDialog from './manage-company.dialog';
import { MangeCompanyForm, useManageCompanyForm } from '@/hooks/forms/company';
import { useState } from 'react';

export default function CompanyDataGrid() {
  const [open, setOpen] = useState(false);
  const companies = trpc.company.getCompanies.useQuery({
    type: [CompanyType.COMPANY],
  });

  const { fieldArray, methods } = useManageCompanyForm();

  const handleUpdate = (data: MangeCompanyForm) => {
    const entries = Object.entries(data);

    entries.forEach(([key, value]) => {
      methods.setValue('companyId', data.companyId);
      // @ts-ignore
      methods.setValue(key, value);
    });

    // @ts-ignore
    methods.setValue('companyId', data._id);

    setOpen(true);
  };

  return (
    <>
      <ManageCompanyDialog
        type={CompanyType.COMPANY}
        methods={methods}
        fieldArray={fieldArray}
        open={open}
        onOpenChange={setOpen}
      />
      <Table className=''>
        <TableHeader>
          <TableRow className='font-semibold border-none   '>
            <TableHead className='capitalize  bg-slate-300 '>No</TableHead>
            <TableHead className='capitalize  bg-slate-300 '>Company</TableHead>
            <TableHead className='capitalize  bg-slate-300 '>Sector</TableHead>
            <TableHead className='capitalize  bg-slate-300 '>
              Vendors Licenses
            </TableHead>
            <TableHead className='capitalize  bg-slate-300 text-center '>
              Details
            </TableHead>

            <TableHead className='capitalize  bg-slate-300 '>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className='border-2 '>
          {companies.data?.map((c, idx) => {
            if (c.type !== CompanyType.COMPANY) return null;

            return (
              <TableRow key={String(c._id)}>
                <TableCell className='border-2 '>{idx + 1}</TableCell>
                <TableCell className='border-2 '>{c.company}</TableCell>
                <TableCell className='border-2 '>{c.sector}</TableCell>
                <TableCell className='border-2 '>
                  {c.numOfLicensedVendors}
                </TableCell>
                <TableCell className=' text-center border-2'>
                  <Sheet>
                    <SheetTrigger>
                      <div className='text-blue-800 hover:text-blue-400 hover:underline'>
                        More Details
                      </div>
                    </SheetTrigger>
                    <SheetContent className='min-w-[45vw]'>
                      <SheetHeader>
                        <SheetTitle>Primary Contact Details</SheetTitle>
                      </SheetHeader>
                      <Table className=' '>
                        <TableHeader className=''>
                          <TableRow className=''>
                            <TableHead className='text-left '>
                              First Name
                            </TableHead>
                            <TableHead className='text-left '>
                              Last Name
                            </TableHead>
                            <TableHead className='text-left '>Title</TableHead>
                            <TableHead className='text-center '>
                              Email
                            </TableHead>
                            <TableHead className='text-center '>
                              Country Code
                            </TableHead>
                            <TableHead className='text-center '>
                              Contact
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody className=''>
                          {c.primaryContact.map((p, idx) => {
                            return (
                              <TableRow key={idx}>
                                <TableCell>
                                  {p.firstName.toUpperCase()}
                                </TableCell>
                                <TableCell>
                                  {p.lastName.toUpperCase()}
                                </TableCell>
                                <TableCell>{p.title}</TableCell>
                                <TableCell>{p.email}</TableCell>
                                <TableCell className='text-center'>
                                  {p.countryCode}
                                </TableCell>
                                <TableCell>{p.phone}</TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                      <SheetTitle>Other Details</SheetTitle>
                      <Table>
                        <TableBody>
                          <TableRow>
                            <TableCell className='border font-bold'>
                              Address
                            </TableCell>
                            <TableCell>{c.address}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className='border font-bold'>
                              City
                            </TableCell>
                            <TableCell>{c.city}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className='border font-bold'>
                              State
                            </TableCell>
                            <TableCell>{c.state}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className='border font-bold'>
                              Zipcode
                            </TableCell>
                            <TableCell>{c.zipcode}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className='border font-bold'>
                              Country
                            </TableCell>
                            <TableCell>{c.country}</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </SheetContent>
                  </Sheet>
                </TableCell>
                <TableCell>
                  <Button onClick={() => handleUpdate(c)}>
                    <Pencil className='size-4 mr-2' />
                    Update
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </>
  );
}
