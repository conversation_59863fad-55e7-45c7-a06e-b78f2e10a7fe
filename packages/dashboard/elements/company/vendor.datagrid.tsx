'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from '@/components/ui/sheet';

import { trpc } from '@/providers/Providers';
import { Edit, Pencil } from 'lucide-react';
import { CompanyType } from '../../../shared/types/Company';
import { useState } from 'react';
import { ManageVendorForm, useManageVendorForm } from '@/hooks/forms/company';
import ManageVendorDialog from './manage.vendor.dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Label } from '@/components/ui/label';
import { useSession } from 'next-auth/react';
import { useRole } from '@/hooks/useRole';

export default function VendorDataGrid() {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const vendors = trpc.company.getCompanies.useQuery({
    type: [CompanyType.VENDOR],
  });

  const loginUser = useSession();

  const { isCompanyAdmin } = useRole();

  const loginUsersCompany = loginUser.data?.user.companies;

  const data = trpc.company.getCompanies.useQuery({
    companies: loginUsersCompany,
  });

  const {
    criticalityFieldArray,
    engagementManagerFieldArray,
    methods,
    vendorAssesmentFieldArray,
  } = useManageVendorForm();

  const handleUpdate = (data: ManageVendorForm) => {
    const entries = Object.entries(data);

    entries.forEach(([key, value]) => {
      methods.setValue('companyId', data.companyId);
      // @ts-ignore
      methods.setValue(key, value);
    });

    // @ts-ignore
    methods.setValue('companyId', data._id);
    // @ts-ignore
    methods.setValue('reportsTo', data.reportsTo._id);

    setOpen(true);
  };
  // @ts-ignore
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const dd = data?.data?.map((p) => p.company);

  const maindata = vendors.data?.filter((_) =>
    // @ts-ignore
    _.reportsTo?.company?.includes(dd)
  );

  const filteredVendors = (isCompanyAdmin ? maindata : vendors.data)?.filter(
    (vendor) => {
      return (
        vendor.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vendor.reportsTo?.company
          .toLowerCase()
          .includes(searchTerm.toLowerCase())
      );
    }
  );

  return (
    <div className='container1'>
      <input
        type='text'
        placeholder='Search...'
        value={searchTerm}
        onChange={handleSearchChange}
        className='mb-4 mt-2 p-2 border border-gray-300 rounded-2xl w-[350px]'
      />
      <ManageVendorDialog
        criticalityFieldArray={criticalityFieldArray}
        engagementManagerFieldArray={engagementManagerFieldArray}
        methods={methods}
        type={CompanyType.VENDOR}
        vendorAssesmentFieldArray={vendorAssesmentFieldArray}
        open={open}
        onOpenChange={setOpen}
      />
      <Table>
        <TableHeader>
          <TableRow className='font-semibold    '>
            <TableHead className='capitalize  bg-slate-300 '>No</TableHead>
            <TableHead className='capitalize  bg-slate-300 '>Company</TableHead>
            <TableHead className='capitalize  bg-slate-300 '>Vendor</TableHead>
            <TableHead className='capitalize  bg-slate-300 '>
              Vendor Type
            </TableHead>

            <TableHead className='capitalize  bg-slate-300 '>
              Priority
            </TableHead>
            <TableHead className='capitalize  bg-slate-300 text-center '>
              Details
            </TableHead>

            <TableHead className='capitalize  bg-slate-300 '>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className='border-2'>
          {filteredVendors?.map((p, idx) => {
            if (p.type !== CompanyType.VENDOR) return null;

            return (
              <TableRow key={String(p._id)}>
                <TableCell className=''>{idx + 1}</TableCell>
                <TableCell className='text-primary border-x-2 '>
                  {p.reportsTo?.company.toUpperCase()}
                </TableCell>
                <TableCell className=''>{p.company.toUpperCase()}</TableCell>
                <TableCell className='border-x-2'>
                  {p.sector.toUpperCase()}
                </TableCell>
                <TableCell className='border-x-2'>{p.priorityLevel}</TableCell>
                <TableCell className='border-x-2'>
                  <Sheet>
                    <SheetTrigger>
                      <div className='text-blue-800 hover:text-blue-400 hover:underline'>
                        More Details
                      </div>
                    </SheetTrigger>

                    <SheetContent className='min-w-[45vw] '>
                      <ScrollArea className='h-[800px]'>
                        <SheetHeader>
                          <SheetTitle>
                            Vendor Assessment Manager Details
                          </SheetTitle>
                        </SheetHeader>

                        <Table className=''>
                          <TableHeader>
                            <TableRow>
                              <TableHead className='text-center  font-semibold underline'>
                                Email
                              </TableHead>
                              <TableHead className='text-center  font-semibold underline'>
                                Country Code
                              </TableHead>
                              <TableHead className='text-center  font-semibold underline'>
                                Contact
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {/* @ts-ignore */}
                            {p.vendorAssessmentManager?.map((v, idx) => {
                              return (
                                <TableRow key={idx}>
                                  <TableCell>{v.email}</TableCell>
                                  <TableCell className='text-center'>
                                    {v.countryCode}
                                  </TableCell>
                                  <TableCell>{v.phone}</TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                        <SheetTitle className='mt-10'>
                          Company Engagement Manager Details
                        </SheetTitle>
                        <Table className=''>
                          <TableHeader>
                            <TableRow>
                              <TableHead className='text-center font-semibold underline'>
                                Email
                              </TableHead>
                              <TableHead className='text-center font-semibold underline'>
                                Country Code
                              </TableHead>
                              <TableHead className='text-center font-semibold underline'>
                                Contact
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {p.companyEngagementManager?.map((v, idx) => {
                              return (
                                <TableRow key={idx}>
                                  <TableCell>{v.email}</TableCell>
                                  <TableCell className='text-center'>
                                    {v.countryCode}
                                  </TableCell>
                                  <TableCell>{v.phone}</TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                        <SheetTitle className='mt-10'>
                          Vulnerability response time based on MSA/SOW
                        </SheetTitle>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className='border-x-2 font-semibold underline'>
                                Level
                              </TableHead>
                              <TableHead className='border-x-2 font-semibold underline'>
                                Duration in days
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {p.criticalityLevels.map((c, idx) => {
                              return (
                                <TableRow
                                  key={idx}
                                  className='border-2 bg-amber-100'
                                >
                                  <TableCell className='border-2'>
                                    {c.level.toUpperCase()}
                                  </TableCell>
                                  <TableCell className='text-center bg-amber-100'>
                                    {c.timeDuration}
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                        <SheetTitle className='mt-10'>Other Details</SheetTitle>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <TableCell className='border font-bold'>
                                Address
                              </TableCell>
                              <TableCell>{p.address}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className='border font-bold'>
                                City
                              </TableCell>
                              <TableCell>{p.city}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className='border font-bold'>
                                State
                              </TableCell>
                              <TableCell>{p.state}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className='border font-bold'>
                                Zipcode
                              </TableCell>
                              <TableCell>{p.zipcode}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className='border font-bold'>
                                Country
                              </TableCell>
                              <TableCell>{p.country}</TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </ScrollArea>
                    </SheetContent>
                  </Sheet>
                </TableCell>
                <TableCell className='border-2'>
                  {/* @ts-ignore */}
                  <Button onClick={() => handleUpdate(p)}>
                    <Pencil className='size-4 mr-2' />
                    Update
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
