'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Close,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  createSectionValidator,
  updateSectionValidator,
} from '../../../shared/validators/standard.validator';
import FormField from '@/components/form/FormField';
import { FormTextField } from '@/components/form/FormTextField';
import { FormProvider, useForm } from 'react-hook-form';
import { toastPromise } from '@/lib/utils';
import { Edit } from 'lucide-react';
import { useMemo, useState } from 'react';
import { RouterInput } from '../../../shared';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { trpc } from '@/providers/Providers';
import Loading from '@/app/loading';
import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import { SECTIONTYPE } from '../../../shared/types/Standard';
import { FormSelect } from '@/components/form/FormSelect';

type CreateForm = z.infer<typeof createSectionValidator>;
type UpdateForm = z.infer<typeof updateSectionValidator>;

export const CreateUpdateSectionDialog = ({
  update: update,
}: {
  update?: RouterInput['standard']['updateSection'];
}) => {
  const standards = trpc.standard.getStandards.useQuery();

  const [open, setOpen] = useState(false);
  const createMethods = useForm<CreateForm>({
    defaultValues: {
      description: '',
      section: '',
      standard: [],
      sectionType: SECTIONTYPE.GENERAL,
      sectionLable: '',
    },
    resolver: zodResolver(createSectionValidator),
  });

  const updateMethods = useForm<UpdateForm>({
    defaultValues: update,
    resolver: zodResolver(updateSectionValidator),
  });

  const createSectionMutation = trpc.standard.createSection.useMutation();
  const updateSectionMutation = trpc.standard.updateSection.useMutation();
  const utils = trpc.useUtils();

  const handleSubmit = update
    ? updateMethods.handleSubmit((data) => {
        toastPromise({
          asyncFunc: updateSectionMutation.mutateAsync(data),
          success: 'Section updated successfully',
          onSuccess: () => {
            updateMethods.reset();
            utils.standard.invalidate();
            setOpen(false);
          },
        });
      })
    : createMethods.handleSubmit((data) => {
        toastPromise({
          asyncFunc: createSectionMutation.mutateAsync(data),
          success: 'Section Created successfully',
          onSuccess: () => {
            createMethods.reset();
            utils.standard.invalidate();
            setOpen(false);
          },
        });
      });

  const selectOptions = useMemo(
    () =>
      (standards.data || []).map((c) => {
        return { value: String(c._id), label: c.standard };
      }),
    [standards.data]
  );

  if (standards.isLoading) return null;
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          {!update ? (
            'Create Section'
          ) : (
            <>
              <Edit className='w-4 h-4 mr-2' />
              Update
            </>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {update ? 'Update Section' : 'Create Section'}
          </DialogTitle>
        </DialogHeader>

        <form className='space-y-3'>
          {/* @ts-ignore */}
          <FormProvider {...(update ? updateMethods : createMethods)}>
            <FormSelect
              name='sectionType'
              label='Select section type'
              placeholder='Select a section type'
              options={[
                { label: 'General', value: SECTIONTYPE.GENERAL },
                { label: 'Inventory', value: SECTIONTYPE.INVENTORY },
              ]}
            />
            <FormComboBoxPopover
              name='standard'
              label='Compliance Standard'
              placeholder='Select standard'
              options={selectOptions}
            />

            <FormField
              name='section'
              label='Section'
              placeholder='Enter Section'
            />
            <FormField
              name='sectionLable'
              label='Section Tag'
              placeholder='Enter section tag'
            />
            <FormTextField
              name='description'
              label='Description'
              placeholder='Enter Description'
            />
          </FormProvider>
        </form>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant='destructive'>Close</Button>
          </DialogClose>
          <Button onClick={handleSubmit}>
            {update ? 'update section' : 'create section'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
