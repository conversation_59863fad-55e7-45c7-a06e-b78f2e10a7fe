import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { RouterOutput } from '../../../shared';
import { format } from 'date-fns';
import { useMemo } from 'react';

export default function CreditReportsDataGrid({
  creditReports,
}: {
  creditReports: RouterOutput['creditReport']['getCreditReports'];
}) {
  const averageRating = useMemo(() => {
    if (creditReports.length === 0) return null;
    const total = creditReports.reduce((acc, { rating }) => acc + rating, 0);
    const average = total / creditReports.length;
    return average;
  }, [creditReports]);
  return (
    <Table>
      <TableHeader>
        <TableHead>Date</TableHead>
        <TableHead>Name</TableHead>
        <TableHead>Company</TableHead>
        <TableHead>Category</TableHead>
        <TableHead>Quarter</TableHead>
        <TableHead>Rating</TableHead>
        <TableHead>Remarks</TableHead>
      </TableHeader>

      <TableBody>
        {creditReports.map(
          ({ _id, date, quarter, rating, vendor, remarks, name, category }) => (
            <TableRow key={String(_id)}>
              <TableCell>{format(date, 'PPP')}</TableCell>
              <TableCell>{name}</TableCell>
              <TableCell>{vendor?.reportsTo.company}</TableCell>
              <TableCell>{category}</TableCell>
              <TableCell>{quarter}</TableCell>
              <TableCell>{rating}</TableCell>
              <TableCell>{remarks}</TableCell>
            </TableRow>
          )
        )}
      </TableBody>
      <TableFooter>
        <TableRow>
          <TableCell></TableCell>
          <TableCell></TableCell>
          <TableCell></TableCell>
          <TableCell></TableCell>
          <TableCell>Average:</TableCell>
          <TableCell>{averageRating?.toFixed(2)}</TableCell>
          <TableCell></TableCell>
        </TableRow>
      </TableFooter>
    </Table>
  );
}
