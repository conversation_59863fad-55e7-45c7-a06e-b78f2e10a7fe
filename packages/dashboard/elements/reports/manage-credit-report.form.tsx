import { FormSelect } from '@/components/form/FormSelect';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useCreditReportMethods } from '@/hooks/forms/credit-report';
import { trpc } from '@/providers/Providers';
import { useSession } from 'next-auth/react';
import {
  Business_Credit_sub_rating,
  CreditRatingCategory,
  Employee_satisfaction_sub_rating,
  Quarter,
  Socialmedia_sub_rating,
} from '../../../shared/types/CreditReport';
import { useMemo, useState } from 'react';
import { FormProvider } from 'react-hook-form';
import FormField from '@/components/form/FormField';
import { selectOptionsParser, toastPromise } from '@/lib/utils';
import { FormDatePicker } from '@/components/form/FormDatePicker';
import { FormTextField } from '@/components/form/FormTextField';
import { Save } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { subYears } from 'date-fns';

export default function ManageCreditReportForm() {
  const [open, setOpen] = useState(false);

  const { data: session } = useSession();
  const methods = useCreditReportMethods();

  const vendors = trpc.company.getCompanies.useQuery(
    { companies: session?.user.companies },
    { enabled: !!session?.user.companies }
  );

  const manageCreditReport = trpc.creditReport.manageCreditReport.useMutation();
  const utils = trpc.useUtils();

  const handleSubmit = methods.handleSubmit((data) =>
    toastPromise({
      asyncFunc: manageCreditReport.mutateAsync(data),
      success: 'credit report created successfully',
      onSuccess: () => {
        utils.creditReport.getCreditReports.invalidate();
        methods.reset();
        setOpen(false);
      },
    })
  );
  const selectedCategory = methods.watch('category');

  const subCategoryOptions = useMemo(() => {
    const options: { label: string; value: string }[] = [];
    if (selectedCategory === CreditRatingCategory.BUSINESS_CREDIT_RATING) {
      options.push(
        ...[
          {
            label: Business_Credit_sub_rating.DB_PAYDEX,
            value: Business_Credit_sub_rating.DB_PAYDEX,
          },
          {
            label: Business_Credit_sub_rating.EQUIFAX_CREDIT_RISK,
            value: Business_Credit_sub_rating.EQUIFAX_CREDIT_RISK,
          },
          {
            label: Business_Credit_sub_rating.EQUIFAX_FAILURE_SCORE,
            value: Business_Credit_sub_rating.EQUIFAX_FAILURE_SCORE,
          },
          {
            label: Business_Credit_sub_rating.EXPERIAN_INTELLISCORE,
            value: Business_Credit_sub_rating.EXPERIAN_INTELLISCORE,
          },
          {
            label: Business_Credit_sub_rating.FICO_CREDIT_SCORE,
            value: Business_Credit_sub_rating.FICO_CREDIT_SCORE,
          },
        ]
      );
    }
    if (
      selectedCategory === CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING
    ) {
      options.push(
        ...[
          {
            label: Employee_satisfaction_sub_rating.GLASSDOOR,
            value: Employee_satisfaction_sub_rating.GLASSDOOR,
          },
        ]
      );
    }
    if (selectedCategory === CreditRatingCategory.SOCIALMEDIA_RATING) {
      options.push(
        ...[
          {
            label: Socialmedia_sub_rating.SOCIALMEDIA,
            value: Socialmedia_sub_rating.SOCIALMEDIA,
          },
        ]
      );
    }
    return options;
  }, [selectedCategory]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>Manage Credit Reports</Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Manage credit report</DialogTitle>
        </DialogHeader>

        <form className='space-y-4' onSubmit={handleSubmit}>
          <FormProvider {...methods}>
            <FormField
              name='name'
              placeholder='enter credit-report name'
              label='CreditReport Name*'
            />
            <FormSelect
              name='vendor'
              placeholder='select vendor'
              label='Vendor*'
              options={selectOptionsParser(vendors.data, {
                labelKey: 'company',
                valKey: '_id',
              })}
            />
            <FormSelect
              name='category'
              label='Category'
              placeholder='select category'
              options={[
                {
                  label: CreditRatingCategory.BUSINESS_CREDIT_RATING,
                  value: CreditRatingCategory.BUSINESS_CREDIT_RATING,
                },
                {
                  label: CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING,
                  value: CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING,
                },
                {
                  label: CreditRatingCategory.SOCIALMEDIA_RATING,
                  value: CreditRatingCategory.SOCIALMEDIA_RATING,
                },
              ]}
            />

            {/* <select onChange={handleCategoryChange}>
              <option value='' disabled>
                Select category
              </option>
              <option value='Business_Credit_sub_rating'>
                Business Credit
              </option>
              <option value='Employee_satisfaction_sub_rating'>
                Employee Satisfaction
              </option>
              <option value='Socialmedia_sub_rating'>Social Media</option>
            </select> */}
            <FormSelect
              name='subCategory'
              placeholder='Select report category'
              label='Report*'
              options={subCategoryOptions}
            />
            <FormSelect
              name='quarter'
              placeholder='select quarter'
              label='Quarter*'
              options={Object.values(Quarter).map((_) => ({
                label: _,
                value: _,
              }))}
            />
            <FormField
              name='rating'
              label='Rating*'
              type='number'
              placeholder='enter rating'
            />
            <FormDatePicker
              name='date'
              captionLayout='dropdown-buttons'
              fromYear={subYears(new Date(), 10).getFullYear()}
              toYear={new Date().getFullYear()}
              placeholder='Select date'
              label='Date'
            />
            <FormTextField
              name='remarks'
              label='Remarks'
              placeholder='Enter remarks (optional)'
            />

            <Button className='w-full'>
              <Save className='size-4 mr-2' /> Save
            </Button>
          </FormProvider>
        </form>
      </DialogContent>
    </Dialog>
  );
}
