import { FormComboBox } from '@/components/form/FormComboBox';
import { FormSelect } from '@/components/form/FormSelect';
import { CreateFileFormProps } from '@/hooks/forms/file';
import { selectOptionsParser } from '@/lib/utils';
import {
  Business_Credit_sub_rating,
  CreditRatingCategory,
  Employee_satisfaction_sub_rating,
  Quarter,
  Socialmedia_sub_rating,
} from '../../../shared/types/CreditReport';
import { FormProvider } from 'react-hook-form';
import { RouterOutput } from '../../../shared';
import {
  GetCreditReportFilter,
  GetCreditReportFilterProps,
} from '@/hooks/filters/credit-report';
import { object } from 'zod';
import { FormDatePicker } from '@/components/form/FormDatePicker';
import { subYears } from 'date-fns';
import { useMemo } from 'react';

export default function CreditReportsFilter({
  methods,
  vendors,
}: GetCreditReportFilterProps & {
  vendors: RouterOutput['company']['getCompanies'];
}) {
  const selectedCategory = methods.watch('category');

  const subCategoryOptions = useMemo(() => {
    const options: { label: string; value: string }[] = [];
    if (selectedCategory === CreditRatingCategory.BUSINESS_CREDIT_RATING) {
      options.push(
        ...[
          {
            label: Business_Credit_sub_rating.DB_PAYDEX,
            value: Business_Credit_sub_rating.DB_PAYDEX,
          },
          {
            label: Business_Credit_sub_rating.EQUIFAX_CREDIT_RISK,
            value: Business_Credit_sub_rating.EQUIFAX_CREDIT_RISK,
          },
          {
            label: Business_Credit_sub_rating.EQUIFAX_FAILURE_SCORE,
            value: Business_Credit_sub_rating.EQUIFAX_FAILURE_SCORE,
          },
          {
            label: Business_Credit_sub_rating.EXPERIAN_INTELLISCORE,
            value: Business_Credit_sub_rating.EXPERIAN_INTELLISCORE,
          },
          {
            label: Business_Credit_sub_rating.FICO_CREDIT_SCORE,
            value: Business_Credit_sub_rating.FICO_CREDIT_SCORE,
          },
        ]
      );
    }
    if (
      selectedCategory === CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING
    ) {
      options.push(
        ...[
          {
            label: Employee_satisfaction_sub_rating.GLASSDOOR,
            value: Employee_satisfaction_sub_rating.GLASSDOOR,
          },
        ]
      );
    }
    if (selectedCategory === CreditRatingCategory.SOCIALMEDIA_RATING) {
      options.push(
        ...[
          {
            label: Socialmedia_sub_rating.SOCIALMEDIA,
            value: Socialmedia_sub_rating.SOCIALMEDIA,
          },
        ]
      );
    }
    return options;
  }, [selectedCategory]);

  return (
    <form className='flex items-end gap-4 mb-4'>
      <FormProvider {...methods}>
        <FormSelect
          name='vendor'
          placeholder='select vendor'
          label='Vendor*'
          options={selectOptionsParser(vendors, {
            labelKey: 'company',
            valKey: '_id',
          })}
        />
        <FormDatePicker
          name='fromDate'
          captionLayout='dropdown-buttons'
          fromYear={subYears(new Date(), 10).getFullYear()}
          toYear={new Date().getFullYear()}
        />
        <FormDatePicker
          name='toDate'
          captionLayout='dropdown-buttons'
          fromYear={subYears(new Date(), 10).getFullYear()}
          toYear={new Date().getFullYear()}
        />
        <FormSelect
          name='category'
          label='Category'
          placeholder='Select Category'
          options={[
            {
              label: CreditRatingCategory.BUSINESS_CREDIT_RATING,
              value: CreditRatingCategory.BUSINESS_CREDIT_RATING,
            },
            {
              label: CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING,
              value: CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING,
            },
            {
              label: CreditRatingCategory.SOCIALMEDIA_RATING,
              value: CreditRatingCategory.SOCIALMEDIA_RATING,
            },
          ]}
        />
        <FormSelect
          name='subCategory'
          label='Sub Category'
          placeholder='Select'
          options={subCategoryOptions}
        />
      </FormProvider>
    </form>
  );
}
