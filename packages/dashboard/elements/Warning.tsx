import { cn } from '@/lib/utils';
import { Quote } from 'lucide-react';
import React from 'react';

export default function Warning({ className }: { className?: string }) {
  return (
    <div
      className={cn(
        `text-white w-[980px]  text-[13px] mt-10 font-semibold sticky bottom-0 flex flex-row ${className}}`
      )}
    >
      <Quote className='mr-2' /> This system is for authorized use only.
      Unauthorized access will result in immediate legal action leading up to
      prosecution as per governing laws. By accessing this system, you
      acknowledge that you are authorized to do so and that all data stored and
      processed here is confidential.
      <Quote className='mt-4' />
    </div>
  );
}
