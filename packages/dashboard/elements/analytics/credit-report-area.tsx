'use client';
import dynamic from 'next/dynamic';
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

import CreditReportsFilter from '../reports/credit-reports.filter';
import { trpc } from '@/providers/Providers';
import { Quarter } from '../../../shared/types/CreditReport';
import { SECTIONTYPE } from '../../../shared/types/Standard';
import { getTotalGeneralQuestionScore } from '@/lib/submissions';

export default function CreditReportArea({
  quarter,
  vendor,
}: {
  vendor: string;
  quarter: Quarter;
}) {
  const submissionsScoreData = trpc.creditReport.getCreditReports.useQuery(
    {
      vendor,
    },
    {
      enabled: !!(vendor && quarter),
      select(data) {
        const series = [
          {
            name: 'series1',
            data: [31, 40, 28, 51, 42, 109, 100],
          },
          {
            name: 'series2',
            data: [11, 32, 45, 32, 34, 52, 41],
          },
        ];
        const options = {
          chart: {
            height: 350,
            type: 'area',
          },
          dataLabels: {
            enabled: false,
          },
          stroke: {
            curve: 'smooth',
          },
          xaxis: {
            type: 'datetime',
            categories: [
              '2018-09-19T00:00:00.000Z',
              '2018-09-19T01:30:00.000Z',
              '2018-09-19T02:30:00.000Z',
              '2018-09-19T03:30:00.000Z',
              '2018-09-19T04:30:00.000Z',
              '2018-09-19T05:30:00.000Z',
              '2018-09-19T06:30:00.000Z',
            ],
          },
          tooltip: {
            x: {
              format: 'dd/MM/yy HH:mm',
            },
          },
        };
        // x
        return { series, options };
      },
    }
  );

  return (
    <div>
      <p className='font-semibold p-3'>Credit Reports</p>
      {/*    @ts-ignore */}
      <Chart
        // @ts-ignore
        options={submissionsScoreData.data?.options || {}}
        series={submissionsScoreData.data?.series || []}
        type='area'
        height={500}
      />
    </div>
  );
}
