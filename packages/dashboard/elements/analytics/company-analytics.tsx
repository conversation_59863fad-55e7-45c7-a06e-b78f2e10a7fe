'use client';
import React from 'react';
import { useSession } from 'next-auth/react';
import { trpc } from '@/providers/Providers';
import { CreditReportChart } from '../charts/credit-report-chart';
import { VulnerabilityReportChart } from '../charts/vulnerability-report-charts';
import VulnerabilityCumilativeBarChart from '../charts/vulnerability-cumilative.barchart';
import CumilativeVendorsVulnerabilities from '../charts/cumilative-vendors-vulnerabilities';
import AllVendorsVsocProcessCharts from '../charts/all-vendors-vsoc-charts';
import { Label } from '@/components/ui/label';
import { motion } from 'framer-motion';

// const AnimatedText = ({ text }: { text: string }) => {
//   const letters = text.split('');

//   const container = {
//     hidden: { opacity: 0 },
//     visible: (i = 1) => ({
//       opacity: 1,
//       transition: { staggerChildren: 0.05, delayChildren: 0.04 * i },
//     }),
//   };

//   const child = {
//     hidden: {
//       opacity: 0,
//       y: 20,
//     },
//     visible: {
//       opacity: 1,
//       y: 0,
//     },
//   };

//   return (
//     <motion.div
//       variants={container}
//       initial='hidden'
//       animate='visible'
//       className='flex'
//     >
//       {letters.map((letter, index) => (
//         <motion.span key={index} variants={child}>
//           {letter === ' ' ? '\u00A0' : letter}
//         </motion.span>
//       ))}
//     </motion.div>
//   );
// };

export default function CompanyAnalaytics() {
  const { data: session } = useSession();

  const vendors = trpc.company.getCompanies.useQuery(
    { reportsTo: session?.user.companies?.at(0) as string },
    { enabled: !!session?.user.companies?.at(0) }
  );
  if (!vendors?.data?.length) return null;
  return (
    <div className='flex flex-col items-center gap-14 '>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: 'easeInOut' }}
      >
        <Label className='text-5xl text-white font-semibold'>
          Vendor&#39;s Risk and Compliance Dashboard
        </Label>
      </motion.div>
      {/* <Label className='text-5xl text-white font-semibold'>
        <AnimatedText text='Vendor&#39;s Risk and Compliance Dashboard' />
      </Label> */}
      <div className='grid grid-cols-3 gap-2 px-10 bg-indigo-950  '>
        <div className='col-span-3 flex gap-2  '>
          <CumilativeVendorsVulnerabilities vendors={vendors.data} />
          <VulnerabilityReportChart vendors={vendors.data} />
          <AllVendorsVsocProcessCharts vendors={vendors.data} />
        </div>
        <div className='col-span-3 gap-2 flex  '>
          <CreditReportChart vendors={vendors.data} />
          <VulnerabilityCumilativeBarChart vendors={vendors.data} />
        </div>
      </div>
    </div>
  );
}
