import { FormComboBox } from '@/components/form/FormComboBox';
import {
  CompanyAnalyticsFilterProps,
  useCompanyAnalyticsFilter,
} from '@/hooks/filters/company-anayltics';
import { selectOptionsParser } from '@/lib/utils';

import { Quarter } from '../../../shared/types/CreditReport';
import { FormProvider } from 'react-hook-form';

export const CompanyAnalyticsFilter = ({
  methods,
  vendors,
}: CompanyAnalyticsFilterProps) => {
  return (
    <form className='flex gap-4 items-end'>
      <FormProvider {...methods}>
        <FormComboBox
          placeholder='Select Vendor'
          name='vendor'
          //   @ts-ignore
          options={selectOptionsParser(vendors, {
            labelKey: 'company',
            valKey: '_id',
          })}
        />

        <FormComboBox
          name='quarter'
          placeholder='select quarter'
          label='Quarter*'
          //   @ts-ignore
          options={Object.values(Quarter).map((_) => ({
            label: _,
            value: _,
          }))}
        />
      </FormProvider>
    </form>
  );
};
