import FormField from '@/components/form/FormField';
import { Input } from '@/components/ui/input';
import { useFileMethods } from '@/hooks/forms/file';
import { FileExtensions, MimeTypes } from '../../../shared/types/File';
import React, { useRef } from 'react';
import { FormProvider } from 'react-hook-form';
import toast from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import { UploadCloud } from 'lucide-react';
import { toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';

export default function CreateFileForm() {
  const methods = useFileMethods();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const resetFile = () => {
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        const ext = file.name.split('.').pop() as FileExtensions;
        const mimeType = file.type as MimeTypes;
        const fileSize = Number((file.size / (1024 * 1024)).toFixed(2));

        if (fileSize > 4) {
          resetFile();
          return toast.error('file size must be less than 4MB');
        }

        methods.setValue('base64', base64);
        methods.setValue('ext', ext);
        methods.setValue('mimeType', mimeType);
      };
      reader.readAsDataURL(file);
    }
  };

  const createFile = trpc.file.createFile.useMutation();
  const utils = trpc.useUtils();

  const handleSubmit = methods.handleSubmit((data) =>
    toastPromise({
      asyncFunc: createFile.mutateAsync(data),
      onSuccess() {
        methods.reset();
        resetFile();
        utils.file.getFiles.invalidate();
      },
    })
  );

  return (
    <form className='space-y-4' onSubmit={handleSubmit}>
      <FormProvider {...methods}>
        <FormField
          name='name'
          label='File name*'
          placeholder='Enter file name'
        />
        <div>
          <Input ref={fileInputRef} type='file' onChange={handleFileChange} />
          {methods.formState.errors.ext && (
            <p className='text-red-500'>Invalid Extension</p>
          )}
        </div>
        <Button>
          <UploadCloud className='size-4 mr-2' />
          Save
        </Button>
      </FormProvider>
    </form>
  );
}
