import { FormProvider, useForm } from 'react-hook-form';
import { FormTextField } from '@/components/form/FormTextField';
import FormFilePicker from '@/components/form/FormFilePicker';
import { z } from 'zod';
import { requestVulnerabilityClosureBySocToCompanyValidator } from '../../../shared/validators/assessment-submission.validator';
import { trpc } from '@/providers/Providers';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toastPromise } from '@/lib/utils';

export type Form = z.infer<
  typeof requestVulnerabilityClosureBySocToCompanyValidator
>;

export default function VulnerabilityClosureBySocForm({
  submissionId,
}: {
  submissionId: string;
}) {
  const methods = useForm<Form>({
    resolver: zodResolver(requestVulnerabilityClosureBySocToCompanyValidator),
    defaultValues: {
      submissionId,
      vulnerabilityClosureDescriptionBySoc: '',
    },
  });
  const vulnerabilityClosureSocToCompany =
    trpc.assessment.requestVulnerabilityClosureBySocToCompany.useMutation();

  const handleSubmit = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: vulnerabilityClosureSocToCompany.mutateAsync(data),
      success: 'Success',
      onSuccess() {
        methods.reset();
      },
    });
  });
  return (
    <form className='space-y-10' onSubmit={handleSubmit}>
      <FormProvider {...methods}>
        <FormTextField
          name='vulnerabilityClosureDescriptionBySoc'
          placeholder='Vulnerability closer description'
          label='Vulnerability Closer Description'
          className='min-h-[400px]'
        />
        <div>
          <Label>Soc Evidence</Label>
          <FormFilePicker
            fileName={`Closure evidence by soc  ${format(
              new Date(),
              'PPP hh:mm a'
            )}`}
            name='vulnerabilityClosureEvidenceBySoc'
          />
        </div>
        <Button className='float-right'>Save and Email</Button>
      </FormProvider>
    </form>
  );
}
