import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import { manageAssessmentValidator } from '../../../shared/validators/assessment.validator';
import React, { useCallback, useEffect } from 'react';
import { UseFieldArrayReturn, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';
import { FormSelect } from '@/components/form/FormSelect';
import { selectOptionsParser } from '@/lib/utils';
import { RouterOutput } from '../../../shared';
import { Button } from '@/components/ui/button';

type Form = z.infer<typeof manageAssessmentValidator>;

export default function AssessmentAssignmentForm({
  fieldArray: { append, fields, remove },
  users,
  sections,
  methods,
  questions,
}: {
  fieldArray: UseFieldArrayReturn<Form, 'assignments'>;
  users: RouterOutput['user']['getUsers'];
  sections: RouterOutput['standard']['getSections'];
  methods: UseFormReturn<Form>;
  questions: RouterOutput['questions']['getQuestions'];
}) {
  const assignments = methods.watch('assignments');

  const onSectionClear = useCallback(
    (section: string, idx: number) => {
      // clear questions
      const assignment = assignments[idx];
      const questionToRemove = questions.find(
        (question) => String(question.section) === section
      )?._id;
      if (questionToRemove) {
        assignment.questions = assignment.questions.filter(
          (qid) => qid !== String(questionToRemove)
        );
        methods.setValue('assignments', [...assignments]);
      }
    },
    [assignments, methods, questions]
  );

  const handleSelectedSection = (idx: number, selectedSection?: string) => {
    if (selectedSection) {
      const selectedSections = methods.getValues(`assignments.${idx}.sections`);
      const sectionExists = !selectedSections.some(
        (section) => section === selectedSection
      );

      const qs = questions
        .filter((question) => {
          return question.section._id === selectedSection;
        })
        .map((q) => String(q._id));

      const existingQuestions = methods.getValues(
        `assignments.${idx}.questions`
      );

      if (sectionExists) {
        methods.setValue(`assignments.${idx}.questions`, [
          ...existingQuestions,
          ...qs,
        ]);
      } else {
        // Handle removing questions
        methods.setValue(
          `assignments.${idx}.questions`,
          existingQuestions.filter((q) => !qs.includes(q))
        );
      }
    }
  };

  return (
    <div>
      <div>
        {fields.map((field, idx) => {
          return (
            <div key={field.id} className='bg-sky-800/50 p-4 rounded-lg mt-2 '>
              <FormSelect
                label='Select User'
                placeholder='Select a user'
                name={`assignments.${idx}.employee`}
                // @ts-ignore
                options={selectOptionsParser(users, {
                  labelKey: 'email',
                  valKey: '_id',
                })}
              />
              <FormComboBoxPopover
                name={`assignments.${idx}.sections`}
                label='Select User Sections'
                onChipSelect={(section) =>
                  onSectionClear(section as string, idx)
                }
                // @ts-ignore
                options={selectOptionsParser(sections, {
                  labelKey: 'section',
                  valKey: '_id',
                })}
                onValueSelect={(v) => handleSelectedSection(idx, v)}
              />

              <FormComboBoxPopover
                name={`assignments.${idx}.questions`}
                label='Select User Questions'
                options={questions
                  .filter((question) => {
                    return assignments[idx].sections.includes(
                      String(question.section._id)
                    );
                  })
                  .map((question) => ({
                    label: question.input.label,
                    value: String(question._id),
                  }))}
              />
            </div>
          );
        })}
      </div>
      <div className='space-x-2'>
        <Button
          type='button'
          onClick={() => {
            //@ts-ignore
            append({ employee: '', questions: [], sections: [] });
          }}
        >
          Add
        </Button>
        <Button
          type='button'
          onClick={() => fields.length > 1 && remove(fields.length - 1)}
          variant={'destructive'}
        >
          Remove
        </Button>
      </div>
    </div>
  );
}
