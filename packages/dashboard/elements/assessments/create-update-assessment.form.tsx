import { trpc } from '@/providers/Providers';
import { manageAssessmentValidator } from '../../../shared/validators/assessment.validator';
import { z } from 'zod';
import {
  FormProvider,
  UseFormReturn,
  useFieldArray,
  useForm,
} from 'react-hook-form';
import { FormSelect } from '@/components/form/FormSelect';
import { selectOptionsParser, toastPromise } from '@/lib/utils';
import FormField from '@/components/form/FormField';
import { Button } from '@/components/ui/button';
import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import { FormDatePicker } from '@/components/form/FormDatePicker';
import { getYear } from 'date-fns';

import { CompanyType } from '../../../shared/types/Company';
import { manageUserValidator } from '../../../shared/validators/user.validator';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { USER_ROLE } from '../../../shared/types/User';
import AssessmentAssignmentForm from './assessment-assignment.form';
import ManageUserForm from '../user/manage-user.form';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { ReorderAssessmentQuestions } from '@/app/admin/(standards)/assessments/reorder-questions';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Label } from '@/components/ui/label';
import { Separator } from '@radix-ui/react-separator';
import { useSession } from 'next-auth/react';

export type ManageAssessmentForm = z.infer<typeof manageAssessmentValidator>;
type UserForm = z.infer<typeof manageUserValidator>;

type Props = {
  methods: UseFormReturn<ManageAssessmentForm>;
  isUpdate?: boolean;
};
export default function CreateUpdateAssessmentForm({
  methods,
  isUpdate,
}: Props) {
  const { watch } = methods;

  const selectedSections = watch('sections');

  // clear assignment sections
  const handleSectionSelect = (section: string | undefined) => {
    if (!section) return;
    let assignments = methods.getValues('assignments');

    // handle assignmet sections
    assignments = assignments.map((assignment) => {
      assignment.sections = assignment.sections.filter(
        (item) => item !== section
      );
      return assignment;
    });

    // handle assignment questions
    const questionsToRemove = (questions.data || [])
      .filter((item) => String(item.section) === section)
      .map((item) => String(item._id));
    assignments.map((assignment) => {
      assignment.questions = assignment.questions.filter(
        (item) => !questionsToRemove.includes(item)
      );
      return assignment;
    });

    methods.setValue('assignments', assignments);
  };

  const manageUserMethods = useForm<UserForm>({
    resolver: zodResolver(manageUserValidator),
    defaultValues: {
      email: '',
      password: '',
      companies: [],
      role: USER_ROLE.EMPLOYEE,
      user: '',
      contact: {
        countryCode: '',
        firstName: '',
        lastName: '',
        phone: '',
        title: '',
      },
    },
  });

  const assignmentFieldArray = useFieldArray({
    control: methods.control,
    name: 'assignments',
  });

  const selectedCompany = methods.watch('company');
  const selectedVendor = methods.watch('vendor');

  const vendors = trpc.company.getCompanies.useQuery(
    {
      type: [CompanyType.VENDOR],
      reportsTo: selectedCompany,
    },
    {
      enabled: !!selectedCompany,
    }
  );

  const usersByVendor = trpc.user.getUsers.useQuery(
    {
      role: USER_ROLE.EMPLOYEE,
      companies: selectedVendor,
    },
    { enabled: !!selectedVendor }
  );

  // const standards = trpc.standard.getStandards.useQuery();
  const companies = trpc.company.getCompanies.useQuery({
    type: [CompanyType.COMPANY],
  });

  const user = useSession();

  const userCompanies = trpc.company.getCompanies.useQuery({
    companies: user?.data?.user?.companies,
    type: [CompanyType.COMPANY],
  });

  const companyAdmin = user.data?.user?.role === USER_ROLE.COMPANY_ADMIN;

  const sections = trpc.standard.getSections.useQuery();

  const questions = trpc.questions.getQuestions.useQuery(
    {
      section: selectedSections,
    },
    { enabled: !!selectedSections }
  );

  const utils = trpc.useUtils();
  const createAssessment = trpc.assessment.createAssessment.useMutation();

  const handleSubmit = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: createAssessment.mutateAsync(data),
      success: 'assessment created successfully',
      onSuccess() {
        methods.reset();
        utils.assessment.getAssessments.invalidate();
      },
    });
  });

  const filteredCompanies = companyAdmin ? userCompanies.data : companies.data;

  return (
    <div className='flex flex-1 gap-3 mt-3  '>
      <div className='  '>
        <Label className='text-2xl  font-semibold text-white flex justify-center underline p-3 '>
          Create Assessment
        </Label>
        <div className='flex-1 border-r  rounded-lg  bg-white'>
          {/* Form */}

          <form onSubmit={handleSubmit} className='space-y-3 min-w-[700px] p-4'>
            <FormProvider {...methods}>
              <div>
                <label className='font-semibold   '>Company</label>
                <FormSelect
                  name='company'
                  label=''
                  placeholder='Select Company'
                  options={selectOptionsParser(filteredCompanies, {
                    labelKey: 'company',
                    valKey: '_id',
                  })}
                />
              </div>
              <div>
                <label className='font-semibold   '>Vendor</label>
                <FormSelect
                  name='vendor'
                  showSearch
                  label=''
                  placeholder='Select Vendor'
                  options={selectOptionsParser(vendors.data, {
                    labelKey: 'company',
                    valKey: '_id',
                  })}
                />
              </div>
              <div>
                <label className='font-semibold   '>Assessment</label>
                <FormField
                  name='name'
                  label=''
                  placeholder='Enter assessment name'
                />
              </div>

              <div>
                <label className='font-semibold   '>Expiry </label>
                <FormDatePicker
                  name='expiresAt'
                  label=''
                  placeholder='Select Expiry date'
                  captionLayout='dropdown-buttons'
                  fromYear={getYear(new Date())}
                  toYear={getYear(new Date()) + 30}
                />
              </div>
              <div>
                <label className='font-semibold'>
                  Company Engagement Manager
                </label>
                <div className='grid grid-cols-2 gap-2 border-2 p-3'>
                  <FormField
                    name='companyEngagementManager.firstName'
                    label='First name'
                    placeholder='Enter first name'
                  />
                  <FormField
                    name='companyEngagementManager.lastName'
                    label='Last name'
                    placeholder='Enter last name'
                  />
                  <FormField
                    name='companyEngagementManager.title'
                    label='Title'
                    placeholder='Enter your job title'
                  />
                  <FormField
                    name='companyEngagementManager.email'
                    label='Email'
                    placeholder='Add email'
                  />
                  <FormField
                    name='companyEngagementManager.countryCode'
                    label='Country Code'
                    placeholder='Enter country code'
                    type='number'
                  />
                  <FormField
                    name='companyEngagementManager.phone'
                    label='Contact'
                    placeholder='Add contact number '
                    type='number'
                  />
                </div>
              </div>
              <div className='flex flex-col gap-2'>
                <label htmlFor='' className='font-semibold'>
                  Vendor Responded Assessment Manager
                </label>
                <div className='grid grid-cols-2 gap-2 border-2 p-3'>
                  <FormField
                    name='vendorAssessmentManager.firstName'
                    label='First name'
                    placeholder='Enter first name'
                  />
                  <FormField
                    name='vendorAssessmentManager.lastName'
                    label='Last name'
                    placeholder='Enter last name'
                  />
                  <FormField
                    name='vendorAssessmentManager.title'
                    label='Title'
                    placeholder='Enter your job title'
                  />
                  <FormField
                    name='vendorAssessmentManager.email'
                    label='Email'
                    placeholder='Add email'
                  />
                  <FormField
                    name='vendorAssessmentManager.countryCode'
                    label='Country Code'
                    placeholder='Enter country code'
                    type='number'
                  />
                  <FormField
                    name='vendorAssessmentManager.phone'
                    label='Contact'
                    placeholder='Add contact number '
                    type='number'
                  />
                </div>
              </div>

              <div>
                <label className='font-semibold'>
                  Select Assessment Sections
                </label>
                <FormComboBoxPopover
                  name={`sections`}
                  label=''
                  onChipSelect={handleSectionSelect}
                  // @ts-ignore
                  options={selectOptionsParser(sections.data || [], {
                    labelKey: 'section',
                    valKey: '_id',
                  })}
                />
              </div>

              {/* Assignments */}
              <div>
                <label htmlFor='' className='font-semibold'>
                  Assignments
                </label>

                <div>
                  {selectedVendor && (
                    <Dialog
                      onOpenChange={() => {
                        manageUserMethods.setValue('companies', [
                          selectedVendor,
                        ]);

                        manageUserMethods.setValue('role', USER_ROLE.EMPLOYEE);
                      }}
                    >
                      <DialogTrigger asChild>
                        <Button>Create users</Button>
                      </DialogTrigger>
                      <DialogContent>
                        <ManageUserForm methods={manageUserMethods} />
                      </DialogContent>
                    </Dialog>
                  )}
                  <AssessmentAssignmentForm
                    fieldArray={assignmentFieldArray}
                    users={usersByVendor.data || []}
                    sections={
                      sections.data?.filter((section) =>
                        selectedSections.includes(String(section._id))
                      ) || []
                    }
                    methods={methods}
                    questions={questions.data || []}
                  />
                </div>
              </div>
            </FormProvider>

            <div className=' bg-white'>
              <Button type='submit'>
                {isUpdate ? 'Update Assessment' : 'Create Assessment'}
              </Button>
            </div>
          </form>
        </div>
      </div>
      <Separator
        orientation='vertical'
        className='p-[1px] bg-white rounded-lg '
      />
      <ScrollArea className=' min-w-[800px]  '>
        <label className='text-2xl  font-semibold text-white flex justify-center underline p-3 '>
          Sections and Questions Template
        </label>

        <ReorderAssessmentQuestions
          methods={methods}
          fieldArray={assignmentFieldArray}
          questions={questions.data || []}
          sections={
            sections.data?.filter((section) =>
              selectedSections.includes(String(section._id))
            ) || []
          }
        />
      </ScrollArea>
    </div>
  );
}
