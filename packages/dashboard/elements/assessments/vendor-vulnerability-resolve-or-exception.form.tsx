import { FormSelect } from '@/components/form/FormSelect';
import { FormTextField } from '@/components/form/FormTextField';
import { Button } from '@/components/ui/button';
import { updateVulnerabilityResolveStatusByEmployeeValidator } from '../../../shared/validators/assessment-submission.validator';
import React from 'react';
import { FormProvider } from 'react-hook-form';
import { z } from 'zod';
import { VulnerabilityResolveStatus } from '../../../shared/types/AssessmentSubmission';
import { EmployeeResolveOrExceptionFormProps } from '@/hooks/forms/employeeResolveOrException';
import FormFilePicker from '@/components/form/FormFilePicker';
import { format } from 'date-fns';

export type Form = z.infer<
  typeof updateVulnerabilityResolveStatusByEmployeeValidator
>;

export default function VendorVulnerabilityResolveOrExceptionForm({
  methods,
  handleSubmit,
}: EmployeeResolveOrExceptionFormProps) {
  return (
    <form onSubmit={methods.handleSubmit(handleSubmit)}>
      <FormProvider {...methods}>
        <FormSelect
          name='resolveStatus'
          label='Status'
          placeholder='Select Status'
          options={[
            {
              label: VulnerabilityResolveStatus.RESOLVED.toUpperCase(),
              value: VulnerabilityResolveStatus.RESOLVED,
            },
            {
              label: VulnerabilityResolveStatus.EXCEPTION.toUpperCase(),
              value: VulnerabilityResolveStatus.EXCEPTION,
            },
          ]}
        />
        <FormTextField
          name='resolveDescription'
          label='Add Comments'
          placeholder='Enter reason for rejection (Max-512 characters)'
          className='h-96'
        />
        <div className='my-4'>
          Choose Evidence
          <FormFilePicker
            name='vulnerabilityResolvedOrExceptionEvidence'
            fileName={`${methods.watch('resolveStatus')} Evidence ${format(
              new Date(),
              'PPP hh:mm a'
            )}`}
          />
        </div>
        <Button>Save and Email</Button>
      </FormProvider>
    </form>
  );
}
