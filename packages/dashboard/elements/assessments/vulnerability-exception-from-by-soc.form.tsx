import { Form<PERSON>rovider, useForm } from 'react-hook-form';
import { FormTextField } from '@/components/form/FormTextField';
import FormFilePicker from '@/components/form/FormFilePicker';
import { z } from 'zod';
import {
  requestVulnerabilityClosureBySocToCompanyValidator,
  requestVulnerabilityExceptionSocToCompanyValidator,
} from '../../../shared/validators/assessment-submission.validator';
import { trpc } from '@/providers/Providers';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toastPromise } from '@/lib/utils';
import { requestVulnerabilityExceptionSocToCompany } from '../../../backend/controllers/assessment-submission.controller';

type Form = z.infer<typeof requestVulnerabilityExceptionSocToCompanyValidator>;

export default function VulnerabilityExceptionBySocForm({
  submissionId,
}: {
  submissionId: string;
}) {
  const methods = useForm<Form>({
    resolver: zodResolver(requestVulnerabilityExceptionSocToCompanyValidator),
    defaultValues: {
      submissionId,
      vulnerabilityExceptionApprovalDescriptionBySoc: '',
    },
  });
  const vulnerabilityExceptionSocToCompany =
    trpc.assessment.requestVulnerabilityExceptionSocToCompany.useMutation();

  const handleSubmit = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: vulnerabilityExceptionSocToCompany.mutateAsync(data),
      success: 'Success',
      onSuccess() {
        methods.reset();
      },
    });
  });

  return (
    <form className='space-y-10' onSubmit={handleSubmit}>
      <FormProvider {...methods}>
        <FormTextField
          name='vulnerabilityExceptionApprovalDescriptionBySoc'
          placeholder='Vulnerability Exception description'
          label='Vulnerability Exception Description'
          className='min-h-[400px]'
        />
        <div>
          <Label>Soc Evidence</Label>
          <FormFilePicker
            fileName={`Exception evidence by soc  ${format(
              new Date(),
              'PPP hh:mm a'
            )}`}
            name='vulnerabilityExceptionApprovalEvidenceBySoc'
          />
        </div>
        <Button className='float-right'>Save and Email</Button>
      </FormProvider>
    </form>
  );
}
