import FormRichTextEditor from '@/components/form/FormRichTextEditor';
import { Button } from '@/components/ui/button';
import { toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import { zodResolver } from '@hookform/resolvers/zod';
import { method } from 'lodash';
import { approvedVulnerabilityExceptionSocToVendorValidator } from '../../../shared/validators/assessment-submission.validator';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';

type Form = z.infer<typeof approvedVulnerabilityExceptionSocToVendorValidator>;

export default function AssessmentExceptionApprovedForm({
  submissionId,
}: {
  submissionId: string;
}) {
  const methods = useForm<Form>({
    defaultValues: {
      vulnerabilityExceptionStatusDateSocToVendor: new Date(),
      submissionId,
      vulnerabilityExceptionStatusSocToVendor: '',
    },
    resolver: zodResolver(approvedVulnerabilityExceptionSocToVendorValidator),
  });
  const approvedVulnerabilityExceptionToVendorBySoc =
    trpc.assessment.requestVulnerabilityExceptionSocToVendorApproved.useMutation();

  const handleSubmit = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: approvedVulnerabilityExceptionToVendorBySoc.mutateAsync(data),
      success: 'Success',
      onSuccess() {
        methods.reset();
      },
    });
  });
  return (
    <form action='' onSubmit={handleSubmit} className='space-y-4'>
      <FormProvider {...methods}>
        <FormRichTextEditor
          name='vulnerabilityExceptionStatusSocToVendor'
          label='Soc description '
        />
        <Button className='float-right'>Save and Email</Button>
      </FormProvider>
    </form>
  );
}
