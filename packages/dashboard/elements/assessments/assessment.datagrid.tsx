"use client";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { trpc } from "@/providers/Providers";
import { differenceInDays, format } from "date-fns";
import { Button } from "@/components/ui/button";
import { RouterOutput } from "../../../shared";
import { EyeIcon, Pencil } from "lucide-react";
import { useRouter } from "next/navigation";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { useMemo } from "react";
import { useRole } from "@/hooks/useRole";
import { useSession } from "next-auth/react";

export default function AssessmentDataGrid({
  assessments,
  showActions = true,
  handleUpdate,
}: {
  assessments: RouterOutput["assessment"]["getAssessments"];
  showActions?: boolean;
  handleUpdate?: (
    assessment: RouterOutput["assessment"]["getAssessments"][number]
  ) => void;
}) {
  const session = useSession();

  const adminCompanies = session.data?.user.companies;

  const sortedCompanies = assessments.filter((assessment) =>
    adminCompanies?.some(
      (adminCompany) => adminCompany === String(assessment.company?._id)
    )
  );

  const { isCompanyAdmin } = useRole();

  const sortedData = useMemo(() => {
    return (isCompanyAdmin ? sortedCompanies : assessments || []).sort(
      (a, b) => {
        return (
          new Date(b.expiresAt).getTime() - new Date(a.expiresAt).getTime()
        );
      }
    );
  }, [assessments, isCompanyAdmin, sortedCompanies]);

  const requiredData = sortedData.filter((d) => !d.name.includes("$"));

  return (
    <ScrollArea className="ph1">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Company</TableHead>
            <TableHead>Vendor</TableHead>
            <TableHead>Assessment Name</TableHead>
            <TableHead>Sections</TableHead>
            <TableHead>Created Date</TableHead>
            <TableHead>Expiry Date</TableHead>

            {showActions && <TableHead>Users</TableHead>}
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {requiredData.map((as) => {
            const expiry = new Date(as?.expiresAt);
            const days = differenceInDays(expiry, new Date());

            return (
              <TableRow key={String(as._id)}>
                <TableCell>{as.company?.company}</TableCell>
                <TableCell>{as.vendor?.company}</TableCell>
                <TableCell>{as.name}</TableCell>
                <TableCell>
                  {as.sections?.map((c) => {
                    return <div key={String(c._id)}>{c.section}</div>;
                  })}
                </TableCell>

                <TableCell className="text-center">
                  {/* @ts-ignore */}
                  {format(as.createdAt, "MMM d, yyyy")}
                </TableCell>
                <TableCell
                  className={cn(
                    days > 0
                      ? "text-green-500 text-center"
                      : days <= 0
                      ? "text-red-500 text-center"
                      : "text-center"
                  )}
                >
                  {/* @ts-ignore */}
                  {format(as.expiresAt, "MMM d, yyyy")}
                </TableCell>
                {showActions && (
                  <TableCell>
                    <Sheet>
                      <SheetTrigger asChild>
                        <Button>Users</Button>
                      </SheetTrigger>
                      <SheetContent className="min-w-[40vw]">
                        <SheetHeader>
                          <SheetTitle>Assessment Details</SheetTitle>

                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>No </TableHead>
                                <TableHead>User Mail </TableHead>
                                <TableHead>Sections</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {as.assignments?.map((asg, idx) => {
                                return (
                                  <TableRow key={idx}>
                                    <TableCell>{idx + 1}</TableCell>
                                    <TableCell>{asg.employee.email}</TableCell>
                                    <TableCell>
                                      {asg.sections?.map((sec, idx) => {
                                        return (
                                          <Badge key={idx}>{sec.section}</Badge>
                                        );
                                      })}
                                    </TableCell>
                                  </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        </SheetHeader>
                      </SheetContent>
                    </Sheet>
                  </TableCell>
                )}

                <TableCell>
                  <Button onClick={() => handleUpdate?.(as)}>
                    <Pencil className="size-4 mr-2" />
                    Update
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </ScrollArea>
  );
}
