import FormField from '@/components/form/FormField';
import { FormSelect } from '@/components/form/FormSelect';
import { FormTextField } from '@/components/form/FormTextField';
import { VendorAcceptanceFormProps } from '@/hooks/forms/vulnerability';
import { VendorAcceptanceStatus } from '../../../shared/types/AssessmentSubmission';
import React from 'react';
import { FormProvider } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { trpc } from '@/providers/Providers';
import { toastPromise } from '@/lib/utils';
import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import { Input } from '@/components/ui/input';
import { MoveRight } from 'lucide-react';
import FormFilePicker from '@/components/form/FormFilePicker';
import { format } from 'date-fns';

export default function VendorAcceptanceForm({
  methods,
}: VendorAcceptanceFormProps) {
  const vendorAcceptanceStatus =
    trpc.assessment.vendorAcceptanceStatus.useMutation();

  const handleSubmit = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: vendorAcceptanceStatus.mutateAsync(data),
      onSuccess: () => methods.reset(),
    });
  });

  return (
    <form className='space-y-4'>
      <FormProvider {...methods}>
        <FormSelect
          name='vendorAcceptanceStatus'
          label='Status'
          placeholder='Select Status'
          options={[
            {
              label: 'Acknowledge',
              value: VendorAcceptanceStatus.ACCEPT,
            },
            {
              label: 'False Positive',
              value: VendorAcceptanceStatus.REJECT,
            },
          ]}
        />
        <FormTextField
          name='vendorRejectReason'
          label='Add Comments'
          placeholder='Enter reason for rejection (Max-512 characters)'
          className='h-96'
        />
        <div className='flex flex-row justify-between items-center gap-2 font-bold '>
          <FormFilePicker
            name='rejectionEvidence'
            fileName={`Accept/Reject ${format(new Date(), 'PPP hh:mm a')}`}
          />
        </div>
        <Button onClick={handleSubmit}>Save and Email</Button>
      </FormProvider>
    </form>
  );
}
