import React, { useRef, useState } from 'react';
import { UploadCloud } from 'lucide-react';
import { FileExtensions, MimeTypes } from '../../../shared/types/File';
import toast from 'react-hot-toast';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

export default function QuestionFilePicker({
  onChange,
}: {
  onChange: (data: {
    base64: string;
    ext: FileExtensions;
    mimeType: MimeTypes;
  }) => void;
}) {
  // File State
  const [base64, setBase64] = useState('');
  const [ext, setExt] = useState<FileExtensions>();
  const [mimeType, setMimeType] = useState<MimeTypes>();

  const fileInputRef = useRef<HTMLInputElement>(null);
  const resetFile = () => {
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        const ext = file.name.split('.').pop() as FileExtensions;
        const mimeType = file.type as MimeTypes;
        const fileSize = Number((file.size / (1024 * 1024)).toFixed(2));

        //   File size
        const maxFileSizeInMb = 10;
        if (fileSize > maxFileSizeInMb) {
          resetFile();
          return toast.error(
            `file size must be less than ${maxFileSizeInMb}MB`
          );
        }
        onChange({ base64, ext, mimeType });
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className='space-y-4 flex flex-row items-center justify-between gap-2'>
      <div>
        <Input
          ref={fileInputRef}
          type='file'
          onChange={handleFileChange}
          accept='.json, .xml, .jpeg, .jpg, .png, .gif, .mp4, .mp3, .pdf, .zip, .x-zip-compressed'
        />
      </div>
    </div>
  );
}
