import { zodResolver } from '@hookform/resolvers/zod';
import { requestVulnerabilityExceptionCompanyToSocApprovalValidator } from '../../../shared/validators/assessment-submission.validator';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';
import { CompanyExceptionApproval } from '../../../shared/types/AssessmentSubmission';
import { trpc } from '@/providers/Providers';
import FormField from '@/components/form/FormField';
import { FormSelect } from '@/components/form/FormSelect';
import FormFilePicker from '@/components/form/FormFilePicker';
import { FormDatePicker } from '@/components/form/FormDatePicker';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { toastPromise } from '@/lib/utils';
import { FormTextField } from '@/components/form/FormTextField';
import { RouterOutput } from '../../../shared';

export type Form = z.infer<
  typeof requestVulnerabilityExceptionCompanyToSocApprovalValidator
>;
export default function CompanyExceptionApprovalForm({
  submissionId,
}: {
  submissionId: string;
}) {
  const methods = useForm<Form>({
    resolver: zodResolver(
      requestVulnerabilityExceptionCompanyToSocApprovalValidator
    ),
    defaultValues: {
      submissionId,
      companyExceptionApproval: '' as CompanyExceptionApproval,
      companyExceptionApprovalDescripton: '',
      companyExceptionEndDate: new Date(),
    },
  });

  const requestVulnerabilityExceptionCompany =
    trpc.assessment.requestVulnerabilityExceptionCompanyToSocApproval.useMutation();

  const handleSubmit = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: requestVulnerabilityExceptionCompany.mutateAsync(data),
      success: 'Success',
      onSuccess() {
        methods.reset();
      },
    });
  });
  return (
    <form action='' onSubmit={handleSubmit} className='space-y-2'>
      <label htmlFor='' className='pl-64 text-xl font-bold underline'>
        Customer Vulnerability Exception Aprroval{' '}
      </label>
      <FormProvider {...methods}>
        <FormSelect
          name='companyExceptionApproval'
          placeholder='Select Approval State'
          label='Exception Approval'
          options={[
            {
              label: 'ACCEPT ',
              value: CompanyExceptionApproval.ACCEPTED,
            },
            {
              label: 'REJECT ',
              value: CompanyExceptionApproval.REJECTED,
            },
          ]}
        />
        <FormDatePicker
          name='companyExceptionEndDate'
          placeholder='Select Date'
          label='Select Exception End-Date'
        />
        <FormTextField
          name='companyExceptionApprovalDescripton'
          placeholder='Description'
          label='Exception Description'
          className='h-96'
        />
        <FormFilePicker
          name='companyExceptionApprovalEvidence'
          fileName={`Exception Accept/Reject Evidence ${format(
            new Date(),
            'PPP hh:mm a'
          )}`}
        />
        <Button>Save and Email</Button>
      </FormProvider>
    </form>
  );
}
