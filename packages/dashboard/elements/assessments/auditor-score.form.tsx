import FormField from '@/components/form/FormField';
import { FormSelect } from '@/components/form/FormSelect';
import { AuditorFormProps } from '@/hooks/forms/auditor';
import { FormProvider } from 'react-hook-form';
import { FormTextField } from '@/components/form/FormTextField';
import { Button } from '@/components/ui/button';
import { AuditorScore } from '../../../shared/types/AssessmentSubmission';

export default function AuditorScoreForm({
  methods,
  handleSubmit,
}: AuditorFormProps) {
  return (
    <form className='space-y-4'>
      <FormProvider {...methods}>
        <FormField
          name='score'
          label='Auditor Score'
          placeholder='Enter Score'
        />
        <FormTextField
          name='remarks'
          label='Remarks'
          placeholder='Enter remarks if any'
        />

        <Button onClick={methods.handleSubmit(handleSubmit)}>Save</Button>
      </FormProvider>
    </form>
  );
}
