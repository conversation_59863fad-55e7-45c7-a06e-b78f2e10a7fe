import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { RouterOutput } from '../../../shared';
import React from 'react';
import { useDownload } from '@/hooks/useDownload';
import { CRITICALITY_LEVELS } from '../../../shared/types/Company';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
type AssesmentSubmission =
  RouterOutput['assessment']['getAssessmentSubmissions'][number];

export default function VulnerabilityExceptionDataGrid({
  submission,
}: {
  submission: AssesmentSubmission;
}) {
  const download = useDownload();
  return (
    <div>
      <Table>
        <TableRow>
          <TableHead className='text-black font-semibold  '>
            Inventory
          </TableHead>
          <TableCell>{submission?.submission?.question?.input.label}</TableCell>
        </TableRow>
        <TableRow>
          <TableHead className='text-black font-semibold  '>
            Inventory Details
          </TableHead>
          <TableCell>{submission.submission.answer}</TableCell>
        </TableRow>
      </Table>

      <Table>
        <TableHeader>
          <TableRow className='text-center'>
            <TableHead className='text-black font-semibold  '>
              Cvss Score
            </TableHead>
            <TableHead className='text-black font-semibold'>
              VendorAi Score
            </TableHead>
            <TableHead className='text-black font-semibold'>
              Criticality
            </TableHead>
            <TableHead className='text-black font-semibold'>
              MSA/SOW Remediation Timelines
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow className='text-center'>
            <TableCell>{submission.vulnerability.cvssScore}</TableCell>
            <TableCell>{submission.vulnerability.score}</TableCell>
            <TableCell
              className={cn(
                'font-bold',
                // @ts-ignore
                submission?.vulnerability?.criticalityLevel ===
                  CRITICALITY_LEVELS.HIGH && 'text-rose-600 w',
                // @ts-ignore
                submission?.vulnerability?.criticalityLevel ===
                  CRITICALITY_LEVELS.CRITICAL && 'text-red-700',
                // @ts-ignore
                submission?.vulnerability?.criticalityLevel ===
                  CRITICALITY_LEVELS.MEDIUM && 'text-orange-600',
                // @ts-ignore
                submission?.vulnerability?.criticalityLevel ===
                  CRITICALITY_LEVELS.LOW && 'text-yellow-600',
                // @ts-ignore
                submission?.vulnerability?.criticalityLevel ===
                  CRITICALITY_LEVELS.VERYLOW && 'text-green-700'
              )}
            >
              {submission?.vulnerability?.criticalityLevel}
            </TableCell>
            <TableCell>
              {submission.vendor?.criticalityLevels?.map((c, idx) => (
                <div key={idx}>
                  {submission.vulnerability?.criticalityLevel === c.level
                    ? `${c.timeDuration} Days`
                    : ''}
                </div>
              ))}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      <Table>
        <TableRow>
          <TableHead className='text-black font-semibold  '>
            Vulnerability Created Date
          </TableHead>
          <TableCell>
            {/* @ts-ignore */}
            {format(submission.vulnerability?.createdAt, 'MMM d,yyyy')}
          </TableCell>
        </TableRow>
        <TableRow>
          <TableHead className='text-black font-semibold  '>
            Vulnerability Description
          </TableHead>
          <TableCell>
            <div
              dangerouslySetInnerHTML={{
                __html: submission.vulnerability?.remarks as string,
              }}
              className='text-lg'
            />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableHead className='text-black font-semibold  '>
            Vendor Exception Reason
          </TableHead>
          <TableCell>{submission.vulnerability.resolveDescription}</TableCell>
        </TableRow>
        <TableRow>
          <TableHead className='text-black font-semibold  '>
            Soc Exception Reason
          </TableHead>
          <TableCell>
            {
              submission.vulnerability
                ?.vulnerabilityExceptionApprovalDescriptionBySoc
            }
          </TableCell>
        </TableRow>
        <TableRow>
          <TableHead className='text-black font-semibold  '>
            Vendor Exception Evidence
          </TableHead>
          <TableCell>
            <Button size={'icon'}>
              <Download
                onClick={() => {
                  download({
                    fileId: submission.vulnerability
                      .vulnerabilityResolvedOrExceptionEvidence as string,
                  });
                }}
              />
            </Button>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableHead className='text-black font-semibold  '>
            Soc Exception Evidence
          </TableHead>
          <TableCell>
            <Button size={'icon'}>
              <Download
                onClick={() => {
                  download({
                    fileId: submission.vulnerability
                      .vulnerabilityResolvedOrExceptionEvidence as string,
                  });
                }}
              />
            </Button>
          </TableCell>
        </TableRow>
      </Table>
    </div>
  );
}
