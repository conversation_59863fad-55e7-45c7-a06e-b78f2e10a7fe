import { FormSelect } from '@/components/form/FormSelect';
import { FormTextField } from '@/components/form/FormTextField';
import { Button } from '@/components/ui/button';
import { approveVulnerabilityClosureRequestByCompanyToSocValidator } from '../../../shared/validators/assessment-submission.validator';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { VulnerabilityClosureStatus } from '../../../shared/types/AssessmentSubmission';
import { toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import FormFilePicker from '@/components/form/FormFilePicker';
import { format } from 'date-fns';
import { Label } from '@/components/ui/label';

export type Form = z.infer<
  typeof approveVulnerabilityClosureRequestByCompanyToSocValidator
>;
export default function CompanyClosureApprovalForm({
  submissionId,
}: {
  submissionId: string;
}) {
  const methods = useForm<Form>({
    resolver: zodResolver(
      approveVulnerabilityClosureRequestByCompanyToSocValidator
    ),
    defaultValues: {
      submissionId,
      vulnerabilityClosureCompanyApprovalStatus:
        '' as VulnerabilityClosureStatus,
      vulnerabilityClosureDescriptionByCompany: '',
    },
  });

  const companyCloserApproval =
    trpc.assessment.approveVulnerabilityClosureRequestByCompanyToSoc.useMutation();

  const handleSubmit = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: companyCloserApproval.mutateAsync(data),
      success: 'success',
      onSuccess() {
        methods.reset();
      },
    });
  });

  return (
    <form className='space-y-4' onSubmit={handleSubmit}>
      <FormProvider {...methods}>
        <FormSelect
          name='vulnerabilityClosureCompanyApprovalStatus'
          placeholder='Select Approval Status'
          label='Closure approval Status'
          options={[
            {
              label: 'Require reassessment ',
              value: VulnerabilityClosureStatus.OPEN,
            },
            {
              label: 'Close  vulnerability  ',
              value: VulnerabilityClosureStatus.CLOSED,
            },
          ]}
        />
        <FormTextField
          name='vulnerabilityClosureDescriptionByCompany'
          placeholder='Description'
          label='Closure Description (required)'
        />
        <div>
          <Label>Closure Evidence</Label>
          <FormFilePicker
            name='vulnerabilityClosureEvidenceByCompany'
            fileName={`Company Closure Approval Evidence ${format(
              new Date(),
              'PPP hh:mm a'
            )}`}
          />
        </div>
        <Button>Save</Button>
      </FormProvider>
    </form>
  );
}
