import FormField from '@/components/form/FormField';
import { FormSelect } from '@/components/form/FormSelect';
import { Button } from '@/components/ui/button';
import { ComplianceFormProps } from '@/hooks/forms/compliance';
import { Plus } from 'lucide-react';
import { useEffect } from 'react';
import { FormProvider, useFieldArray } from 'react-hook-form';

export default function ComplianceForm({
  methods,
  onSubmit,
  assignmentIndex,
  assessmentId,
}: ComplianceFormProps) {
  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: 'files',
  });

  useEffect(() => {
    methods.setValue('assessmentId', assessmentId);
    methods.setValue('assignmentIndex', assignmentIndex);
  }, [assessmentId, assignmentIndex, methods]);

  return (
    <div>
      <form action='' onSubmit={methods.handleSubmit(onSubmit)}>
        <FormProvider {...methods}>
          {fields.map((m, idx) => {
            return (
              <div key={m.id} className='grid grid-cols-2 gap-2 mb-4'>
                <FormSelect
                  name={`files.${idx}.compliance`}
                  label='Compliance Standard'
                  placeholder='Select compliance'
                  options={[
                    {
                      label:
                        'The Payment Card Industry Data Security Standard (PCI DSS)',
                      value: 'PCI_DSS',
                    },
                    {
                      label:
                        'The International Organization for Standardization (ISO)',
                      value: 'ISO_27001',
                    },
                    {
                      label:
                        'The Health Insurance Portability and Accountability Act (HIPAA)',
                      value: 'HIPAA',
                    },
                    {
                      label: 'The General Data Protection Regulation (GDPR)',
                      value: 'GDPR',
                    },
                    { label: 'Sarbanes-Oxley Act (SOX) 1', value: 'SOX_1' },
                    { label: 'Sarbanes-Oxley Act (SOX)2', value: 'SOX_2' },
                    { label: 'Gramm- Leach-Bliley Act (GLBA)', value: 'GLBA' },
                    {
                      label:
                        'The Federal Information Security Management Act (FISMA)',
                      value: 'FISMA',
                    },
                  ]}
                />
                <div className='mt-2.5'>
                  <FormField
                    name={`files.${idx}.file`}
                    label='File'
                    type='file'
                    s3FilePath='assessment-documents'
                  />
                </div>
              </div>
            );
          })}
          <div className='flex justify-end gap-4'>
            <Button
              type='button'
              onClick={() => append({ file: '', compliance: '' })}
            >
              Add Compliance
            </Button>
            <Button
              type='button'
              onClick={() => fields.length > 1 && remove(fields.length - 1)}
              variant={'destructive'}
            >
              Remove
            </Button>
          </div>
          <div className='  flex items-center justify-center'>
            <Button>Submit Compliance</Button>
          </div>
        </FormProvider>
      </form>
    </div>
  );
}
