'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, Cell, <PERSON>Axis, YAxis } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';

// Define a type for the props
type ScoreReportChartProps = {
  sectionsData: { section: string; percentage: number }[];
};

const chartConfig = {
  percentage: {
    label: 'Percentage',
  },
} satisfies ChartConfig;

export function ScoreReportChart({ sectionsData }: ScoreReportChartProps) {
  // Define your gradient IDs
  const gradients = [
    { id: 'grad1', colors: ['#f87171', '#facc15'] }, // Red to Yellow
    { id: 'grad2', colors: ['#60a5fa', '#a78bfa'] }, // Blue to Purple
    { id: 'grad3', colors: ['#34d399', '#14b8a6'] }, // Green to Teal
    { id: 'grad4', colors: ['#f472b6', '#f87171'] }, // Pink to Red
    { id: 'grad5', colors: ['#818cf8', '#60a5fa'] }, // Indigo to Blue
    { id: 'grad6', colors: ['#fb923c', '#f472b6'] }, // Orange to Pink
    { id: 'grad7', colors: ['#a3e635', '#10b981'] }, // Lime to Emerald
    { id: 'grad8', colors: ['#22d3ee', '#38bdf8'] }, // Cyan to Sky
    { id: 'grad9', colors: ['#e879f9', '#fb7185'] }, // Fuchsia to Rose
    { id: 'grad10', colors: ['#c084fc', '#818cf8'] }, // Violet to Indigo
    { id: 'grad11', colors: ['#f87171', '#facc15'] }, // Yellow to Red (Reversed)
    { id: 'grad12', colors: ['#60a5fa', '#a78bfa'] }, // Purple to Blue (Reversed)
    { id: 'grad13', colors: ['#34d399', '#14b8a6'] }, // Teal to Green (Reversed)
    { id: 'grad14', colors: ['#f472b6', '#f87171'] }, // Red to Pink (Reversed)
    { id: 'grad15', colors: ['#818cf8', '#60a5fa'] }, // Blue to Indigo (Reversed)
    { id: 'grad16', colors: ['#fb923c', '#f472b6'] }, // Pink to Orange (Reversed)
    { id: 'grad17', colors: ['#a3e635', '#10b981'] }, // Emerald to Lime (Reversed)
    { id: 'grad18', colors: ['#22d3ee', '#38bdf8'] }, // Sky to Cyan (Reversed)
    { id: 'grad19', colors: ['#e879f9', '#fb7185'] }, // Rose to Fuchsia (Reversed)
    { id: 'grad20', colors: ['#c084fc', '#818cf8'] }, // Indigo to Violet (Reversed)
  ];

  // Prepare the data for the chart, assigning gradients from the defined array
  const chartData = sectionsData.map((section, idx) => ({
    section: section.section,
    percentage: section.percentage,
    gradientId: gradients[idx % gradients.length].id, // Cycle through the gradients
  }));

  return (
    <Card className='border-0'>
      <CardHeader>
        <CardTitle>Score Percentage by Area</CardTitle>
        <CardDescription>Overview of Assessment Sections</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className='w-[90vw] h-[200px]'>
          <BarChart
            accessibilityLayer
            data={chartData}
            layout='vertical'
            margin={{
              left: 0,
            }}
          >
            {/* Define the gradients */}
            <defs>
              {gradients.map((grad) => (
                <linearGradient
                  key={grad.id}
                  id={grad.id}
                  x1='0%'
                  y1='0%'
                  x2='100%'
                  y2='100%'
                >
                  <stop offset='0%' stopColor={grad.colors[0]} />
                  <stop offset='100%' stopColor={grad.colors[1]} />
                </linearGradient>
              ))}
            </defs>
            <YAxis
              dataKey='section'
              type='category'
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => value}
            />
            <XAxis dataKey='percentage' type='number' />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Bar dataKey='percentage' layout='vertical' radius={5} barSize={25}>
              {chartData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={`url(#${entry.gradientId})`} // Reference the gradient ID
                />
              ))}
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
