'use client';

import { <PERSON>, <PERSON><PERSON><PERSON> } from 'recharts';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { CRITICALITY_LEVELS } from '../../../shared/types/Company';
import { z } from 'zod';
import { RouterOutput } from '../../../shared';
import { FormProvider, useForm } from 'react-hook-form';
import { endOfYear, format, startOfYear, subYears } from 'date-fns';
import { FormDatePicker } from '@/components/form/FormDatePicker';
import { FormSelect } from '@/components/form/FormSelect';
import { trpc } from '@/providers/Providers';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Download, Filter } from 'lucide-react';
import { use, useEffect, useState } from 'react';
import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { set } from 'lodash';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useDownload } from '@/hooks/useDownload';
import {
  VendorAcceptanceStatus,
  VulnerabilityClosureStatus,
  VulnerabilityResolveStatus,
} from '../../../shared/types/AssessmentSubmission';
import Assessment from '@/app/(dashboard)/assessment/[assessment]/page';
import AssessmentSubmissionHistorySheet from '@/sheets/assessment.history.sheet/assessment.submission.history.sheet';

const chartConfig = {
  [CRITICALITY_LEVELS.CRITICAL]: {
    label: 'Critical',
    color: '#b3091d',
  },
  [CRITICALITY_LEVELS.HIGH]: {
    label: 'High',
    color: 'hsl(var(--chart-1))',
  },
  [CRITICALITY_LEVELS.MEDIUM]: {
    label: 'Medium',
    color: '#ffff00',
  },
  [CRITICALITY_LEVELS.LOW]: {
    label: 'Low',
    color: 'hsl(var(--chart-3))',
  },
  [CRITICALITY_LEVELS.VERYLOW]: {
    label: 'Very Low',
    color: 'hsl(var(--chart-4))',
  },
} as ChartConfig;

export const vulnerabilityReportChartsSchema = z.object({
  vendor: z.string(),
  criticalityLevels: z.array(z.nativeEnum(CRITICALITY_LEVELS)),
  from: z.date(),
  to: z.date(),
});

type FilterForm = z.infer<typeof vulnerabilityReportChartsSchema>;

export function VulnerabilityReportChart({
  vendors,
}: {
  vendors?: RouterOutput['company']['getCompanies'];
}) {
  const [criticality, setCriticality] = useState('');
  const [open, setOpen] = useState(false);
  const download = useDownload();
  const allCriticalities = Object.values(CRITICALITY_LEVELS);
  const filterMethods = useForm<FilterForm>({
    defaultValues: {
      from: startOfYear(new Date()),
      to: endOfYear(new Date()),
      criticalityLevels: allCriticalities,
      vendor: '',
    },
  });

  useEffect(() => {
    // @ts-ignore
    if (vendors?.length) filterMethods.setValue('vendor', vendors.at(3)?._id);
  }, [vendors, filterMethods]);
  const vendorFilter = filterMethods.watch('vendor');
  const fromFilter = filterMethods.watch('from');
  const toFilter = filterMethods.watch('to');
  const criticalityFiler = filterMethods.watch('criticalityLevels');

  const { data: chartData } = trpc.analytics.vulnerabilityChart.useQuery(
    {
      criticalityLevels: criticalityFiler,
      from: fromFilter,
      to: toFilter,
      vendor: vendorFilter,
    },
    {
      enabled: !!vendorFilter?.length,
    }
  );

  const { data: vulnerabilityDrilldown } =
    trpc.assessment.getAssessmentSubmissions.useQuery(
      {
        vendor: vendorFilter,
      },
      {
        enabled: !!vendorFilter?.length,
      }
    );
  const filteredVulnerabilityDrilldown = (vulnerabilityDrilldown ?? []).filter(
    (data) => data?.vulnerability?.criticalityLevel === criticality
  );

  const filteredChartData = (chartData ?? []).filter((data) => data.value > 0);

  // @ts-ignore
  const handleClick = (data, index) => {
    setCriticality(data.criticality);
    setOpen(true);
  };
  return (
    <div className='space-y-5'>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className='w-full min-w-[1800px] text-2xl  bg-indigo-950'>
          <DialogHeader>
            <DialogTitle className=' text-center text-3xl font-bold   text-white'>
              Vendor Vulnerability Insights
            </DialogTitle>
          </DialogHeader>
          <ScrollArea>
            <Accordion type='single' collapsible>
              {filteredVulnerabilityDrilldown.map((data, idx) => (
                <AccordionItem
                  key={String(data._id)}
                  value={String(data._id)}
                  className='p-5 '
                >
                  <AccordionTrigger>
                    <div className='text-start text-white'>
                      <span className='mx-2 text-white'>{idx + 1}.</span>
                      <span>{data.submission.question?.input.label}</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className='bg-slate-300'>
                    <Table>
                      <TableBody className='bg-transparent'>
                        <TableRow>
                          <TableHead>Inventory</TableHead>
                          <TableCell className='text-lg'>
                            {data.submission.question?.input.label}
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableHead>Inventory Details</TableHead>
                          <TableCell className='text-lg'>
                            {data.submission.answer}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                    <Table>
                      <TableHeader className='bg-transparent'>
                        <TableRow className=''>
                          <TableHead className='text-black font-semibold  '>
                            Cvss Score
                          </TableHead>
                          <TableHead className='text-black font-semibold text-center'>
                            VendorAi Score
                          </TableHead>
                          <TableHead className='text-black font-semibold text-center'>
                            Criticality
                          </TableHead>
                          <TableHead className='text-black font-semibold text-center'>
                            MSA/SOW Remediation Timelines
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody className='bg-transparent'>
                        <TableRow>
                          <TableCell className='text-start text-lg'>
                            {data.vulnerability?.cvssScore}
                          </TableCell>
                          <TableCell className='text-center text-lg'>
                            {data.vulnerability?.score}
                          </TableCell>
                          <TableCell
                            className={cn(
                              'font-bold text-center text-lg',
                              // @ts-ignore
                              data?.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.HIGH && 'text-rose-600 w',
                              // @ts-ignore
                              data?.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.CRITICAL && 'text-red-700',
                              // @ts-ignore
                              data?.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.MEDIUM && 'text-orange-600',
                              // @ts-ignore
                              data?.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.LOW && 'text-yellow-600',
                              // @ts-ignore
                              data?.vulnerability?.criticalityLevel ===
                                CRITICALITY_LEVELS.VERYLOW && 'text-green-700'
                            )}
                          >
                            {data?.vulnerability?.criticalityLevel.toUpperCase()}
                          </TableCell>
                          <TableCell className='text-center text-lg'>
                            {data.vendor?.criticalityLevels?.map((c, idx) => (
                              <div key={idx}>
                                {data.vulnerability?.criticalityLevel ===
                                c.level
                                  ? `${c.timeDuration} Days`
                                  : ''}
                              </div>
                            ))}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                    <Table>
                      <TableRow>
                        <TableHead className='text-black font-semibold  '>
                          Vulnerability Created Date :
                        </TableHead>
                        <TableCell className='text-lg'>
                          {/* @ts-ignore */}
                          {data.vulnerability?.createdAt
                            ? format(
                                // @ts-ignore
                                data.vulnerability?.createdAt,
                                'MMM-d-yyyy hh:mm a'
                              )
                            : '-'}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableHead className='text-black font-semibold  '>
                          Vulnerability Description:
                        </TableHead>
                        <TableCell className='w-[1000px]'>
                          <div
                            dangerouslySetInnerHTML={{
                              __html: data.vulnerability?.remarks as string,
                            }}
                            className='text-lg'
                          />
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableHead className='text-black font-semibold  '>
                          Vulnerability Accepted/Rejected Date:
                        </TableHead>
                        <TableCell className='w-[1000px] text-lg'>
                          {data?.vendorAcceptanceOrRejectionDate
                            ? format(
                                data?.vendorAcceptanceOrRejectionDate,
                                'MMM-d-yyyy hh:mm a'
                              )
                            : '-'}
                        </TableCell>
                      </TableRow>

                      <TableRow>
                        <TableHead className='text-black font-semibold  '>
                          Vulnerability Accepted/Rejected by Vendor:
                        </TableHead>
                        <TableCell className='text-lg'>
                          {data.vendorAcceptanceStatus ===
                          VendorAcceptanceStatus.ACCEPT
                            ? 'Vendor acknowledged for remediation(s)'
                            : data.vendorAcceptanceStatus ===
                              VendorAcceptanceStatus.REJECT
                            ? ' Vendor identified false positive'
                            : null}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableHead className='text-black font-semibold  '>
                          Vulnerability Accepted/Rejected Description:
                        </TableHead>
                        <TableCell className='capitalize text-lg'>
                          {data?.vendorRejectReason}
                        </TableCell>
                      </TableRow>
                      {data.vulnerability?.resolveStatus ===
                      VulnerabilityResolveStatus.RESOLVED ? (
                        <>
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Vulnerability Resolved Date:
                            </TableHead>
                            <TableCell className='text-lg'>
                              {data?.vulnerability?.resolvedDate
                                ? format(
                                    data?.vulnerability?.resolvedDate,
                                    'MMM - d -yyyy hh:mm a'
                                  )
                                : null}
                            </TableCell>
                          </TableRow>
                        </>
                      ) : (
                        ''
                      )}
                      {data.vulnerability?.resolveStatus ===
                      VulnerabilityResolveStatus.EXCEPTION ? (
                        <>
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Vendor Exception Reason:
                            </TableHead>
                            <TableCell className='text-lg'>
                              {data.vulnerability?.resolveDescription}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Soc Exception Reason:
                            </TableHead>
                            <TableCell className='text-lg'>
                              {
                                data.vulnerability
                                  ?.vulnerabilityExceptionApprovalDescriptionBySoc
                              }
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Vendor Exception Evidence:
                            </TableHead>
                            <TableCell>
                              <Button>
                                <Download
                                  onClick={() =>
                                    download({
                                      fileId: data.vulnerability
                                        ?.vulnerabilityResolvedOrExceptionEvidence as string,
                                    })
                                  }
                                />
                              </Button>
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Soc Exception Evidence:
                            </TableHead>
                            <TableCell>
                              <Button>
                                <Download
                                  onClick={() =>
                                    download({
                                      fileId: data.vulnerability
                                        ?.vulnerabilityExceptionApprovalEvidenceBySoc as string,
                                    })
                                  }
                                />
                              </Button>
                            </TableCell>
                          </TableRow>
                        </>
                      ) : null}

                      {data.vulnerability
                        ?.vulnerabilityClosureDescriptionBySoc ? (
                        <>
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Vulnerability Closer Description By Soc:
                            </TableHead>
                            <TableCell className='text-lg'>
                              {
                                data.vulnerability
                                  ?.vulnerabilityClosureDescriptionBySoc
                              }
                            </TableCell>
                          </TableRow>

                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Vulnerability Closer Evidence By Soc:
                            </TableHead>
                            <TableCell className='text-lg'>
                              <Button>
                                <Download
                                  onClick={() =>
                                    download({
                                      fileId: data.vulnerability
                                        ?.vulnerabilityClosureEvidenceBySoc as string,
                                    })
                                  }
                                />
                              </Button>
                            </TableCell>
                          </TableRow>
                        </>
                      ) : null}
                      <TableRow>
                        <TableHead className='text-black font-semibold  '>
                          Vulnerability Closure status by Customer:
                        </TableHead>
                        <TableCell className='text-lg'>
                          {data.vulnerability
                            ?.vulnerabilityClosureCompanyApprovalStatus ===
                          VulnerabilityClosureStatus.CLOSED
                            ? 'Approved for closure'
                            : data.vulnerability
                                ?.vulnerabilityClosureCompanyApprovalStatus ===
                              VulnerabilityClosureStatus.OPEN
                            ? 'Required reassessment'
                            : 'Pending'}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableHead className='text-black font-semibold  '>
                          Vulnerability Closed Status:
                        </TableHead>
                        <TableCell
                          className={cn(
                            'font-bold text-lg   ',
                            // @ts-ignore
                            data?.vulnerability?.vulnerabilityClosureStatus ===
                              VulnerabilityClosureStatus.CLOSED &&
                              'text-green-700',
                            // @ts-ignore
                            data?.vulnerability?.vulnerabilityClosureStatus ===
                              VulnerabilityClosureStatus.OPEN && 'text-red-700'
                          )}
                        >
                          {data.vulnerability?.vulnerabilityClosureStatus
                            ? data.vulnerability?.vulnerabilityClosureStatus.toUpperCase()
                            : '-'}
                        </TableCell>
                      </TableRow>
                      <TableFooter>
                        <TableRow>
                          <TableCell colSpan={1}></TableCell>
                          <TableCell className='' colSpan={1}>
                            <AssessmentSubmissionHistorySheet
                              question={String(data.submission.question?._id)}
                            />
                          </TableCell>
                        </TableRow>
                      </TableFooter>
                    </Table>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </ScrollArea>
        </DialogContent>
      </Dialog>
      <Sheet>
        <SheetTrigger asChild>
          <Button>
            <Filter />
            Filter
          </Button>
        </SheetTrigger>
        <SheetContent side={'left'} className='min-w-[500px]'>
          <FormProvider {...filterMethods}>
            <div className='flex flex-col gap-4 items-start'>
              <FormDatePicker
                name='from'
                label='From'
                captionLayout='dropdown-buttons'
                fromYear={subYears(new Date(), 10).getFullYear()}
                toYear={new Date().getFullYear()}
              />
              <FormDatePicker
                name='to'
                label='To'
                captionLayout='dropdown-buttons'
                fromYear={subYears(new Date(), 10).getFullYear()}
                toYear={new Date().getFullYear()}
              />
            </div>
            <div className='flex flex-col gap-4 items-start pb-4'>
              <div className='w-[450px]'>
                <FormSelect
                  name='vendor'
                  label='Vendor'
                  placeholder='Select vendor'
                  options={(vendors ?? []).map((c) => ({
                    label: c.company,
                    value: String(c._id),
                  }))}
                />
              </div>
              <FormComboBoxPopover
                name='criticalityLevels'
                label='Category'
                options={Object.values(CRITICALITY_LEVELS).map((c) => ({
                  label: c,
                  value: c,
                }))}
              />
            </div>
          </FormProvider>
        </SheetContent>
      </Sheet>

      <Card className='flex flex-col h-[378px] w-[530px] rounded-2xl border shadow-2xl bg-gradient-to-r to-indigo-950 via-indigo-900 from-blue-700'>
        <CardHeader className='items-center pb-0'>
          <CardTitle className='text-white text-lg'>
            {vendors?.find((c) => String(c._id) === vendorFilter)?.company ??
              ''}
            &nbsp;Vulnerabilities.
          </CardTitle>
        </CardHeader>
        <CardContent className='flex-1 pb-0'>
          <ChartContainer
            config={chartConfig}
            className='mx-auto aspect-square h-[330px] w-[400px]'
          >
            <PieChart data={filteredChartData} className='flex flex-row'>
              <ChartTooltip
                content={
                  <ChartTooltipContent nameKey='criticality' hideLabel />
                }
              />
              <ChartLegend
                content={
                  <ChartLegendContent
                    nameKey='criticality'
                    className=' text-white mb-1'
                  />
                }
              />
              <Pie
                data={filteredChartData}
                dataKey='value'
                onClick={(e, index) => handleClick(e, index)}
              >
                {/* <LabelList
                  dataKey='criticality'
                  className='fill-background'
                  stroke='none'
                  fontSize={12}
                  formatter={(value: keyof typeof chartConfig) =>
                    chartConfig[value]?.label
                  }
                /> */}
              </Pie>
            </PieChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
}
