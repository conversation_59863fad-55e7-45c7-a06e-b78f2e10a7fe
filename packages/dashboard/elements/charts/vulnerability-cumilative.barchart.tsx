'use client';
import React from 'react';
import { Filter, TrendingUp } from 'lucide-react';
import { <PERSON>, BarChart, CartesianGrid, XA<PERSON><PERSON>, YAxis } from 'recharts';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { z } from 'zod';
import { CRITICALITY_LEVELS } from '../../../shared/types/Company';
import { RouterOutput } from '../../../shared';
import { FormProvider, useForm } from 'react-hook-form';
import { endOfYear, startOfYear, subYears } from 'date-fns';
import { trpc } from '@/providers/Providers';
import { FormDatePicker } from '@/components/form/FormDatePicker';
import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { <PERSON><PERSON> } from '@/components/ui/button';

const chartConfig = {
  [CRITICALITY_LEVELS.CRITICAL]: {
    label: 'Critical',
    color: '#FF0000',
  },
  [CRITICALITY_LEVELS.HIGH]: {
    label: 'High',
    color: '#FFA500',
  },
  [CRITICALITY_LEVELS.MEDIUM]: {
    label: 'Medium',
    color: '#ffff00',
  },
  [CRITICALITY_LEVELS.LOW]: {
    label: 'Low',
    color: '#00FF00',
  },
  [CRITICALITY_LEVELS.VERYLOW]: {
    label: 'Very Low',
    color: '#0000FF',
  },
} satisfies ChartConfig;

export const allVendorVulnerabilityCharts = z.object({
  vendors: z.array(z.string()),
  criticalityLevels: z.array(z.nativeEnum(CRITICALITY_LEVELS)),
  from: z.date(),
  to: z.date(),
});

export type Filter = z.infer<typeof allVendorVulnerabilityCharts>;

export default function VulnerabilityCumulativeBarChart({
  vendors,
}: {
  vendors?: RouterOutput['company']['getCompanies'];
}) {
  const allCriticalityLevels = Object.values(CRITICALITY_LEVELS);
  const allVendors = vendors?.map((v) => String(v._id));
  const filterMethods = useForm<Filter>({
    defaultValues: {
      criticalityLevels: allCriticalityLevels,
      from: startOfYear(new Date()),
      to: endOfYear(new Date()),
      vendors: allVendors,
    },
  });

  const vendorsFilter = filterMethods.watch('vendors');
  const fromFilter = filterMethods.watch('from');
  const toFilter = filterMethods.watch('to');
  const criticalityFilter = filterMethods.watch('criticalityLevels');

  const { data: chartData } = trpc.analytics.vulnerabilityVendorsChart.useQuery(
    {
      criticalityLevels: criticalityFilter,
      from: fromFilter,
      to: toFilter,
      vendors: vendorsFilter,
    },
    {
      enabled: !!vendorsFilter?.length,
    }
  );

  return (
    <div className='space-y-5'>
      <Sheet>
        <SheetTrigger asChild>
          <Button>
            <Filter />
            Filter
          </Button>
        </SheetTrigger>
        <SheetContent side={'left'} className='min-w-[500px]'>
          <FormProvider {...filterMethods}>
            <div className='flex flex-col gap-4 items-start'>
              <FormDatePicker
                name='from'
                label='From'
                captionLayout='dropdown-buttons'
                fromYear={subYears(new Date(), 10).getFullYear()}
                toYear={new Date().getFullYear()}
              />
              <FormDatePicker
                name='to'
                label='To'
                captionLayout='dropdown-buttons'
                fromYear={subYears(new Date(), 10).getFullYear()}
                toYear={new Date().getFullYear()}
              />
            </div>
            <div className='flex flex-col gap-4 items-start pb-4'>
              <FormComboBoxPopover
                name='vendors'
                label='Vendor'
                options={(vendors ?? []).map((c) => ({
                  label: c.company,
                  value: String(c._id),
                }))}
              />
              <FormComboBoxPopover
                name='criticalityLevels'
                label='Criticality level'
                options={Object.values(CRITICALITY_LEVELS).map((c) => ({
                  label: c,
                  value: c,
                }))}
              />
            </div>
          </FormProvider>
        </SheetContent>
      </Sheet>

      <Card className=' border  shadow-2xl rounded-2xl bg-gradient-to-r from-teal-950 to-teal-700  '>
        <CardHeader>
          <CardTitle className='text-white text-lg text-center'>
            Multi-Vendor Vulnerability Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={chartConfig}
            className='mx-auto aspect-square h-[167px] w-[700px]'
          >
            <BarChart data={chartData} height={10}>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey='vendor'
                tickLine={false}
                tickMargin={10}
                axisLine={false}
                className='text-sm font-bold'
              />
              <YAxis type='number' interval={'preserveEnd'} />
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent indicator='dashed' />}
              />
              <Bar
                dataKey={CRITICALITY_LEVELS.CRITICAL}
                fill={chartConfig[CRITICALITY_LEVELS.CRITICAL].color}
                radius={4}
                barSize={40}
              />
              <Bar
                dataKey={CRITICALITY_LEVELS.HIGH}
                fill={chartConfig[CRITICALITY_LEVELS.HIGH].color}
                radius={4}
                barSize={40}
              />
              <Bar
                dataKey={CRITICALITY_LEVELS.MEDIUM}
                fill={chartConfig[CRITICALITY_LEVELS.MEDIUM].color}
                radius={4}
                barSize={40}
              />
              <Bar
                dataKey={CRITICALITY_LEVELS.LOW}
                fill={chartConfig[CRITICALITY_LEVELS.LOW].color}
                radius={4}
                barSize={40}
              />
              <Bar
                dataKey={CRITICALITY_LEVELS.VERYLOW}
                fill={chartConfig[CRITICALITY_LEVELS.VERYLOW].color}
                radius={4}
                barSize={40}
              />
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
}
