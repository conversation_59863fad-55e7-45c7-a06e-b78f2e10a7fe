'use client';

import { <PERSON>, AreaChart, CartesianGrid, XAxis } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { CreditRatingCategory } from '../../../shared/types/CreditReport';
import { trpc } from '@/providers/Providers';
import { RouterOutput } from '../../../shared';
import { z } from 'zod';
import { FormProvider, useForm } from 'react-hook-form';
import {
  endOfYear,
  format,
  startOfQuarter,
  startOfYear,
  subQuarters,
  subYears,
} from 'date-fns';
import { FormDatePicker } from '@/components/form/FormDatePicker';
import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Download, Filter } from 'lucide-react';
import { useExcellDownload } from '@/hooks/useExcelDownload';
import { useEffect } from 'react';

const chartConfig = {
  [CreditRatingCategory.BUSINESS_CREDIT_RATING]: {
    label: 'Business',
    color: 'hsl(var(--chart-1))',
  },
  [CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING]: {
    label: 'Glassdoor',
    color: 'hsl(var(--chart-2))',
  },
  [CreditRatingCategory.SOCIALMEDIA_RATING]: {
    label: 'Social Media',
    color: 'hsl(var(--chart-3))',
  },
} satisfies ChartConfig;

export const creditReportsChartSchema = z.object({
  vendors: z.array(z.string()),
  from: z.date(),
  to: z.date(),
  category: z.array(z.nativeEnum(CreditRatingCategory)),
});

type Filter = z.infer<typeof creditReportsChartSchema>;

export function CreditReportChart({
  vendors,
}: {
  vendors?: RouterOutput['company']['getCompanies'];
}) {
  // const defaultVendors = (vendors ?? []).map((c) => String(c._id));

  const downloadExcel = useExcellDownload();

  function getStartOfPreviousFourQuarters() {
    const currentDate = new Date();
    const startOfCurrentQuarter = startOfQuarter(currentDate);
    const startOfPreviousFourQuarters = subQuarters(startOfCurrentQuarter, 4);
    return startOfPreviousFourQuarters;
  }

  const filterMethods = useForm<Filter>({
    defaultValues: {
      from: getStartOfPreviousFourQuarters(),
      to: endOfYear(new Date()),
      vendors: [],
      category: [
        CreditRatingCategory.BUSINESS_CREDIT_RATING,
        CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING,
        CreditRatingCategory.SOCIALMEDIA_RATING,
      ],
    },
  });

  useEffect(() => {
    if (vendors?.length)
      filterMethods.setValue(
        'vendors',
        vendors.map((c) => String(c._id))
      );
  }, [vendors, filterMethods]);

  const vendorsFilter = filterMethods.watch('vendors');
  const fromFilter = filterMethods.watch('from');
  const toFilter = filterMethods.watch('to');
  const categoryFilter = filterMethods.watch('category');

  const { data: chartData } = trpc.analytics.creditReportsChart.useQuery(
    {
      from: fromFilter,
      to: toFilter,
      vendors: vendorsFilter,
      category: categoryFilter,
    },
    {
      enabled: !!vendorsFilter?.length,
    }
  );

  const { data: allVendorsReports } =
    trpc.creditReport.getAllCreditReports.useQuery({
      vendor: vendorsFilter,
      fromDate: fromFilter,
      toDate: toFilter,
      category: categoryFilter,
    });

  // Transform the data for each chart
  const businessData = chartData?.map((data) => ({
    month: data.month,
    value: data[CreditRatingCategory.BUSINESS_CREDIT_RATING],
  }));

  const glassdoorData = chartData?.map((data) => ({
    month: data.month,
    value: data[CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING],
  }));

  const socialMediaData = chartData?.map((data) => ({
    month: data.month,
    value: data[CreditRatingCategory.SOCIALMEDIA_RATING],
  }));

  const exportCreditReports = () => {
    const data = (allVendorsReports ?? []).map((report) => ({
      Report_Date: format(report.date, 'MMM d, yyyy - h:mm a'),
      Vendor: report.vendor?.company,
      Report_Category: report.category,
      Source: report.subCategory,
      Quarter: report.quarter,
      Rating: report.rating,
      Comments: report.remarks,
    }));
    downloadExcel(data, `Credit Reports_${new Date()}.xlsx`);
  };
  return (
    <div className='space-y-5'>
      <Sheet>
        <SheetTrigger asChild>
          <Button>
            <Filter />
            Filter
          </Button>
        </SheetTrigger>
        <SheetContent side={'left'} className='flex flex-col justify-between'>
          <FormProvider {...filterMethods}>
            <div className='flex flex-col gap-4 items-start'>
              <FormDatePicker
                name='from'
                label='From'
                captionLayout='dropdown-buttons'
                fromYear={subYears(new Date(), 10).getFullYear()}
                toYear={new Date().getFullYear()}
              />
              <FormDatePicker
                name='to'
                label='To'
                captionLayout='dropdown-buttons'
                fromYear={subYears(new Date(), 10).getFullYear()}
                toYear={new Date().getFullYear()}
              />

              <div className='flex gap-4 items-center pb-4 w-[300px]'>
                <FormComboBoxPopover
                  name='vendors'
                  label='Vendor'
                  options={(vendors ?? []).map((c) => ({
                    label: c.company,
                    value: String(c._id),
                  }))}
                />
              </div>
            </div>
          </FormProvider>
          <div className='sticky bottom-0 flex justify-center'>
            <Button onClick={exportCreditReports}>
              <Download size={24} />
              Export BI Reports
            </Button>
          </div>
        </SheetContent>
      </Sheet>
      <div className='flex flex-row gap-2 justify-between '>
        <Card className=' border shadow-2xl rounded-2xl bg-gradient-to-r from-purple-900 to-purple-950 '>
          <CardHeader>
            <CardTitle className='text-white text-base text-center'>
              Comprehensive Business Credit Score
            </CardTitle>
          </CardHeader>
          <CardContent className=''>
            <ChartContainer config={chartConfig} className=''>
              <AreaChart
                accessibilityLayer
                data={businessData ?? []}
                margin={{
                  left: 12,
                  right: 12,
                  bottom: 10,
                }}
              >
                <defs>
                  <linearGradient
                    id='businessGradient'
                    x1='0'
                    y1='0'
                    x2='0'
                    y2='1'
                  >
                    <stop offset='0%' stopColor='#fc036b' stopOpacity={0.8} />
                    <stop offset='100%' stopColor='#fc036b' stopOpacity={0.2} />
                  </linearGradient>
                </defs>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey='month'
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tickFormatter={(value) => value}
                  className='font-bold text-sm '
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent indicator='line' />}
                />
                <Area
                  dataKey='value'
                  type='basis'
                  fill='#ad4bfa'
                  fillOpacity={0.9}
                  stroke='#ead2fc'
                  stackId='a'
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
        </Card>
        <Card className=' border shadow-2xl rounded-2xl  bg-gradient-to-r from-fuchsia-800 to-fuchsia-950  '>
          <CardHeader>
            <CardTitle className='text-white text-base text-center'>
              Overall Glassdoor Rating
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className='w-[300px]'>
              <AreaChart
                accessibilityLayer
                data={glassdoorData ?? []}
                margin={{
                  left: 12,
                  right: 12,
                  bottom: 10,
                }}
              >
                <defs>
                  <linearGradient
                    id='glassdoorGradient'
                    x1='0'
                    y1='0'
                    x2='0'
                    y2='1'
                  >
                    <stop offset='0%' stopColor='#05e8fc' stopOpacity={0.8} />
                    <stop offset='100%' stopColor='#05e8fc' stopOpacity={0.2} />
                  </linearGradient>
                </defs>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey='month'
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tickFormatter={(value) => value}
                  className='font-bold text-sm '
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent indicator='line' />}
                />
                <Area
                  dataKey='value'
                  type='basis'
                  fill='#db00be'
                  fillOpacity={0.9}
                  stroke='#fcb8f3'
                  stackId='b'
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
        </Card>
        <Card className=' border shadow-2xl rounded-2xl bg-gradient-to-r from-teal-700 to-teal-900  '>
          <CardHeader>
            <CardTitle className='text-white text-base text-center'>
              Social Media Risk Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className='w-[300px]'>
              <AreaChart
                accessibilityLayer
                data={socialMediaData ?? []}
                margin={{
                  left: 12,
                  right: 12,
                  bottom: 10,
                }}
              >
                {/* <defs>
                  <linearGradient
                    id='socialMediaGradient'
                    x1='0'
                    y1='0'
                    x2='0'
                    y2='1'
                  >
                    <stop offset='0%' stopColor='#0bd35d' stopOpacity={0.8} />
                    <stop offset='100%' stopColor='#0bd35d' stopOpacity={0.2} />
                  </linearGradient>
                </defs> */}
                <defs>
                  <linearGradient
                    id='socialMediaGradient'
                    x1='0'
                    y1='0'
                    x2='0'
                    y2='1'
                  >
                    <stop offset='0%' stopColor='#61d1b8' stopOpacity={0.8} />
                    <stop offset='100%' stopColor='#61d1b8' stopOpacity={0.2} />
                  </linearGradient>
                </defs>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey='month'
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tickFormatter={(value) => value}
                  className='font-bold text-sm '
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent indicator='line' />}
                />
                <Area
                  dataKey='value'
                  type='basis'
                  fill='#0fdaa5'
                  fillOpacity={0.9}
                  stroke='#0a7d7a'
                  stackId='c'
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
