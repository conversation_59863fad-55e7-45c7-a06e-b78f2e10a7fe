'use client';

import { useEffect, useState } from 'react';
import { Filter, TrendingUp } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axis, YAxi<PERSON>, Cell } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { trpc } from '@/providers/Providers';
import { z } from 'zod';
import { RouterOutput } from '../../../shared';
import { FormProvider, useForm } from 'react-hook-form';
import { endOfYear, startOfYear, subYears } from 'date-fns';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { FormDatePicker } from '@/components/form/FormDatePicker';
import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';

const chartConfig = {
  totalVulnerabilitiesRaised: {
    label: 'Total Vulnerabilities',
    color: '#0080ff',
  },
  nonAcknowledgement: {
    label: 'Pending-Vendor Acknowledgement',
    color: '#FF0000',
  },
  accept: {
    label: 'Vendor Acknowledged',
    color: '#00FF00',
  },
  reject: {
    label: 'False Positive',
    color: '#FF0000',
  },
  resolved: {
    label: 'Resolved',
    color: '#00FF00',
  },
  exception: {
    label: 'Exception/ Escaltion',
    color: '#FFA500',
  },
  exceptionApproval: {
    label: 'Exception Approval',
    color: '#ffff00',
  },
  exceptionRejected: {
    label: 'Exception Rejected',
    color: '#FF0000',
  },
  vulnerabilityClosed: {
    label: 'Vulnerability Closed',
    color: '#00FF00',
  },
} satisfies ChartConfig;

export const allVendorVsocProcessSchema = z.object({
  vendors: z.array(z.string()),
  from: z.date(),
  to: z.date(),
});

export type Form = z.infer<typeof allVendorVsocProcessSchema>;

export default function AllVendorsVsocProcessCharts({
  vendors,
}: {
  vendors?: RouterOutput['company']['getCompanies'];
}) {
  const filterMethods = useForm<Form>({
    defaultValues: {
      from: startOfYear(new Date()),
      to: endOfYear(new Date()),
      vendors: (vendors ?? []).map((v) => String(v._id)),
    },
  });

  const vendorsFilter = filterMethods.watch('vendors');
  const fromFilter = filterMethods.watch('from');
  const toFilter = filterMethods.watch('to');

  const { data: chartData, isLoading: isChartDataLoading } =
    trpc.analytics.allVsocProcessChartByvendors.useQuery(
      {
        from: fromFilter,
        to: toFilter,
        vendors: vendorsFilter,
      },
      {
        enabled: !!vendorsFilter?.length,
      }
    );
  console.log({ chartData });
  return (
    <div className='space-y-5'>
      <Sheet>
        <SheetTrigger asChild>
          <Button>
            <Filter />
            Filter
          </Button>
        </SheetTrigger>
        <SheetContent side={'left'} className='min-w-[500px]'>
          <FormProvider {...filterMethods}>
            <div className='flex flex-col gap-4 items-start'>
              <FormDatePicker
                name='from'
                label='From'
                captionLayout='dropdown-buttons'
                fromYear={subYears(new Date(), 10).getFullYear()}
                toYear={new Date().getFullYear()}
              />
              <FormDatePicker
                name='to'
                label='To'
                captionLayout='dropdown-buttons'
                fromYear={subYears(new Date(), 10).getFullYear()}
                toYear={new Date().getFullYear()}
              />
            </div>
            <div className='flex flex-col gap-4 items-start pb-4'>
              <FormComboBoxPopover
                name='vendors'
                label='Vendor'
                options={(vendors ?? []).map((c) => ({
                  label: c.company,
                  value: String(c._id),
                }))}
              />
            </div>
          </FormProvider>
        </SheetContent>
      </Sheet>
      <Card className='border rounded-2xl bg-gradient-to-r from-gray-800 to-gray-500'>
        <CardHeader>
          <CardTitle className='text-white text-lg text-center'>
            Aggregate VSOC Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={chartConfig}
            className='mx-auto aspect-square h-[276px] w-[700px]   '
          >
            {!isChartDataLoading ? (
              <BarChart
                accessibilityLayer
                data={chartData}
                layout='vertical'
                margin={{
                  left: 78,
                }}
              >
                <YAxis
                  dataKey='status'
                  type='category'
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  tickFormatter={(value) =>
                    chartConfig[value as keyof typeof chartConfig]?.label
                  }
                  className={cn(`font-semibold `)}
                />
                <XAxis dataKey='value' type='number' />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent hideLabel />}
                />
                <Bar dataKey='value' layout='vertical' radius={5}>
                  {chartData?.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={
                        chartConfig[entry.status as keyof typeof chartConfig]
                          ?.color
                      }
                    />
                  ))}
                </Bar>
              </BarChart>
            ) : (
              <></>
            )}
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
}
