'use client';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { trpc } from '@/providers/Providers';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { StringParam, useQueryParam, withDefault } from 'use-query-params';
import CompanyAnalaytics from '../analytics/company-analytics';
import { CompanyType } from '../../../shared/types/Company';
import { Loader } from 'lucide-react';

export default function CompanyDashboard() {
  const router = useRouter();
  const { data: session } = useSession();
  const [tab, setTab] = useQueryParam(
    'tab',
    withDefault(StringParam, 'analytics')
  );

  const vendors = trpc.company.getCompanies.useQuery(
    {
      reportsTo: session?.user.companies?.at(0),
    },
    { enabled: !!session?.user.companies?.length }
  );

  const handleVendorDetails = (vendorId: string) => {
    router.push(`/vendor-details/${vendorId}`);
  };

  // isloading

  if (vendors.isLoading)
    return (
      <div className='container flex flex-1 justify-center items-center text-center text-2xl'>
        <Loader className='animate-spin text-cyan-400' size={60} /> Loading....
      </div>
    );
  return (
    <div className='ph '>
      <Tabs
        defaultValue='analytics'
        value={tab}
        onValueChange={setTab}
        className='bg-indigo-950 ph'
      >
        <TabsList className='bg-cyan-500'>
          <TabsTrigger value='analytics'>Analytics</TabsTrigger>
          <TabsTrigger value='vendors'>Vendors</TabsTrigger>
        </TabsList>
        <TabsContent value='analytics'>
          <CompanyAnalaytics />
        </TabsContent>
        <TabsContent value='vendors'>
          {/* Create a datagrid component */}
          <Table className=''>
            <TableHeader>
              <TableRow className='font-semibold border-none   '>
                <TableHead className='capitalize  bg-slate-300 '>No</TableHead>
                <TableHead className='capitalize  bg-slate-300 '>
                  Vendor
                </TableHead>
                <TableHead className='capitalize  bg-slate-300 '>
                  Company
                </TableHead>
                <TableHead className='capitalize  bg-slate-300 '>
                  Vendor Sector
                </TableHead>

                <TableHead className='capitalize  bg-slate-300 '>
                  Priority
                </TableHead>

                <TableHead className='capitalize  bg-slate-300 '>
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className='border-2'>
              {vendors.data?.map((p, idx) => {
                if (p.type !== CompanyType.VENDOR) return null;

                return (
                  <TableRow key={String(p._id)}>
                    <TableCell className=''>{idx + 1}</TableCell>
                    <TableCell className='border-2'>
                      {p.company.toUpperCase()}
                    </TableCell>
                    <TableCell className='text-primary '>
                      {p.reportsTo?.company.toUpperCase()}
                    </TableCell>
                    <TableCell className='border-2'>
                      {p.sector.toUpperCase()}
                    </TableCell>

                    <TableCell className='border-2'>
                      {p.priorityLevel}
                    </TableCell>

                    <TableCell className='border-2'>
                      {/* @ts-ignore */}
                      <Button
                        onClick={() => handleVendorDetails(String(p._id))}
                      >
                        {p.company}
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TabsContent>
      </Tabs>
    </div>
  );
}
