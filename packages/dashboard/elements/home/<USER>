import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { trpc } from '@/providers/Providers';
import { ASSIGNMENT_STATUS } from '../../../shared/types/Assesment';
import React from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function AuditorDashboard() {
  const { data: session } = useSession();
  const router = useRouter();
  const onAssessmentSelect = (assessmentId: string) =>
    router.push(`/audit/${String(assessmentId)}`);

  const assessments = trpc.assessment.getAssessments.useQuery(
    {
      vendor: session?.user.companies,
    },
    { enabled: !!session?.user.companies?.length }
  );

  return (
    <ScrollArea className='h-[calc(100vh-120px)] container'>
      <Table className='border'>
        <TableHeader className='border-b'>
          <TableHead>No</TableHead>
          <TableHead>Assessment</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Action</TableHead>
        </TableHeader>
        <TableBody>
          {assessments.data?.map(
            ({ _id, assignments, company, sections, vendor, name }, idx) => {
              const assignmentCompleted = assignments.every(
                (assignment) =>
                  assignment.assignmentStatus === ASSIGNMENT_STATUS.COMPLETED
              );
              return (
                <TableRow key={idx}>
                  <TableCell>{idx + 1}</TableCell>
                  <TableCell>{name}</TableCell>
                  <TableCell>
                    {assignmentCompleted ? 'COMPLETE' : 'PENDING BY VENDOR'}
                  </TableCell>
                  <TableCell>
                    <Button
                      onClick={() => onAssessmentSelect(String(_id))}
                      disabled={!assignmentCompleted}
                    >
                      Action
                    </Button>
                  </TableCell>
                </TableRow>
              );
            }
          )}
        </TableBody>
      </Table>
    </ScrollArea>
  );
}
