import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { trpc } from '@/providers/Providers';
import { ASSIGNMENT_STATUS } from '../../../shared/types/Assesment';
import React from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';

export default function SockAnalystDashboard() {
  const { data: session } = useSession();
  const router = useRouter();
  const onAssessmentSelect = (assessmentId: string) =>
    router.push(`/assessment/${String(assessmentId)}`);

  const assessments = trpc.assessment.getAssessments.useQuery(
    {
      vendor: session?.user.companies,
    },
    { enabled: !!session?.user.companies?.length }
  );

  return (
    <ScrollArea className='h-[calc(100vh-120px)] container1'>
      <Table className='border'>
        <TableHeader className='border-b'>
          <TableHead>No</TableHead>
          <TableHead>Company</TableHead>
          <TableHead>Vendor</TableHead>
          <TableHead>Assessment</TableHead>
          <TableHead> Start Date </TableHead>
          <TableHead> End Date</TableHead>
          <TableHead> Completed Date</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Action</TableHead>
        </TableHeader>
        <TableBody>
          {assessments.data?.map(
            (
              {
                _id,
                assignments,
                company,
                sections,
                vendor,
                name,
                expiresAt,
                // @ts-ignore
                createdAt,
              },
              idx
            ) => {
              const assignmentCompleted = assignments.every(
                (assignment) =>
                  assignment.assignmentStatus === ASSIGNMENT_STATUS.COMPLETED
              );

              return (
                <TableRow key={idx}>
                  <TableCell>{idx + 1}</TableCell>
                  <TableCell>{company?.company}</TableCell>
                  <TableCell>{vendor?.company}</TableCell>
                  <TableCell>{name}</TableCell>
                  <TableCell>{format(createdAt, 'MMM d,yyy')}</TableCell>
                  <TableCell>{format(expiresAt, 'MMM d,yyy')}</TableCell>
                  {/* {assignments.length !== 0
                    ? assignments.map((ass) => (
                        <TableCell key={ass._id}>
                          {ass?.assignmentCompletedDate
                            ? format(ass?.assignmentCompletedDate, 'MMM d,yyy')
                            : '-'}
                        </TableCell>
                      ))
                    : ''} */}

                  <TableCell>
                    {assignments.length !== 0
                      ? assignments.map((ass) => (
                          <div key={ass._id}>
                            {ass?.assignmentCompletedDate
                              ? format(
                                  ass?.assignmentCompletedDate,
                                  'MMM d,yyy'
                                )
                              : '-'}
                          </div>
                        ))
                      : '-'}
                  </TableCell>
                  <TableCell>
                    {assignmentCompleted ? 'COMPLETED' : 'PENDING BY VENDOR'}
                  </TableCell>
                  <TableCell>
                    <Button
                      onClick={() => onAssessmentSelect(String(_id))}
                      disabled={!assignmentCompleted}
                    >
                      Audit
                    </Button>
                  </TableCell>
                </TableRow>
              );
            }
          )}
        </TableBody>
      </Table>
    </ScrollArea>
  );
}
