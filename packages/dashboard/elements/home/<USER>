'use client';
import { trpc } from '@/providers/Providers';
import { useSession } from 'next-auth/react';
import React, { useMemo, useState } from 'react';
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { TableCell } from '../../components/ui/table';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { ASSIGNMENT_STATUS } from '../../../shared/types/Assesment';
import { USER_ROLE } from '../../../shared/types/User';
import { differenceInDays, format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { toastPromise } from '@/lib/utils';
import { uploadFile } from '@/components/form/FormField';
import { ComboboxPopover } from '@/components/ui/form-combo-popover';
import { COMPLIENCES } from '../../../shared/types/Question';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useComplianceForm } from '@/hooks/forms/compliance';
import ComplianceForm from '../compliance/compliance.form';

export default function EmployeeDashboard() {
  const { data: session } = useSession();

  const [upload, setUpload] = useState(false);

  const companies = session?.user.companies;
  const vendors = trpc.company.getCompanies.useQuery(
    { companies: companies },
    { enabled: !!companies?.length }
  );

  const isVendor = useMemo(
    () => session?.user.role === USER_ROLE.VENDOR,
    [session?.user.role]
  );

  const assessments = trpc.assessment.getAssessments.useQuery(
    { ...(!isVendor && { employee: session?.user._id }) },
    { enabled: !!session?.user._id }
  );

  const router = useRouter();
  const onAssessmentSelect = (assessmentId: string) => {
    router.push(`/assessment/${String(assessmentId)}`);
  };
  const onReportClick = (assessmentId: string) =>
    router.push(`/reports/${String(assessmentId)}`);

  const { methods, onSubmit, success, successCompleted } = useComplianceForm();

  return (
    <div className='container1'>
      {/* Assessment Table */}

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>S.No</TableHead>
            <TableHead>Company</TableHead>
            <TableHead>Vendor</TableHead>
            <TableHead>Assessment Name</TableHead>
            <TableHead> Start Date</TableHead>
            <TableHead> End Date</TableHead>
            <TableHead> Sections</TableHead>
            <TableHead> Status</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {(assessments.data || []).map((assessment, idx) => {
            const userAssignments = assessment.assignments?.filter(
              (assignment) =>
                String(assignment.employee._id) === session?.user._id
            );

            const assessmentCompleted = assessment.assignments
              ?.filter(
                (assignment) =>
                  String(assignment.employee._id) === session?.user._id
              )
              .every(
                ({ assignmentStatus }) =>
                  assignmentStatus === ASSIGNMENT_STATUS.COMPLETED
              );
            const remaingDays = assessment.expiresAt
              ? differenceInDays(new Date(assessment.expiresAt), new Date())
              : null;
            const remaingDaysColor =
              remaingDays !== null
                ? remaingDays <= 0
                  ? 'text-red-600'
                  : 'text-green-600'
                : 'black';

            return (
              <TableRow key={String(assessment._id)}>
                <TableCell>{idx + 1}</TableCell>
                <TableCell>{assessment.company?.company}</TableCell>
                <TableCell>{assessment.vendor?.company}</TableCell>
                <TableCell>{assessment.name}</TableCell>
                <TableCell>
                  {/* @ts-ignore */}
                  {format(assessment.createdAt, 'MMM d,yyy')}
                </TableCell>
                <TableCell
                  className={`w-40 py-[10px] tracking-tighter ${remaingDaysColor}`}
                >
                  {format(assessment.expiresAt, 'MMM d,yyy')}
                </TableCell>
                <TableCell>
                  {userAssignments?.map((assignment) => {
                    return assignment.sections.map((section, idx) => (
                      <div key={idx}>{section.section}</div>
                    ));
                  })}
                </TableCell>
                <TableCell>
                  {userAssignments?.map((assignment) => {
                    return assignment.assignmentStatus.toUpperCase();
                  })}
                </TableCell>

                <TableCell>
                  {!assessmentCompleted ? (
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button>Start</Button>
                      </DialogTrigger>
                      <DialogContent className='min-w-[70%] rounded-xl bg-cyan-100'>
                        <DialogHeader>
                          <DialogTitle className='self-center text-2xl underline'>
                            Compliances
                          </DialogTitle>
                        </DialogHeader>
                        <div>
                          <p className='text-base font-semibold'>
                            Note: If you have any compliances certificates,
                            Please select and submit relevant compliance
                            certificates from the options provided below
                          </p>

                          {successCompleted && (
                            <div className='my-5 space-x-2'>
                              <Checkbox
                                onClick={() => setUpload(!upload)}
                                id='upload'
                                className='size-4'
                                checked={upload}
                              />
                              <Label>
                                Please check this box, if you have any
                                compliances
                              </Label>
                              {upload ? (
                                <div className='flex  gap-10'>
                                  {assessmentCompleted
                                    ? ''
                                    : userAssignments.map(
                                        (assessmentId, assignmentIndex) => {
                                          return (
                                            <div
                                              key={assignmentIndex}
                                              className='flex-1'
                                            >
                                              <ComplianceForm
                                                methods={methods}
                                                assessmentId={String(
                                                  assessment._id
                                                )}
                                                assignmentIndex={
                                                  assignmentIndex
                                                }
                                                onSubmit={onSubmit}
                                              />
                                            </div>
                                          );
                                        }
                                      )}
                                </div>
                              ) : null}{' '}
                            </div>
                          )}

                          <div className='flex justify-center items-center gap-2'>
                            {!upload ? (
                              <Button
                                onClick={() =>
                                  onAssessmentSelect(String(assessment._id))
                                }
                              >
                                Take Assessment
                              </Button>
                            ) : null}
                            {success ? (
                              <Button
                                onClick={() =>
                                  onAssessmentSelect(String(assessment._id))
                                }
                              >
                                Take Assessment
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  ) : (
                    <Button
                      onClick={() => onReportClick(String(assessment._id))}
                    >
                      Report
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
