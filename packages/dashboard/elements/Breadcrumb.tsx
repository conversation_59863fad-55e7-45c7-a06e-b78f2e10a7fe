'use client';
import {
  Breadcrumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';

export function BreadCrumb() {
  const { data: session } = useSession();
  const path = usePathname();

  const breadCrumbs = path.split('/');

  if (!session) return null;

  return (
    <div className='container mb-2'>
      <Breadcrumb>
        <BreadcrumbList>
          {breadCrumbs.map((bc, idx) => {
            let path = '';
            for (const item of breadCrumbs) {
              path += `${item}/`;
              if (bc === item) break;
            }

            return (
              <React.Fragment key={bc}>
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={path} className='capitalize'>
                      {bc}
                    </Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                {!!idx && idx !== breadCrumbs.length - 1 && (
                  <BreadcrumbSeparator />
                )}
              </React.Fragment>
            );
          })}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
