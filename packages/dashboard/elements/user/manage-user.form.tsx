'use client';
import <PERSON><PERSON>ield from '@/components/form/FormField';
import { FormSelect } from '@/components/form/FormSelect';
import { Button } from '@/components/ui/button';

import { selectOptionsParser, toastPromise } from '@/lib/utils';
import { trpc } from '@/providers/Providers';

import { USER_ROLE } from '../../../shared/types/User';
import { manageUserValidator } from '../../../shared/validators/user.validator';
import { FormProvider, UseFormReturn, useForm } from 'react-hook-form';
import { boolean, z } from 'zod';
import { FormComboBoxPopover } from '@/components/form/FormComboPopover';
import { CompanyType } from '../../../shared/types/Company';

const roleCompanyRelation: Record<USER_ROLE, CompanyType[]> = {
  company: [CompanyType.COMPANY],
  'company-admin': [CompanyType.COMPANY],
  employee: [CompanyType.VENDOR],
  'lead-soc-analyst': [CompanyType.VENDOR],
  'soc-analyst': [CompanyType.VENDOR],
  superadmin: [CompanyType.COMPANY, CompanyType.VENDOR],
  vendor: [CompanyType.VENDOR],
  auditor: [CompanyType.VENDOR],
};

type CreateForm = z.infer<typeof manageUserValidator>;

export type Props = {
  methods: UseFormReturn<CreateForm>;
  isUpdate?: boolean;
};

export default function ManageUserForm({ methods, isUpdate }: Props) {
  const selectedRole = methods.watch('role');

  const companies = trpc.company.getCompanies.useQuery(
    { type: roleCompanyRelation[selectedRole] },
    { enabled: !!selectedRole }
  );

  const createUser = trpc.user.createUser.useMutation();

  const utils = trpc.useUtils();

  const handleSubmit = methods.handleSubmit((data) =>
    toastPromise({
      asyncFunc: createUser.mutateAsync(data),
      success: 'User created successfully',
      onSuccess: () => {
        methods.reset();
        utils.user.invalidate();
      },
    })
  );

  return (
    <div className=''>
      <form
        onSubmit={handleSubmit}
        className='space-y-4 p-5  bg-white border rounded-2xl'
      >
        <label>{isUpdate ? 'Update User' : 'Create User'}</label>
        {/* @ts-ignore */}
        <FormProvider {...methods}>
          <FormField
            type='email'
            name='email'
            label='Email'
            placeholder='Enter Email'
          />
          <FormField
            name='password'
            label='Password'
            placeholder='Enter Password'
            type='password'
          />
          <FormSelect
            name='role'
            label='User Role'
            placeholder='Select User Role'
            options={[
              {
                label: USER_ROLE.SUPERADMIN.toUpperCase(),
                value: USER_ROLE.SUPERADMIN,
              },
              {
                label: USER_ROLE.COMPANY.toUpperCase(),
                value: USER_ROLE.COMPANY,
              },
              {
                label: USER_ROLE.COMPANY_ADMIN.toUpperCase(),
                value: USER_ROLE.COMPANY_ADMIN,
              },
              {
                label: USER_ROLE.VENDOR.toUpperCase(),
                value: USER_ROLE.VENDOR,
              },
              {
                label: USER_ROLE.LEAD_SOC_ANALYST.toUpperCase(),
                value: USER_ROLE.LEAD_SOC_ANALYST,
              },
              {
                label: USER_ROLE.SOC_ANALYST.toUpperCase(),
                value: USER_ROLE.SOC_ANALYST,
              },
              {
                label: 'VENDOR-RESPONDENT',
                value: USER_ROLE.EMPLOYEE,
              },
              {
                label: USER_ROLE.AUDITOR.toUpperCase(),
                value: USER_ROLE.AUDITOR,
              },
            ]}
          />
          <div className='flex flex-col gap-2'>
            <label htmlFor='' className='font-semibold'>
              Contact
            </label>
            <div className='grid grid-cols-2 gap-2 border-2 p-3'>
              <FormField
                name='contact.firstName'
                label='First name'
                placeholder='Enter first name'
              />
              <FormField
                name='contact.lastName'
                label='Last name'
                placeholder='Enter last name'
              />
              <FormField
                name='contact.title'
                label='Title'
                placeholder='Enter your job title'
              />
              <FormField
                name='contact.countryCode'
                label='Country Code'
                placeholder='Enter country code'
                type='number'
              />
              <FormField
                name='contact.phone'
                label='Contact'
                placeholder='Add contact number '
                type='number'
              />
            </div>
          </div>
          <FormComboBoxPopover
            label='Companies'
            name='companies'
            // @ts-ignore
            options={selectOptionsParser(companies.data, {
              labelKey: 'company',
              valKey: '_id',
            })}
          />
        </FormProvider>
      </form>

      <Button
        className=' float-right mt-2 min-w-[100px] bg-cyan-600 hover:bg-cyan-500 w-30'
        onClick={handleSubmit}
      >
        Save
      </Button>
    </div>
  );
}
