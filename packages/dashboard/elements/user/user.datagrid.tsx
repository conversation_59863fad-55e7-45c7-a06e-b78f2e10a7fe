"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { trpc } from "@/providers/Providers";
import ManageUserForm from "./manage-user.form";
import { MangeUserForm, useMangeUserForm } from "@/hooks/forms/manage-user";
import { Co<PERSON>, LogIn, Pencil, Scroll } from "lucide-react";
import { useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn, toastPromise } from "@/lib/utils";
import { signIn, useSession } from "next-auth/react";
import { Input } from "@/components/ui/input";
import { CompanyType } from "../../../shared/types/Company";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { USER_ROLE } from "../../../shared/types/User";

export default function UserDatagrid() {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const users = trpc.user.getUsers.useQuery();
  const { methods } = useMangeUserForm();
  const { data: session } = useSession();

  const handleSubmit = (data: MangeUserForm) => {
    const entries = Object.entries(data);
    entries.forEach(([key, data]) => {
      // @ts-ignore
      methods.setValue(key, data);
    });
    setOpen(true);
  };

  const generateSSL = trpc.user.singleSignOnLink.useMutation();

  const copySSL = (userId: string) => {
    toastPromise({
      asyncFunc: generateSSL.mutateAsync(
        { userId },
        {
          onSuccess(data) {
            const token = data.token;
            const sslUrl = `${process.env.NEXT_PUBLIC_FE_URL!}/ssl/${token}`;
            navigator.clipboard.writeText(sslUrl);
          },
        }
      ),
      success: "Single Signon Link copied to your clipboard",
    });
  };

  const delegateLogin = async (userId: string) => {
    const delegateUser = await generateSSL.mutateAsync({ userId });
    signIn("credentials", {
      response: JSON.stringify({
        ...delegateUser,
        delegate: true,
        deletegateAuthorSession: session,
      }),
      redirect: true,
      callbackUrl: "/",
    });
  };
  // @ts-ignore
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const requiredData = users?.data?.filter((d) => !d.email.includes("demo"));

  const filteredUser = requiredData?.filter((user) => {
    return (
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.companies?.some((com) =>
        com.company.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  });

  return (
    <div className="">
      <Input
        type="search"
        placeholder="Search...."
        value={searchTerm}
        onChange={handleSearchChange}
        className="mb-4 mt-2 p-2 border border-gray-300 rounded-2xl max-w-lg"
      />
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <ManageUserForm methods={methods} isUpdate />
        </DialogContent>
      </Dialog>
      <div className="ph1 overflow-y-auto">
        <Table>
          <TableHeader>
            <TableRow className=" border-b-2 border-black bg-gray-300 hover:bg-gray-300">
              <TableHead className="text-base text-black text-center">
                Company
              </TableHead>
              <TableHead className="text-base text-black text-center">
                Vendor
              </TableHead>
              <TableHead className="text-base text-black">Users</TableHead>
              <TableHead className="text-base text-black">Users Role</TableHead>
              <TableHead className="text-base text-black">Update</TableHead>
              <TableHead className="text-base text-black">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>OTA</TooltipTrigger>
                    <TooltipContent>
                      <p className="w-52">
                        One Time Access (OTA) gives the tempory access to the
                        particular user for centain time period
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableHead>
              <TableHead className="text-base text-black">Delegate</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUser?.map((user) => {
              return (
                <TableRow key={String(user._id)}>
                  <TableCell className="">
                    {user.role === USER_ROLE.SUPERADMIN
                      ? "All Companies"
                      : null}
                    {user.companies?.map((c) => {
                      return (
                        <div key={c._id} className=" text-cyan-600">
                          {c.type === CompanyType.COMPANY
                            ? c.company
                            : c.type === CompanyType.VENDOR
                            ? c.reportsTo?.company
                            : ""}
                        </div>
                      );
                    })}
                  </TableCell>
                  <TableCell className="">
                    {user.role === USER_ROLE.SUPERADMIN
                      ? "All Companies"
                      : null}
                    {user.companies?.map((c) => {
                      return (
                        <div key={c._id} className="text-green-600">
                          {c.type === CompanyType.VENDOR ? c.company : "-"}
                        </div>
                      );
                    })}
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    {user.role === USER_ROLE.EMPLOYEE
                      ? "VENDOR-RESPONDENT"
                      : user.role === USER_ROLE.SUPERADMIN
                      ? "SUPER-ADMIN"
                      : user.role.toUpperCase()}
                  </TableCell>

                  <TableCell>
                    <Button
                      onClick={() =>
                        handleSubmit({
                          companies: user.companies.map((_) => String(_._id)),
                          contact: user.contact,
                          email: user.email,
                          password: "",
                          role: user.role,
                          user: String(user._id),
                        })
                      }
                    >
                      <Pencil className="size-4 mr-2" />
                      Update
                    </Button>
                  </TableCell>
                  <TableCell>
                    <Button
                      size="icon"
                      onClick={() => copySSL(String(user._id))}
                    >
                      <Copy />
                    </Button>
                  </TableCell>
                  <TableCell>
                    <Button onClick={() => delegateLogin(String(user._id))}>
                      <LogIn className="size-4 mr-2" /> Delegate
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
