'use client';
import { Ava<PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { nameToInitials } from '@/lib/utils';
import { signIn, useSession } from 'next-auth/react';
import { signOut } from 'next-auth/react';

import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from '@/components/ui/menubar';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Link from 'next/link';
import React, { useMemo, useState } from 'react';
import ManageCompanyDialog from './company/manage-company.dialog';

import { CompanyType } from '../../shared/types/Company';

import {
  File,
  FileStack,
  LogOut,
  MoonIcon,
  Settings2Icon,
  SunIcon,
  UserCogIcon,
  UserRoundCog,
} from 'lucide-react';
import { USER_ROLE } from '../../shared/types/User';
import ManageVendorDialog from './company/manage.vendor.dialog';
import {
  useManageCompanyForm,
  useManageVendorForm,
} from '@/hooks/forms/company';
import Image from 'next/image';
import { trpc } from '@/providers/Providers';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { useTheme } from 'next-themes';

export default function NavBar() {
  const { data: session, status } = useSession();
  const { setTheme } = useTheme();
  const [companyDialog, setCompanyDialog] = useState(false);
  const [vendorDialog, setVendorDialog] = useState(false);
  const { data: me, isLoading: isMeLoading } = trpc.user.me.useQuery();
  const menuItems = useMemo(() => {
    return [
      {
        link: '/',
        Menu: 'Home',
        subContent: [],
        access: [
          USER_ROLE.SUPERADMIN,
          USER_ROLE.COMPANY,
          USER_ROLE.VENDOR,
          USER_ROLE.SOC_ANALYST,
          USER_ROLE.COMPANY_ADMIN,
          USER_ROLE.LEAD_SOC_ANALYST,
          USER_ROLE.AUDITOR,
          USER_ROLE.EMPLOYEE,
        ],
      },
      {
        link: '',
        Menu: 'Company',
        access: [USER_ROLE.SUPERADMIN],
        subContent: [
          {
            link: '/admin/company',
            Menu: 'Companies',
            access: [USER_ROLE.SUPERADMIN],
          },
          {
            Menu: 'Create Company',
            isDialog: setCompanyDialog,
            access: [USER_ROLE.SUPERADMIN],
          },
        ],
      },
      {
        link: '/admin/vendors',
        Menu: ' Vendor',
        access: [USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN],
        subContent: [
          {
            link: '/admin/vendors',
            Menu: 'Vendors ',
            access: [USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN],
          },
          {
            Menu: 'Create Vendor ',
            isDialog: setVendorDialog,
            access: [USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN],
          },
        ],
      },
      {
        link: '',
        Menu: 'Manage Sections',
        access: [USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN],
        subContent: [
          {
            Menu: 'Create / Modify Standards',
            link: '/admin/standards',
            access: [USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN],
          },
          {
            Menu: 'Create / Modify Sections',
            link: '/admin/sections',
            access: [USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN],
          },
          {
            Menu: 'Create / Modify Sub Sections',
            link: '/admin/sub-sections',
            access: [USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN],
          },
          {
            Menu: 'Create / Modify Questions',
            link: '/admin/questions',
            access: [USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN],
          },
          {
            Menu: 'Create / Modify Assessment',
            link: '/admin/assessments',
            access: [USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN],
          },
        ],
      },
      {
        link: '/admin/vendor-assessments',
        Menu: 'Assesments',
        subContent: [],
        access: [USER_ROLE.VENDOR],
      },
      {
        Menu: 'SOC Analyst',
        link: '/admin/soc-analyst',
        subContent: [],
        access: [USER_ROLE.SUPERADMIN],
      },
      {
        link: '/reports',
        Menu: 'Reports',
        subContent: [],
        access: [
          USER_ROLE.SUPERADMIN,
          USER_ROLE.COMPANY,
          USER_ROLE.SOC_ANALYST,
          USER_ROLE.VENDOR,
          USER_ROLE.LEAD_SOC_ANALYST,
          USER_ROLE.COMPANY_ADMIN,
          USER_ROLE.AUDITOR,
        ],
      },
    ];
  }, [setCompanyDialog, setVendorDialog]);

  const displayMenuByPerm = useMemo(() => {
    return menuItems.some((menu) =>
      menu.access.includes(me?.role as USER_ROLE)
    );
  }, [menuItems, me?.role]);

  const { fieldArray: manageCompanyFieldArray, methods: manageCompanyMethods } =
    useManageCompanyForm();

  const {
    criticalityFieldArray,
    engagementManagerFieldArray,
    methods,
    vendorAssesmentFieldArray,
  } = useManageVendorForm();

  const exitDelegateAccess = () => {
    signIn('credentials', {
      response: JSON.stringify({
        ...session?.user.deletegateAuthorSession?.user,
        delegate: false,
        deletegateAuthorSession: null,
      }),
      redirect: true,
      callbackUrl: '/',
    });
  };

  if (status === 'loading' || isMeLoading || !me) return null;
  return (
    <div className='mb-3 '>
      <nav className='border-b px-4 shadow-md bg-cyan-700'>
        <div className='container py-4 flex justify-between items-center '>
          <div className='flex flex-row justify-center items-end'>
            <Link href='/'>
              <Image
                src='/images/logo.png'
                alt='logo'
                height={50}
                width={150}
              />
            </Link>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger className='text-xs underline font-semibold'>
                  {session?.user.role === USER_ROLE.EMPLOYEE
                    ? 'Vendor Respondent'
                    : session?.user.role}
                </TooltipTrigger>
                <TooltipContent>
                  <p>Add to library</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          {/* Menu Items */}
          {displayMenuByPerm && (
            <Menubar className='border-0 rounded-xl p-4   '>
              {menuItems.map(({ link, Menu, subContent, access }, idx) => {
                const isSubContentAvailable = !!subContent.length;

                if (!access?.includes(me.role)) return null;
                return (
                  <MenubarMenu key={idx}>
                    <MenubarTrigger className=''>
                      {!isSubContentAvailable ? (
                        <Link href={link}>{Menu}</Link>
                      ) : (
                        Menu
                      )}
                    </MenubarTrigger>
                    {isSubContentAvailable && (
                      <MenubarContent>
                        {subContent.map(({ link, Menu, isDialog }, idx) => {
                          return (
                            <MenubarItem
                              key={idx}
                              onClick={() => isDialog?.(true)}
                            >
                              {isDialog ? (
                                Menu
                              ) : (
                                <Link href={link} className='w-full h-full'>
                                  {Menu}
                                </Link>
                              )}
                            </MenubarItem>
                          );
                        })}
                      </MenubarContent>
                    )}
                  </MenubarMenu>
                );
              })}
            </Menubar>
          )}
          <div className='flex flex-col items-center divide-y divide-black divide-opacity-20 gap-2 '>
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Avatar className='shadow-lg shadow-cyan-900 hover:scale-105 '>
                  <AvatarFallback delayMs={100} className=''>
                    {nameToInitials(me.email).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>My Account-{me.email}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {me.role === USER_ROLE.SUPERADMIN ? (
                  <DropdownMenuItem>
                    <Link
                      href='/admin/manage-users'
                      className='flex flex-row gap-2 w-full'
                    >
                      <UserCogIcon />
                      User management
                    </Link>
                  </DropdownMenuItem>
                ) : null}
                {session?.user.role === USER_ROLE.SUPERADMIN ? (
                  <DropdownMenuItem>
                    <Link href='/files' className='flex flex-row gap-2  w-full'>
                      <FileStack /> Manage Files
                    </Link>
                  </DropdownMenuItem>
                ) : null}

                <DropdownMenuItem
                  className='flex flex-row gap-2'
                  onClick={() => signOut()}
                >
                  <LogOut /> Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <p className='text-sm'>My Account</p>
          </div>
        </div>

        {/* Modals */}
        <ManageCompanyDialog
          open={companyDialog}
          onOpenChange={setCompanyDialog}
          type={CompanyType.COMPANY}
          methods={manageCompanyMethods}
          fieldArray={manageCompanyFieldArray}
        />
        <ManageVendorDialog
          criticalityFieldArray={criticalityFieldArray}
          engagementManagerFieldArray={engagementManagerFieldArray}
          vendorAssesmentFieldArray={vendorAssesmentFieldArray}
          methods={methods}
          type={CompanyType.VENDOR}
          onOpenChange={setVendorDialog}
          open={vendorDialog}
        />
      </nav>
      {/* Delegate Login Info */}
      {session?.user?.delegate && (
        <div className='bg-amber-50 w-full z-10 p-2 '>
          <div className='container flex items-center gap-4'>
            <div className='font-medium'>
              Delegate logged in as {session?.user?.email}
            </div>
            <div
              className='bg-red-400 text-white px-4 rounded-full cursor-pointer'
              onClick={exitDelegateAccess}
            >
              Exit
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
