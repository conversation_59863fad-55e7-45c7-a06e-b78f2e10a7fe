"use client";

import { useState } from "react";
import { trpc } from "@/providers/Providers";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, Calculator, Calendar, User, Building } from "lucide-react";
import { format } from "date-fns";

export default function VendorCalculatedData({
  assessment,
  company,
  vendor,
  onSelectionChange,
  selectedCalculation: externalSelectedCalculation,
}: {
  company: string;
  vendor: string;
  assessment: string;
  onSelectionChange?: (calculation: any) => void;
  selectedCalculation?: any;
}) {
  const [selectedCalculation, setSelectedCalculation] = useState<any | null>(
    null
  );

  // Query calculator data with filters
  const {
    data: calculatorData,
    isLoading,
    error,
    refetch,
  } = trpc.calculator.getCalculatorData.useQuery(
    {
      assessment,
      vendor,
      company,
    },
    {
      enabled: !!assessment && !!vendor,
      refetchOnWindowFocus: false,
    }
  );

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      case "moderate":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "very high":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-3" />
          <p className="text-gray-600">Loading CVSS calculations...</p>
          <p className="text-sm text-gray-400 mt-1">
            Fetching saved risk assessments
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <div className="text-red-600 mb-4">
            <svg
              className="h-12 w-12 mx-auto mb-3"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <p className="font-medium">Failed to load calculator data</p>
            <p className="text-sm text-red-500 mt-1">
              Unable to fetch CVSS calculations
            </p>
          </div>
          <Button
            onClick={() => refetch()}
            variant="outline"
            size="sm"
            className="border-red-300 text-red-600 hover:bg-red-50"
          >
            🔄 Try Again
          </Button>
        </div>
      </div>
    );
  }

  const calculations = calculatorData?.data || [];

  if (calculations.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 max-w-md mx-auto">
          <Calculator className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Risk Calculations Found
          </h3>
          <p className="text-gray-600 mb-4">
            No CVSS calculations have been saved for this assessment and vendor
            combination.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-blue-700">
            💡 <strong>Tip:</strong> Use the CVSS Calculator to perform risk
            assessments and save them for future reference.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">
                Total Calculations
              </p>
              <p className="text-2xl font-bold text-blue-900">
                {calculations.length}
              </p>
            </div>
            <Calculator className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">
                Avg Base Score
              </p>
              <p className="text-2xl font-bold text-green-900">
                {calculations.length > 0
                  ? (
                      calculations.reduce(
                        (sum: number, calc: any) =>
                          sum + calc.riskResults.baseScore,
                        0
                      ) / calculations.length
                    ).toFixed(1)
                  : "0.0"}
              </p>
            </div>
            <div className="h-8 w-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">
              📊
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-600">
                Avg Risk Reduction
              </p>
              <p className="text-2xl font-bold text-orange-900">
                {calculations.length > 0
                  ? (
                      calculations.reduce(
                        (sum: number, calc: any) =>
                          sum + calc.riskResults.deltaScore,
                        0
                      ) / calculations.length
                    ).toFixed(1)
                  : "0.0"}
              </p>
            </div>
            <div className="h-8 w-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">
              📉
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">
                Latest Calculation
              </p>
              <p className="text-sm font-bold text-purple-900">
                {calculations.length > 0
                  ? format(
                      new Date(
                        Math.max(
                          ...calculations.map((calc: any) =>
                            new Date(calc.calculationDate).getTime()
                          )
                        )
                      ),
                      "MMM dd"
                    )
                  : "None"}
              </p>
            </div>
            <Calendar className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Calculations Grid */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">
            Saved Risk Calculations
          </h3>
          <Badge variant="outline" className="text-sm">
            {calculations.length} calculation
            {calculations.length !== 1 ? "s" : ""}
          </Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {calculations.map((calc: any) => (
            <div
              key={calc._id}
              className={`relative border rounded-xl p-5 cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 ${
                selectedCalculation?._id === calc._id
                  ? "border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 shadow-md"
                  : "border-gray-200 hover:border-blue-300 bg-white hover:bg-gray-50"
              }`}
              onClick={() => {
                setSelectedCalculation(calc);
                onSelectionChange?.(calc);
              }}
            >
              {/* Selection indicator */}
              {selectedCalculation?._id === calc._id && (
                <div className="absolute top-3 right-3">
                  <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                </div>
              )}

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-base truncate text-gray-900">
                    {calc.name || `Risk Assessment ${calc._id.slice(-6)}`}
                  </h4>
                  <div className="flex items-center gap-1 text-xs text-gray-500 mt-1">
                    <Calendar className="h-3 w-3" />
                    {format(new Date(calc.calculationDate), "MMM dd, yyyy")}
                  </div>
                </div>

                <div className="space-y-3">
                  {/* Base Score */}
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Base Score</span>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-16 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-green-400 to-red-500 rounded-full"
                          style={{
                            width: `${
                              (calc.riskResults.baseScore / 10) * 100
                            }%`,
                          }}
                        ></div>
                      </div>
                      <Badge variant="outline" className="text-xs font-bold">
                        {calc.riskResults.baseScore.toFixed(1)}
                      </Badge>
                    </div>
                  </div>

                  {/* Risk Level */}
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Risk Level</span>
                    <Badge
                      className={`text-xs font-medium ${getRiskLevelColor(
                        calc.riskResults.riskLevelAfter
                      )}`}
                    >
                      {calc.riskResults.riskLevelAfter}
                    </Badge>
                  </div>

                  {/* Risk Reduction */}
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">
                      Risk Reduction
                    </span>
                    <Badge
                      variant="outline"
                      className={`text-xs font-bold ${
                        calc.riskResults.deltaScore > 0
                          ? "text-green-700 border-green-300 bg-green-50"
                          : "text-gray-600"
                      }`}
                    >
                      -{calc.riskResults.deltaScore.toFixed(2)}
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <User className="h-3 w-3" />
                  {calc.user.contact.firstName} {calc.user.contact.lastName}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed View */}
      {selectedCalculation && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="border-b border-gray-200 px-6 py-4">
            <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Calculation Details:{" "}
              {selectedCalculation.name ||
                `Calculation ${selectedCalculation._id.slice(-6)}`}
            </h3>
          </div>
          <div className="p-6 space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">Base Score</p>
                <p className="text-2xl font-bold text-blue-600">
                  {selectedCalculation.riskResults.baseScore.toFixed(1)}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">
                  Environmental Score
                </p>
                <p className="text-2xl font-bold text-orange-600">
                  {selectedCalculation.riskResults.envScoreAfter.toFixed(2)}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">
                  Risk Reduction
                </p>
                <p className="text-2xl font-bold text-green-600">
                  {selectedCalculation.riskResults.deltaScore.toFixed(2)}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">
                  Final Risk Level
                </p>
                <Badge
                  className={`text-sm ${getRiskLevelColor(
                    selectedCalculation.riskResults.riskLevelAfter
                  )}`}
                >
                  {selectedCalculation.riskResults.riskLevelAfter}
                </Badge>
              </div>
            </div>

            {/* CVSS Vector */}
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-600">CVSS Vector</p>
              <div className="bg-gray-50 p-3 rounded-md">
                <code className="text-sm font-mono break-all">
                  {selectedCalculation.cvssVector}
                </code>
              </div>
            </div>

            {/* Residual Vector */}
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-600">
                Residual Vector
              </p>
              <div className="bg-gray-50 p-3 rounded-md">
                <code className="text-sm font-mono break-all">
                  {selectedCalculation.riskResults.residualVector}
                </code>
              </div>
            </div>

            {/* Security Risk Overview */}
            {selectedCalculation.securityRiskOverview && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600">
                  Security Risk Posture Overview
                </p>
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm whitespace-pre-wrap">
                    {selectedCalculation.securityRiskOverview}
                  </p>
                </div>
              </div>
            )}

            {/* VendorAI Score */}
            {selectedCalculation.vendorAIScore && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600">
                  VendorAI Score Notes
                </p>
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm whitespace-pre-wrap">
                    {selectedCalculation.vendorAIScore}
                  </p>
                </div>
              </div>
            )}

            {/* Metadata */}
            <div className="border-t pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Created:</span>
                  <span>
                    {format(
                      new Date(selectedCalculation.calculationDate),
                      "PPP"
                    )}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">By:</span>
                  <span>
                    {selectedCalculation.user.contact.firstName}{" "}
                    {selectedCalculation.user.contact.lastName}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Vendor:</span>
                  <span>{selectedCalculation.vendor.company}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
