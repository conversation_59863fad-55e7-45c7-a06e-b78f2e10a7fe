import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>et,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from '@/components/ui/sheet';
import { Table, TableCell, TableHead, TableRow } from '@/components/ui/table';
import { Download, Minus } from 'lucide-react';
import { RouterOutput } from '../../../shared';
import React, { useState } from 'react';
import { useRole } from '@/hooks/useRole';
import {
  VulnerabilityClosureStatus,
  VulnerabilityResolveStatus,
} from '../../../shared/types/AssessmentSubmission';
import { cn, toastPromise } from '@/lib/utils';
import { useDownload } from '@/hooks/useDownload';
import { z } from 'zod';
import { updateVulnerabilityClosureBySocValidator } from '../../../shared/validators/assessment-submission.validator';
import { FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { trpc } from '@/providers/Providers';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
} from '@/components/ui/dialog';
import { FormSelect } from '@/components/form/FormSelect';
import { FormTextField } from '@/components/form/FormTextField';

type AssesmentSubmission =
  RouterOutput['assessment']['getAssessmentSubmissions'][number];

type Form = z.infer<typeof updateVulnerabilityClosureBySocValidator>;

export default function VulnerabilityCloseSheet({
  role,
  submission,
}: {
  role: ReturnType<typeof useRole>;
  submission: AssesmentSubmission;
}) {
  const [open, setOpen] = useState(false);
  const download = useDownload();
  const methods = useForm<Form>({
    resolver: zodResolver(updateVulnerabilityClosureBySocValidator),
    defaultValues: {
      submissionId: String(submission._id),
      vulnerabilityClosedDescriptionBySoc: '',
      vulnerabilityClosureStatus: '' as VulnerabilityClosureStatus,
    },
  });
  const vulnerabilityClosure =
    trpc.assessment.updateVulnerabilityClosureBySoc.useMutation();
  const handleSubmit = methods.handleSubmit((data) => {
    toastPromise({
      asyncFunc: vulnerabilityClosure.mutateAsync(data),
      success: 'Success',
      onSuccess() {
        methods.reset();
      },
    });
  });

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogTitle className='text-lg font-bold'>
            Are you sure to close the vulnerability
          </DialogTitle>

          <form onSubmit={handleSubmit} className='space-y-10'>
            <FormProvider {...methods}>
              <FormSelect
                name='vulnerabilityClosureStatus'
                options={[
                  {
                    label: VulnerabilityClosureStatus.OPEN,
                    value: VulnerabilityClosureStatus.OPEN,
                  },
                  {
                    label: VulnerabilityClosureStatus.CLOSED,
                    value: VulnerabilityClosureStatus.CLOSED,
                  },
                ]}
                placeholder='select response'
                label='Vulnerability Close'
              />
              <FormTextField
                name='vulnerabilityClosedDescriptionBySoc'
                placeholder='Vulnerability Close Description by SOC'
                label='Vulnerability Close Description'
              />
              <Button className=' w-full'>Submit</Button>
            </FormProvider>
          </form>
          <DialogClose>
            <Button className='w-full' variant={'destructive'}>
              cancel
            </Button>
          </DialogClose>
        </DialogContent>
      </Dialog>
      <Sheet>
        <SheetTrigger
          disabled={
            !submission.vulnerability?.vulnerabilityClosureCompanyApprovalStatus
              ?.length ||
            submission.vulnerability
              ?.vulnerabilityClosureCompanyApprovalStatus ===
              VulnerabilityClosureStatus.PENDING
          }
        >
          {submission?.vulnerability?.vulnerabilityClosureDescriptionBySoc ? (
            <Badge
              className={cn(
                `w-40 h-8 ${
                  submission.vulnerability
                    ?.vulnerabilityClosureCompanyApprovalStatus ===
                  VulnerabilityClosureStatus.OPEN
                    ? 'bg-red-700 pl-8 '
                    : submission.vulnerability
                        ?.vulnerabilityClosureCompanyApprovalStatus ===
                      VulnerabilityClosureStatus.CLOSED
                    ? 'bg-green-900 pl-5 '
                    : 'bg-orange-400 pl-8'
                }`
              )}
            >
              {submission.vulnerability
                ?.vulnerabilityClosureCompanyApprovalStatus ===
              VulnerabilityClosureStatus.OPEN
                ? 'REQUIRED REVIEW'
                : submission.vulnerability
                    ?.vulnerabilityClosureCompanyApprovalStatus ===
                  VulnerabilityClosureStatus.CLOSED
                ? 'APPROVED FOR CLOSURE'
                : 'PENDING'}
            </Badge>
          ) : (
            <Minus size={30} className='ml-12' />
          )}
        </SheetTrigger>
        <SheetContent className='min-w-[1250px] flex flex-col '>
          <SheetHeader>
            <SheetTitle>Company Vulnerability Closure Status</SheetTitle>
          </SheetHeader>
          <Table>
            <TableRow>
              <TableHead> Company Clouser Status</TableHead>
              <TableCell>
                {submission.vulnerability
                  ?.vulnerabilityClosureCompanyApprovalStatus ===
                VulnerabilityClosureStatus.OPEN
                  ? 'REQUIRED REVIEW'
                  : submission.vulnerability
                      ?.vulnerabilityClosureCompanyApprovalStatus ===
                    VulnerabilityClosureStatus.CLOSED
                  ? 'APPROVED FOR CLOSURE'
                  : 'PENDING'}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableHead>Company Closure Description</TableHead>
              <TableCell>
                {
                  submission.vulnerability
                    ?.vulnerabilityClosureDescriptionByCompany
                }
              </TableCell>
            </TableRow>
            <TableRow>
              <TableHead>Company Closure Evidence</TableHead>
              <TableCell>
                <Button size={'icon'}>
                  <Download
                    onClick={() =>
                      download({
                        fileId: submission.vulnerability
                          ?.vulnerabilityClosureEvidenceByCompany as string,
                      })
                    }
                  />
                </Button>
              </TableCell>
            </TableRow>
          </Table>
          <SheetFooter className='sticky bottom-0  self-center'>
            <Button onClick={() => setOpen(true)}>Close Vulnerability</Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </>
  );
}
