import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/sheet';
import { TableRow, Table, TableHead, TableCell } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@radix-ui/react-scroll-area';
import { Download, Minus } from 'lucide-react';
import { RouterOutput } from '../../../shared';
import {
  VendorAcceptanceStatus,
  VulnerabilityClosureStatus,
} from '../../../shared/types/AssessmentSubmission';
import { useRole } from '@/hooks/useRole';
import { Button } from '@/components/ui/button';
import { useDownload } from '@/hooks/useDownload';
import { format } from 'date-fns';

type AssesmentSubmission =
  RouterOutput['assessment']['getAssessmentSubmissions'][number];

type Props = {
  submission: AssesmentSubmission;
  role: ReturnType<typeof useRole>;
  onUpdateVulnerabilityPress: (
    assessmentSubmission: AssesmentSubmission
  ) => void;
};

export function VulnerabilityResponseSheet({
  role: { isSoc, isLeadSoc },
  submission,
  onUpdateVulnerabilityPress,
}: Props) {
  const downloadFile = useDownload();
  return (
    <Sheet>
      <SheetTrigger
        disabled={!submission.vendorAcceptanceStatus?.length}
        className='item-center'
      >
        {submission.vulnerability?.criticalityLevel ? (
          <Badge
            className={cn(
              'w-32 py-2 self-center ',
              submission.vendorAcceptanceStatus ===
                VendorAcceptanceStatus.REJECT
                ? 'bg-red-700 hover:bg-red-500 pl-2'
                : submission.vendorAcceptanceStatus ===
                  VendorAcceptanceStatus.ACCEPT
                ? 'bg-green-600 pl-2'
                : 'bg-orange-500 '
            )}
          >
            {submission.vendorAcceptanceStatus === VendorAcceptanceStatus.ACCEPT
              ? 'ACKNOWLEDGED'
              : submission.vendorAcceptanceStatus ===
                VendorAcceptanceStatus.REJECT
              ? 'FALSE POSITIVE'
              : 'PENDING'}
          </Badge>
        ) : (
          <Minus size={30} className='ml-12' />
        )}
      </SheetTrigger>
      <SheetContent className='min-w-[1000px] flex flex-col '>
        <ScrollArea className='ph1'>
          <SheetHeader>
            <SheetTitle>Vendor Vulnerability Response</SheetTitle>
          </SheetHeader>
          <Table>
            <TableRow>
              <TableHead>Vendor Acceptance Status</TableHead>
              <TableCell>
                {submission?.vendorAcceptanceStatus ===
                VendorAcceptanceStatus.ACCEPT
                  ? 'ACKNOWLEDGED'
                  : 'FALSE POSITIVE'}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableHead>Attached Evidence</TableHead>
              <TableCell>
                <Button
                  onClick={() =>
                    downloadFile({
                      fileId: submission.rejectionEvidence as string,
                    })
                  }
                >
                  <Download />
                </Button>
              </TableCell>
            </TableRow>

            <TableRow>
              <TableHead>Vendor Reason</TableHead>
              <TableCell>{submission.vendorRejectReason}</TableCell>
            </TableRow>
          </Table>
        </ScrollArea>
        <SheetFooter className='sticky bottom-0  self-center'>
          {(isSoc || isLeadSoc) && (
            <Button
              onClick={() => onUpdateVulnerabilityPress(submission)}
              disabled={
                submission.vendorAcceptanceStatus ===
                VendorAcceptanceStatus.ACCEPT
              }
            >
              Update Vulnerability Rating
            </Button>
          )}
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
