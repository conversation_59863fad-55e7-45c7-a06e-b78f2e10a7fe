import VulnerabilityResolveDataGrid from '@/app/(dashboard)/assessment/[assessment]/vulnerabiility-resolve.datagrid';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Sheet<PERSON>rigger,
  SheetContent,
  SheetFooter,
  Sheet,
} from '@/components/ui/sheet';
import { useRole } from '@/hooks/useRole';
import { cn, toastPromise } from '@/lib/utils';
import {
  ArrowUpRightFromSquareIcon,
  Check,
  Minus,
  Plus,
  X,
} from 'lucide-react';
import React, { useState } from 'react';
import { RouterOutput } from '../../../shared';
import {
  CompanyExceptionApproval,
  VendorAcceptanceStatus,
  VulnerabilityResolveStatus,
} from '../../../shared/types/AssessmentSubmission';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@radix-ui/react-scroll-area';
import { trpc } from '@/providers/Providers';
import VendorVulnerabilityClosureDataGrid from '@/app/(dashboard)/assessment/[assessment]/vendor-vulnerability-closer.datagrid';
import VulnerabilityClosureBySocForm from '@/elements/assessments/vulnerability-closure-by-soc.form';
import VulnerabilityExceptionDataGrid from '@/elements/assessments/vulnerability-exception.datagrid';
import VulnerabilityExceptionBySocForm from '@/elements/assessments/vulnerability-exception-from-by-soc.form';
import AssessmentExceptionApprovedForm from '@/elements/assessments/assessment-exception-approved.form';

type AssesmentSubmission =
  RouterOutput['assessment']['getAssessmentSubmissions'][number];

export const VulnerabilityResolveAndExceptionSheet = ({
  role: { isSoc, isLeadSoc },

  onUpdateVulnerabilityPress,
  submission,
}: {
  role: ReturnType<typeof useRole>;
  submission: AssesmentSubmission;
  onUpdateVulnerabilityPress: (
    assessmentSubmission: AssesmentSubmission
  ) => void;
}) => {
  const [exceptionDialog, setExceptionDialog] = useState(false);
  const [closureDialog, setClosureDialog] = useState(false);
  const [socEvidenceDialog, setSocEvidenceDialog] = useState(false);
  const [socExceptionDialog, setSocExceptionDialog] = useState(false);
  const [socExceptionApprovedDialog, setSocExceptionApprovedDialog] =
    useState(false);

  // const requestVulnerabilityExceptionSocToCompany =
  //   trpc.assessment.requestVulnerabilityExceptionSocToCompany.useMutation();

  // const requestVulnerabilityClosureBySocToCompany =
  //   trpc.assessment.requestVulnerabilityClosureBySocToCompany.useMutation();

  // const handleRequestExceptionSocToCompany = () => {
  //   toastPromise({
  //     asyncFunc: requestVulnerabilityExceptionSocToCompany.mutateAsync({
  //       submissionId: String(submission._id),
  //     }),
  //   });
  // };

  // const requestClosureToCompany = () => {
  //   toastPromise({
  //     asyncFunc: requestVulnerabilityClosureBySocToCompany.mutateAsync({
  //       submissionId: String(submission._id),
  //       vulnerabilityClosureDescriptionBySoc,
  //       vulnerabilityClosureEvidenceBySoc
  //     }),
  //   });
  // };

  return (
    <Sheet>
      <Dialog open={exceptionDialog} onOpenChange={setExceptionDialog}>
        <DialogContent className='min-w-[1200px] ph1 '>
          <ScrollArea>
            <DialogHeader>
              <DialogTitle>Exception</DialogTitle>
            </DialogHeader>
            <VulnerabilityExceptionDataGrid submission={submission} />
            <DialogFooter className='sticky bottom-0 gap-2 mt-10'>
              <Button onClick={() => setSocExceptionDialog(true)}>
                <Plus />
                Soc exception
              </Button>

              <DialogClose asChild>
                <Button>Close</Button>
                {/* <Button onClick={handleRequestExceptionSocToCompany}>
                  Send Mail
                </Button> */}
              </DialogClose>
            </DialogFooter>
          </ScrollArea>
        </DialogContent>
      </Dialog>
      {/* Exception Dialog */}
      <Dialog open={socExceptionDialog} onOpenChange={setSocExceptionDialog}>
        <DialogContent className='min-w-[1200px] ph1 '>
          <ScrollArea>
            <DialogHeader>
              <DialogTitle>Soc Evidence and Description attach</DialogTitle>
            </DialogHeader>
            <VulnerabilityExceptionBySocForm
              submissionId={String(submission._id)}
            />
            <DialogFooter className='sticky bottom-0 gap-2 mt-10'></DialogFooter>
          </ScrollArea>
        </DialogContent>
      </Dialog>
      <Dialog
        open={socExceptionApprovedDialog}
        onOpenChange={setSocExceptionApprovedDialog}
      >
        <DialogContent className='min-w-[1200px] ph1 '>
          <ScrollArea>
            <DialogHeader>
              <DialogTitle>Soc Evidence and Description attach</DialogTitle>
            </DialogHeader>
            <AssessmentExceptionApprovedForm
              submissionId={String(submission._id)}
            />
            <DialogFooter className='sticky bottom-0 gap-2 mt-10'></DialogFooter>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      <Dialog open={closureDialog} onOpenChange={setClosureDialog}>
        <DialogContent className='min-w-[1200px] ph1 '>
          <DialogHeader>
            <DialogTitle>Vulnerability Closure</DialogTitle>
          </DialogHeader>
          <VendorVulnerabilityClosureDataGrid submission={submission} />
          <DialogFooter>
            <Button onClick={() => setSocEvidenceDialog(true)}>
              <Plus />
              Attach
            </Button>
            <DialogClose asChild>
              <Button>Close</Button>
              {/* <Button onClick={handleRequestExceptionSocToCompany}>
                Send Mail
              </Button> */}
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog open={socEvidenceDialog} onOpenChange={setSocEvidenceDialog}>
        <DialogContent className='min-w-[1200px] ph1 '>
          <DialogHeader>
            <DialogTitle>Soc Evidence and Description attach</DialogTitle>
          </DialogHeader>
          <VulnerabilityClosureBySocForm
            submissionId={String(submission._id)}
          />
        </DialogContent>
      </Dialog>

      <SheetTrigger
        className='flex flex-row items-center justify-center'
        disabled={!submission?.vendorAcceptanceStatus}
      >
        {submission.vendorAcceptanceStatus ? (
          <>
            <Badge
              className={cn(
                `py-2 w-32 pl-7 ${
                  submission?.vendorAcceptanceStatus ===
                  VendorAcceptanceStatus.REJECT
                    ? 'bg-red-500'
                    : submission?.vulnerability?.resolveStatus ===
                      VulnerabilityResolveStatus.RESOLVED
                    ? 'bg-green-600'
                    : submission?.vulnerability?.resolveStatus ===
                      VulnerabilityResolveStatus.EXCEPTION
                    ? 'bg-orange-600'
                    : submission.vulnerability?.resolveStatus ?? 'bg-orange-600'
                }`
              )}
            >
              {submission.vendorAcceptanceStatus ===
              VendorAcceptanceStatus.REJECT
                ? 'REVIEW'
                : submission.vulnerability?.resolveStatus?.toUpperCase() ??
                  'OPEN '}
            </Badge>
            {submission.vulnerability?.companyExceptionApproval ===
            CompanyExceptionApproval.ACCEPTED ? (
              <Check size={35} color='green' />
            ) : submission.vulnerability?.companyExceptionApproval ===
              CompanyExceptionApproval.REJECTED ? (
              <X size={35} color='red' />
            ) : null}
          </>
        ) : (
          <Minus size={30} className='ml-12' />
        )}
      </SheetTrigger>
      <SheetContent className='min-w-[1250px] flex flex-col p-4 items-center'>
        <VulnerabilityResolveDataGrid
          vulnerability={submission.vulnerability}
          // @ts-ignore
          isRejected={submission.vendorAcceptanceStatus}
        />
        <SheetFooter className='sticky bottom-0  '>
          {(isSoc || isLeadSoc) && (
            <div className='flex flex-row items-center justify-center gap-10'>
              <Button onClick={() => onUpdateVulnerabilityPress(submission)}>
                Update Vulnerability Rating
              </Button>
              <Button
                onClick={() => setExceptionDialog(true)}
                disabled={
                  submission.vulnerability?.resolveStatus !==
                  VulnerabilityResolveStatus.EXCEPTION
                }
              >
                <Plus />
                Exception
              </Button>
              <Button
                onClick={() => setSocExceptionApprovedDialog(true)}
                className='gap-2'
              >
                Send Exception
                <ArrowUpRightFromSquareIcon size={18} />
              </Button>
              <Button
                onClick={() => setClosureDialog(true)}
                disabled={
                  submission.vendorAcceptanceStatus ===
                    VendorAcceptanceStatus.ACCEPT &&
                  submission.vulnerability?.resolveStatus !==
                    VulnerabilityResolveStatus.RESOLVED
                }
              >
                <Plus />
                Request Closure
              </Button>
            </div>
          )}
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
