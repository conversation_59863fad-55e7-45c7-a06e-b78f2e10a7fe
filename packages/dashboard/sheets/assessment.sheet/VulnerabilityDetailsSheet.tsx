import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { RouterOutput } from '../../../shared';
import { CRITICALITY_LEVELS } from '../../../shared/types/Company';
import { Separator } from '@/components/ui/separator';
import { differenceInDays, sub } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { SECTIONTYPE } from '../../../shared/types/Standard';
import { useRole } from '@/hooks/useRole';
import { Button } from '@/components/ui/button';
import { useDownload } from '@/hooks/useDownload';
import { Download } from 'lucide-react';
import { VulnerabilityClosureStatus } from '../../../shared/types/AssessmentSubmission';

type AssesmentSubmission =
  RouterOutput['assessment']['getAssessmentSubmissions'][number];

type Props = {
  submission: AssesmentSubmission;
  role: ReturnType<typeof useRole>;
  onVulnerabilityPress: (assessmentSubmission: AssesmentSubmission) => void;
  onUpdateVulnerabilityPress: (
    assessmentSubmission: AssesmentSubmission
  ) => void;
};

export function VulnerabilityDetailsSheet({
  submission: _submission,
  role: { isAuditor, isSoc, isLeadSoc },
  onUpdateVulnerabilityPress,
  onVulnerabilityPress,
}: Props) {
  const download = useDownload();
  const { submission, vulnerability, sectionType, vendor } = _submission;
  const expiryDate = vulnerability?.expiry;
  const remainingDays = expiryDate
    ? differenceInDays(new Date(expiryDate), new Date())
    : null;

  const remainingDaysColor =
    remainingDays !== null
      ? remainingDays > 30
        ? 'text-green-700 font-semibold text-md'
        : remainingDays > 10
        ? 'text-amber-600 font-semibold text-md'
        : remainingDays >= 5
        ? 'text-red-700 font-semibold text-md'
        : 'gray'
      : 'black';
  const remainingDaysBgColor =
    remainingDays !== null
      ? remainingDays > 30
        ? 'bg-green-800'
        : remainingDays > 10
        ? 'bg-amber-600'
        : remainingDays >= 5
        ? 'bg-red-700 '
        : 'bg-red-700 '
      : 'black';
  return (
    <Sheet>
      <SheetTrigger className='flex flex-row gap-2'>
        {vulnerability?.criticalityLevel ? (
          <>
            <Badge
              className={cn(
                'font-bold w-40 py-[10px] ',
                vulnerability?.criticalityLevel === CRITICALITY_LEVELS.HIGH &&
                  'bg-rose-600 pl-12',
                vulnerability?.criticalityLevel ===
                  CRITICALITY_LEVELS.CRITICAL && 'bg-red-700 pl-12',
                vulnerability?.criticalityLevel === CRITICALITY_LEVELS.MEDIUM &&
                  'bg-orange-600 pl-9',
                vulnerability?.criticalityLevel === CRITICALITY_LEVELS.LOW &&
                  'bg-yellow-600 pl-12',
                vulnerability?.criticalityLevel ===
                  CRITICALITY_LEVELS.VERYLOW && 'bg-green-700 pl-8'
              )}
            >
              {/* @ts-ignore */}
              {vulnerability?.criticalityLevel?.toUpperCase()}
            </Badge>
            <Separator orientation='vertical' className='h-8 border' />
            <Badge
              className={`w-40 py-[10px] tracking-tighter ${remainingDaysBgColor}`}
            >
              {_submission.vulnerability?.vulnerabilityClosureStatus ===
              VulnerabilityClosureStatus.CLOSED
                ? ' Vulnerability Resolved In Time'
                : remainingDays !== null
                ? remainingDays >= 0
                  ? `DAYS TO RESOLVE ${remainingDays}`
                  : `Overdue by ${Math.abs(remainingDays)} days `
                : null}
            </Badge>
          </>
        ) : (
          <Badge className='font-bold w-[340px] pl-20 py-2 text-[16px] '>
            <pre>RAISE VULNERABILITY</pre>
          </Badge>
        )}
      </SheetTrigger>
      <SheetContent className='min-w-[1200px] flex flex-col p-4 items-center'>
        <div className='ph overflow-y-scroll'>
          <SheetHeader>
            <SheetTitle className='text-black font-semibold underline '>
              Vulnerability Details :-
            </SheetTitle>
          </SheetHeader>
          <Table>
            <TableRow>
              <TableHead className='text-black font-semibold  '>
                Inventory
              </TableHead>
              <TableCell>{submission?.question?.input.label}</TableCell>
            </TableRow>
            <TableRow>
              <TableHead className='text-black font-semibold  '>
                Inventory Details
              </TableHead>
              <TableCell>
                {Array.isArray(submission.answer)
                  ? submission.answer.map((_, idx) => (
                      <Badge key={idx}>{_}</Badge>
                    ))
                  : submission.answer}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableHead>Inventory FIle</TableHead>
              <TableRow>
                <Button size={'icon'}>
                  <Download />
                </Button>
              </TableRow>
            </TableRow>
          </Table>

          {sectionType === SECTIONTYPE.INVENTORY && (
            <>
              <Table>
                <TableHeader>
                  <TableRow className='text-center'>
                    <TableHead className='text-black font-semibold  '>
                      Cvss Score
                    </TableHead>
                    <TableHead className='text-black font-semibold'>
                      VendorAi Score
                    </TableHead>
                    <TableHead className='text-black font-semibold'>
                      Criticality
                    </TableHead>
                    <TableHead className='text-black font-semibold'>
                      MSA/SOW Remediation Timelines
                    </TableHead>
                    <TableHead className='text-black font-semibold'>
                      Countdown
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow className='text-center'>
                    <TableCell>{vulnerability?.cvssScore ?? '-'}</TableCell>
                    <TableCell>{vulnerability?.score ?? '-'}</TableCell>
                    <TableCell
                      className={cn(
                        'font-bold',
                        vulnerability?.criticalityLevel ===
                          CRITICALITY_LEVELS.HIGH && 'text-rose-600 w',

                        vulnerability?.criticalityLevel ===
                          CRITICALITY_LEVELS.CRITICAL && 'text-red-700',

                        vulnerability?.criticalityLevel ===
                          CRITICALITY_LEVELS.MEDIUM && 'text-orange-600',

                        vulnerability?.criticalityLevel ===
                          CRITICALITY_LEVELS.LOW && 'text-yellow-600',

                        vulnerability?.criticalityLevel ===
                          CRITICALITY_LEVELS.VERYLOW && 'text-green-700'
                      )}
                    >
                      {vulnerability?.criticalityLevel.toUpperCase() ?? '-'}
                    </TableCell>
                    <TableCell>
                      {vendor?.criticalityLevels?.map((c, idx) => (
                        <div key={idx}>
                          {vulnerability?.criticalityLevel === c.level
                            ? c.timeDuration
                            : ''}
                        </div>
                      ))}
                    </TableCell>
                    <TableCell className={remainingDaysColor}>
                      {remainingDays !== null
                        ? remainingDays >= 0
                          ? `Days Remaining: ${remainingDays}`
                          : `Expired ${Math.abs(remainingDays)} days ago`
                        : 'No expiry date set'}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
              <Table>
                <TableRow>
                  <TableHead className='text-black font-semibold border-2 '>
                    Vulnerability Description
                  </TableHead>
                  <TableCell>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: vulnerability?.remarks as string,
                      }}
                      className='text-lg'
                    />
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableHead>Vulnerability Evidence</TableHead>
                  <TableCell>
                    <Button size={'icon'}>
                      <Download
                        onClick={() =>
                          download({
                            fileId:
                              vulnerability?.vulnerabilityEvidence as string,
                          })
                        }
                      />
                    </Button>
                  </TableCell>
                </TableRow>
              </Table>
            </>
          )}
          {sectionType === SECTIONTYPE.GENERAL && (
            <Table>
              <TableCell>{_submission?.audit?.score}</TableCell>
              <TableCell>{_submission?.audit?.remarks}</TableCell>
            </Table>
          )}
        </div>
        <SheetFooter className='sticky bottom-0 items-center'>
          {(isSoc || isLeadSoc || isAuditor) && (
            <Button
              className='px-10'
              disabled={!!vulnerability?.cvssScore}
              onClick={() => {
                onVulnerabilityPress(_submission);
              }}
            >
              {isAuditor && `Audit Score`}
              {(isSoc || isLeadSoc) && `Vulnerability Rating`}
            </Button>
          )}
          {(isSoc || isLeadSoc) && (
            <Button
              disabled={!vulnerability?.cvssScore}
              onClick={() => {
                onUpdateVulnerabilityPress(_submission);
              }}
            >
              Update Vulnerability Rating
            </Button>
          )}
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
