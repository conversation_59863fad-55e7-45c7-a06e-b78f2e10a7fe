'use client';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from '@/components/ui/sheet';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useDownload } from '@/hooks/useDownload';
import { cn } from '@/lib/utils';
import { trpc } from '@/providers/Providers';
import { format } from 'date-fns';
import { Download, Upload } from 'lucide-react';
import {
  VendorAcceptanceStatus,
  VulnerabilityClosureStatus,
  VulnerabilityResolveStatus,
} from '../../../shared/types/AssessmentSubmission';
import { CRITICALITY_LEVELS } from '../../../shared/types/Company';
import React from 'react';
import { useExcellDownload } from '@/hooks/useExcelDownload';

type Props = {
  question: string;
};
export default function AssessmentSubmissionHistorySheet({ question }: Props) {
  const download = useDownload();
  const downloadExcel = useExcellDownload();
  const submissionHistory =
    trpc.assessment.getAssessmentSubmissionHistory.useQuery({
      question,
    });
  const handleDownload = () => {
    const data = (submissionHistory.data || []).map((history) => ({
      Event_Log_Time: format(history.createdAt, 'MMM d, yyyy - h:mm a'),
      Sender: history.actionTaker?.email,
      Recipient: history.actionReciver?.email,
      Inventory: history?.submissionSnapshot?.submission?.question?.input.label,
      InventoryDetails: history.submissionSnapshot.submission.answer,
      CvssScore: history.submissionSnapshot.vulnerability?.cvssScore,
      VendorAiScore: history.submissionSnapshot.vulnerability?.score,
      Criticality:
        history.submissionSnapshot.vulnerability?.criticalityLevel.toUpperCase(),
      VulnerabilityCreatedAt: history.submissionSnapshot.vulnerability
        ?.createdAt
        ? format(
            history.submissionSnapshot.vulnerability?.createdAt,
            'MMM d, yyyy - h:mm a'
          )
        : '-',
      VulnerabilityDescription:
        history?.submissionSnapshot?.vulnerability?.remarks ?? '-',
      VendorAcceptance:
        history?.submissionSnapshot?.vendorAcceptanceStatus ===
        VendorAcceptanceStatus.ACCEPT
          ? 'Vendor acknowledged for remediation(s)'
          : history.submissionSnapshot?.vendorAcceptanceStatus ===
            VendorAcceptanceStatus.REJECT
          ? 'Vendor identified false positive'
          : null,
      VendorAcceptanceDescription:
        history?.submissionSnapshot?.vendorRejectReason,
      VulnerabilityResolvedDate: history?.submissionSnapshot?.vulnerability
        ?.resolvedDate
        ? format(
            history?.submissionSnapshot?.vulnerability?.resolvedDate,
            'MMM d, yyyy - h:mm a'
          )
        : null,
      VendorExceptionReason:
        history?.submissionSnapshot.vulnerability?.resolveDescription,
      SocExceptionReason:
        history?.submissionSnapshot.vulnerability
          ?.vulnerabilityExceptionApprovalDescriptionBySoc,
      VulnerabilityCloserDescriptionBySoc:
        history?.submissionSnapshot.vulnerability
          ?.vulnerabilityClosureDescriptionBySoc,
      VulnerabilityClosureStatusByCustomer:
        history?.submissionSnapshot.vulnerability
          ?.vulnerabilityClosureCompanyApprovalStatus ===
        VulnerabilityClosureStatus.CLOSED
          ? 'Approved for closure'
          : history?.submissionSnapshot.vulnerability
              ?.vulnerabilityClosureCompanyApprovalStatus ===
            VulnerabilityClosureStatus.OPEN
          ? 'Required reassessment'
          : 'Pending',
    }));

    downloadExcel(data, `Logs_${new Date()}.xlsx`);
  };
  return (
    <Sheet>
      <SheetTrigger>
        <Button> Vsoc Logs</Button>
      </SheetTrigger>
      <SheetContent className='min-w-[70vw] bg-indigo-950'>
        <ScrollArea className='ph'>
          <SheetHeader>
            <SheetTitle className='text-white text-3xl flex gap-10 '>
              <div>Vsoc Log History</div>
              <Button onClick={handleDownload} className='flex gap-2'>
                <Download size={24} />
                <p className='text-base'>Export History</p>
              </Button>
            </SheetTitle>
          </SheetHeader>

          <div>
            <Accordion type='single' collapsible>
              {(submissionHistory.data || []).map((history, idx) => {
                return (
                  <AccordionItem
                    value={String(history._id)}
                    key={String(history._id)}
                  >
                    <AccordionTrigger className='text-white text-lg'>
                      {format(history?.createdAt, 'MMM d,yyyy - h:mm a')}
                    </AccordionTrigger>
                    <AccordionContent>
                      <Table>
                        <TableBody>
                          <TableRow>
                            <TableHead> Sender:</TableHead>
                            <TableCell className='text-lg'>
                              {history?.actionTaker?.email}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead>Reciepient:</TableHead>

                            <TableCell className='text-lg'>
                              {history?.actionReciver?.email}
                            </TableCell>
                          </TableRow>

                          <TableRow>
                            <TableHead>Inventory:</TableHead>
                            <TableCell className='text-lg'>
                              {
                                history?.submissionSnapshot?.submission
                                  ?.question?.input.label
                              }
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead>Inventory Details:</TableHead>
                            <TableCell className='text-lg'>
                              {history.submissionSnapshot.submission.answer}
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                      <Table>
                        <TableHeader>
                          <TableRow className='text-center'>
                            <TableHead className='text-black font-semibold  '>
                              Cvss Score
                            </TableHead>
                            <TableHead className='text-black font-semibold text-center'>
                              VendorAi Score
                            </TableHead>
                            <TableHead className='text-black font-semibold text-center'>
                              Criticality
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow className='text-center'>
                            <TableCell className='text-lg text-start'>
                              {
                                history.submissionSnapshot.vulnerability
                                  ?.cvssScore
                              }
                            </TableCell>
                            <TableCell className='text-lg '>
                              {history.submissionSnapshot.vulnerability?.score}
                            </TableCell>
                            <TableCell
                              className={cn(
                                'font-bold text-lg',
                                // @ts-ignore
                                history.submissionSnapshot?.vulnerability
                                  ?.criticalityLevel ===
                                  CRITICALITY_LEVELS.HIGH && 'text-rose-600 w',
                                // @ts-ignore
                                history.submissionSnapshot?.vulnerability
                                  ?.criticalityLevel ===
                                  CRITICALITY_LEVELS.CRITICAL && 'text-red-700',
                                // @ts-ignore
                                history.submissionSnapshot?.vulnerability
                                  ?.criticalityLevel ===
                                  CRITICALITY_LEVELS.MEDIUM &&
                                  'text-orange-600',
                                // @ts-ignore
                                history.submissionSnapshot?.vulnerability
                                  ?.criticalityLevel ===
                                  CRITICALITY_LEVELS.LOW && 'text-yellow-600',
                                // @ts-ignore
                                history.submissionSnapshot?.vulnerability
                                  ?.criticalityLevel ===
                                  CRITICALITY_LEVELS.VERYLOW && 'text-green-700'
                              )}
                            >
                              {history.submissionSnapshot?.vulnerability?.criticalityLevel.toUpperCase()}
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                      <Table>
                        <TableBody>
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Vulnerability Created Date :
                            </TableHead>
                            <TableCell className='text-lg'>
                              {/* @ts-ignore */}
                              {history.submissionSnapshot.vulnerability
                                ?.createdAt
                                ? format(
                                    // @ts-ignore
                                    history.submissionSnapshot.vulnerability
                                      ?.createdAt,
                                    'MMM-d-yyyy - h:mm a'
                                  )
                                : '-'}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead>Vulnerability Description</TableHead>
                            <TableCell className='text-lg'>
                              <div
                                dangerouslySetInnerHTML={{
                                  __html:
                                    history?.submissionSnapshot?.vulnerability
                                      ?.remarks ?? '-',
                                }}
                              />
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Vulnerability Acceptance by Vendor:
                            </TableHead>
                            <TableCell className='text-lg'>
                              {history?.submissionSnapshot
                                ?.vendorAcceptanceStatus ===
                              VendorAcceptanceStatus.ACCEPT
                                ? 'Vendor acknowledged for remediation(s)'
                                : history.submissionSnapshot
                                    ?.vendorAcceptanceStatus ===
                                  VendorAcceptanceStatus.REJECT
                                ? ' Vendor identified false positive'
                                : null}
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Vulnerability Acceptance Description:
                            </TableHead>
                            <TableCell className='text-lg'>
                              {history?.submissionSnapshot?.vendorRejectReason}
                            </TableCell>
                          </TableRow>
                          {history?.submissionSnapshot.vulnerability
                            ?.resolveStatus ===
                          VulnerabilityResolveStatus.RESOLVED ? (
                            <>
                              <TableRow>
                                <TableHead className='text-black font-semibold  '>
                                  Vulnerability Resolved Date:
                                </TableHead>
                                <TableCell className='text-lg'>
                                  {history?.submissionSnapshot?.vulnerability
                                    ?.resolvedDate
                                    ? format(
                                        history?.submissionSnapshot
                                          ?.vulnerability?.resolvedDate,
                                        'MMM - d -yyyy - h:mm a'
                                      )
                                    : null}
                                </TableCell>
                              </TableRow>
                            </>
                          ) : (
                            ''
                          )}
                          {history?.submissionSnapshot.vulnerability
                            ?.resolveStatus ===
                          VulnerabilityResolveStatus.EXCEPTION ? (
                            <>
                              <TableRow>
                                <TableHead className='text-black font-semibold  '>
                                  Vendor Exception Reason:
                                </TableHead>
                                <TableCell className='text-lg'>
                                  {
                                    history?.submissionSnapshot.vulnerability
                                      ?.resolveDescription
                                  }
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableHead className='text-black font-semibold  '>
                                  Soc Exception Reason:
                                </TableHead>
                                <TableCell className='text-lg'>
                                  {
                                    history?.submissionSnapshot.vulnerability
                                      ?.vulnerabilityExceptionApprovalDescriptionBySoc
                                  }
                                </TableCell>
                              </TableRow>

                              <TableRow>
                                <TableHead className='text-black font-semibold  '>
                                  Soc Exception Evidence:
                                </TableHead>
                                <TableCell>
                                  <Button>
                                    <Download
                                      onClick={() =>
                                        download({
                                          fileId: history?.submissionSnapshot
                                            .vulnerability
                                            ?.vulnerabilityExceptionApprovalEvidenceBySoc as string,
                                        })
                                      }
                                    />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            </>
                          ) : null}

                          {history?.submissionSnapshot.vulnerability
                            ?.vulnerabilityClosureDescriptionBySoc ? (
                            <>
                              <TableRow>
                                <TableHead className='text-black font-semibold  '>
                                  Vulnerability Closer Description By Soc:
                                </TableHead>
                                <TableCell className='text-lg'>
                                  {
                                    history?.submissionSnapshot.vulnerability
                                      ?.vulnerabilityClosureDescriptionBySoc
                                  }
                                </TableCell>
                              </TableRow>
                            </>
                          ) : null}
                          <TableRow>
                            <TableHead className='text-black font-semibold  '>
                              Vulnerability Closure status by Customer:
                            </TableHead>
                            <TableCell className='text-lg'>
                              {history?.submissionSnapshot.vulnerability
                                ?.vulnerabilityClosureCompanyApprovalStatus ===
                              VulnerabilityClosureStatus.CLOSED
                                ? 'Approved for closure'
                                : history?.submissionSnapshot.vulnerability
                                    ?.vulnerabilityClosureCompanyApprovalStatus ===
                                  VulnerabilityClosureStatus.OPEN
                                ? 'Required reassessment'
                                : 'Pending'}
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}
