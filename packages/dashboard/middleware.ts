import { getToken } from 'next-auth/jwt';
import { withAuth } from 'next-auth/middleware';
import { NextFetchEvent, NextRequest, NextResponse } from 'next/server';

const publicPages = ['/terms_conditions', '/register', '/sign', '/links'];

export default async function middleware(
  req: NextRequest,
  event: NextFetchEvent
) {
  const token = await getToken({ req });
  const isAuthenticated = !!token;

  if (req.nextUrl.pathname.includes('.')) return;
  if (req.nextUrl.pathname.startsWith('/reset')) return;
  if (req.nextUrl.pathname.startsWith('/ssl')) return;
  if (req.nextUrl.pathname.startsWith('/links')) return;
  if (publicPages.includes(req.nextUrl.pathname)) return;

  if (req.nextUrl.pathname.startsWith('/login') && isAuthenticated) {
    return NextResponse.redirect(new URL('/', req.url));
  }

  const authMiddleware = await withAuth({
    pages: {
      signIn: `/login`,
    },
  });

  // @ts-expect-error
  return authMiddleware(req, event);
}
