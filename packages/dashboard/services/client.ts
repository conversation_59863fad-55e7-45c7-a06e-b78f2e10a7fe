import { useMutation } from '@tanstack/react-query';
import { z } from 'zod';

type Payload = { file: File };

async function fileUpload({ file }: Payload): Promise<{
  status: 'ok';
  s3Path: string;
}> {
  const formdata = new FormData();
  formdata.append('file', file);

  const response = await fetch('http://localhost:8080/upload', {
    method: 'POST',
    body: formdata,
    redirect: 'follow',
  });

  return response.json();
}

export default function useFileUpload() {
  return useMutation((payload: Payload) => fileUpload(payload));
}
