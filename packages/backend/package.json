{"name": "be", "version": "1.0.0", "main": "app.ts", "license": "MIT", "scripts": {"dev": "nodemon app.ts", "build": "npx tsc", "start": "node ./dist/backend/app.js"}, "dependencies": {"@azure/msal-node": "^2.13.0", "@react-email/render": "^0.0.12", "@trpc/server": "^10.44.1", "aws-sdk": "^2.1687.0", "axios": "^1.6.7", "bcrypt": "^5.1.1", "cors": "^2.8.5", "date-fns": "^3.0.0", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-fileupload": "^1.4.3", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mime-types": "^2.1.35", "mongodb-client-encryption": "^6.0.0", "mongoose": "^8.0.2", "nanoid": "3.0.0", "nodemailer": "^6.9.13", "nodemon": "^3.1.7", "superjson": "1.13.3", "trpc-transformer": "^3.0.0", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.4.4", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/mime-types": "^2.1.4", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "ts-node-dev": "^2.0.0", "typescript": "^5.3.2"}}