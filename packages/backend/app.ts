import "express-async-errors";

import "./startup/mongoose";

import express from "express";
import cors from "cors";
import fileUpload from "express-fileupload";
import { createExpressMiddleware } from "@trpc/server/adapters/express";
import { appRouter } from "./routes/routes";
import { createContext } from "./context";
import config from "./utils/config";
import uploadRoute from "./routes/upload.route";

const app = express();
app.use(express.json({ limit: "100MB" }));
app.use(express.urlencoded({ extended: true }));
app.use(cors({ origin: "*" }));
app.use(fileUpload());

app.use("/file-upload", uploadRoute);

app.use(
  "/trpc",
  createExpressMiddleware({
    router: appRouter,
    createContext,
    onError(error: any) {
      console.log(error.path, error.error);
    },
  })
);
const PORT = config.PORT;
app.listen(PORT, () => console.log(`server started on port ${PORT}`));
