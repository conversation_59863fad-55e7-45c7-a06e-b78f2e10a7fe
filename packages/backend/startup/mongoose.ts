import mongoose from 'mongoose';
import config from '../utils/config';
import bcrypt from 'bcrypt';
import { UserModel } from '../models/user.model';
import { timestampsPlugin } from '../models/timestamps.plugin';
import { USER_ROLE } from '../../shared/types/User';

mongoose.plugin(timestampsPlugin);

mongoose.connect(config.DB_URL!).then(async () => {
  try {
    console.log('connected to database');
    // create default super-admin if does not exist
    const hashPassword = await bcrypt.hash(config.SUPERADMIN_PASSWORD!, 10);
    await UserModel.updateOne(
      { email: config.SUPERADMIN_EMAIL },
      {
        email: config.SUPERADMIN_EMAIL,
        hashPassword,
        role: USER_ROLE.SUPERADMIN,
      },
      { upsert: true }
    );
  } catch (ex) {
    if (ex instanceof Error) {
      if (ex.message.includes('E11000')) console.log('superadmin exists');
    }
  }
});
