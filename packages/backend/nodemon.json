{"env": {"DB_URL": "mongodb+srv://rafeeq:<EMAIL>/vendor-ai--dev", "DB_NAME": "vendorai--staging", "PORT": 8080, "JWT_SECRET": "1qp0FHGx/BGx+DtuIL1k0gP7v5VLZrNjk0j0Y5Vap+g=", "SUPERADMIN_EMAIL": "<EMAIL>", "SUPERADMIN_PASSWORD": "azszg?49doisC$rN", "ADMIN_EMAIL": "<EMAIL>", "ADMIN_EMAIL_APP_PASSWORD": "Dios-45@12!", "FRONTEND_URL": "https://staging.vendorsecurity.ai", "RECAPTCHA_SECRET_KEY": "6Ldfxv4pAAAAAO_pADmRkX6q5zsTujoNI1QtrEWU", "TENANT_ID": "0be30d5c-d68a-42eb-9045-e013cde4850d", "CLIENT_ID": "e0acd6e7-9258-4c0d-818d-8bcec78c67d3", "CLIENT_SECRET": "****************************************", "SENDER_EMAIL": "<EMAIL>", "AWS_ACCESS_KEY_ID": "********************", "AWS_SECRET_ACCESS_KEY": "pByG1jFq3uDkuka5i0s3PeNvJ/KhvYVW1YSy8GvB", "AWS_S3_REGION": "us-east-2", "AWS_S3_BUCKET": "vendorai--dev"}}