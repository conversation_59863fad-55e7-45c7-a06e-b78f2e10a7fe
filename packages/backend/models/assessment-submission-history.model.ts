import { Schema, model } from 'mongoose';
import { AssesmentSubmissionHistory } from '../../shared/types/AssessmentSubmissionHistory';
import { AssessmentModel } from './assessment.model';
import { CompanyModel } from './company.model';
import { UserModel } from './user.model';
import { QuestionModel } from './questions.model';
import { AssessmentSubmissionModel } from './asessment-submission.model';

const schema = new Schema<AssesmentSubmissionHistory>({
  assessment: { required: true, type: Schema.ObjectId, ref: AssessmentModel },
  submission: {
    type: Schema.ObjectId,
    ref: AssessmentSubmissionModel,
  },
  actionTaker: { required: true, type: Schema.ObjectId, ref: UserModel },
  actionReciver: { type: Schema.ObjectId, ref: UserModel },
  vendor: { required: true, type: Schema.ObjectId, ref: CompanyModel },
  message: String,
  htmlContent: String,
  question: { type: Schema.ObjectId, ref: QuestionModel },
  submissionSnapshot: { type: Schema.Types.Mixed },
});

export const AsessmentSubmissionHistoryModel = model(
  'assessment-submission-history',
  schema
);
