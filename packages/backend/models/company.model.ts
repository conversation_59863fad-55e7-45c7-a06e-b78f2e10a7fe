import mongoose, { Schema, Types, model } from 'mongoose';
import {
  Company,
  Contact,
  CriticalityLevel,
  License,
  Vendor,
} from '../../shared/types/Company';

export const contactSchema = new Schema<Contact>({
  phone: { required: true, type: String },
  email: { required: true, type: String },
  countryCode: String,
  firstName: { required: true, type: String },
  lastName: { required: true, type: String },
  title: String,
});

const schema = new Schema<Company | Vendor>({
  company: { required: true, type: String },
  sector: { required: true, type: String },
  address: { required: true, type: String },
  country: { required: true, type: String },
  state: { required: true, type: String },
  city: { required: true, type: String },
  location: String,
  zipcode: { required: true, type: String },
  // @ts-ignore
  primaryContact: [contactSchema],
  numOfLicensedVendors: Number,
  priorityLevel: String,
  type: {
    required: true,
    type: String,
  },
  reportsTo: { type: Schema.Types.ObjectId, ref: 'company' },
  criticalityLevels: [
    new Schema<CriticalityLevel>({ level: String, timeDuration: Number }),
  ],
  companyEngagementManager: [contactSchema],
  vendorAssessmentManager: [contactSchema],
});

export const CompanyModel = mongoose.model('company', schema);

const licenseSchema = new Schema<License>({
  from: { required: true, type: Date },
  to: { required: true, type: Date },
  company: { required: true, type: Schema.ObjectId, ref: CompanyModel },
});
export const LicenseModel = model('license', licenseSchema);
