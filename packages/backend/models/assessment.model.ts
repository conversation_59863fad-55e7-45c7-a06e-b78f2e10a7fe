import mongoose, { Schema, model } from 'mongoose';
import {
  ASSIGNMENT_STATUS,
  Assessment,
  Assignment,
} from '../../shared/types/Assesment';
import { CompanyModel, contactSchema } from './company.model';
import { SectionModel, StandardModel, SubSectionModel } from './standard.model';
import { QuestionModel, questionSchema } from './questions.model';
import { UserModel } from './user.model';

const assignmentSchema = new Schema<Assignment>({
  sections: [
    {
      required: true,
      type: Schema.Types.ObjectId,
      ref: SectionModel,
    },
  ],
  questions: [
    {
      required: true,
      type: Schema.Types.ObjectId,
      ref: QuestionModel,
    },
  ],
  files: [
    {
      userId: { type: Schema.Types.ObjectId, ref: UserModel },
      file: String,
      compliance: String,
    },
  ],
  employee: { required: true, type: Schema.ObjectId, ref: UserModel },
  assignmentStatus: {
    required: true,
    type: String,
    default: ASSIGNMENT_STATUS.PENDING,
  },
  assignmentCompletedDate: Date,
});

const schema = new Schema<Assessment>({
  company: { required: true, type: Schema.Types.ObjectId, ref: CompanyModel },
  vendor: { required: true, type: Schema.Types.ObjectId, ref: CompanyModel },

  name: { required: true, type: String },
  expiresAt: { required: true, type: Date },
  companyEngagementManager: { required: true, type: contactSchema },
  vendorAssessmentManager: { required: true, type: contactSchema },

  sections: [
    {
      required: true,
      type: Schema.Types.ObjectId,
      ref: SectionModel,
    },
  ],
  assignments: { required: true, type: [assignmentSchema] },
});

export const AssessmentModel = mongoose.model('assessment', schema);
