import mongoose, { Schema } from 'mongoose';
import { AccountReset, User } from '../../shared/types/User';
import { CompanyModel } from './company.model';

const schema = new Schema<User>({
  email: { required: true, type: String, unique: true },
  hashPassword: { required: true, type: String },
  role: { required: true, type: String },
  status: { type: String },
  companies: { required: true, type: [Schema.ObjectId], ref: CompanyModel },
  loginRetries: { type: Number, default: 0 },
  contact: {
    type: new Schema({
      phone: { required: true, type: String },
      countryCode: String,
      firstName: { required: true, type: String },
      lastName: { required: true, type: String },
      title: String,
    }),
  },
});

schema.pre('save', function (next) {
  this.email = this.email.toLowerCase();
  next();
});

export const UserModel = mongoose.model('user', schema);

// Account Reset
export const AccountResetModel = mongoose.model(
  'account-reset',
  new Schema<AccountReset>({
    expires: { required: true, type: Date },
    user: { required: true, type: Schema.ObjectId, ref: UserModel },
  })
);
