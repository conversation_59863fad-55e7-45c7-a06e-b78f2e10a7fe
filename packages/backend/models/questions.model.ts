import { Schema, model } from 'mongoose';
import { Question } from '../../shared/types/Question';
import { SectionModel, StandardModel, SubSectionModel } from './standard.model';

export const questionSchema = new Schema<Question>({
  input: { required: true, type: Schema.Types.Mixed },

  standard: { required: true, type: Schema.ObjectId, ref: StandardModel },
  section: { required: true, type: Schema.ObjectId, ref: SectionModel },
  subSection: { required: true, type: Schema.ObjectId, ref: SubSectionModel },
  complience: { required: true, type: [String] },

  weightage: { required: true, type: String },
  canAttachDocument: Boolean,
  file: String,
});

export const QuestionModel = model('question', questionSchema);
