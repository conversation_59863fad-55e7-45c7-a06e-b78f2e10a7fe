import { Schema, model } from "mongoose";
import { SECTIONTYPE } from "../../shared/types/Standard";
import {
  AssessmentSubmission,
  AuditorScore,
  Vulnerability,
} from "../../shared/types/AssessmentSubmission";
import { AssessmentModel } from "./assessment.model";
import { CompanyModel } from "./company.model";
import { FileModel } from "./file.model";
import { QuestionModel } from "./questions.model";
import { UserModel } from "./user.model";
import { number } from "zod";
import { CVSSSavingModel } from "./comparison-save.model";

const assessmentSubmissionSchema = new Schema<AssessmentSubmission>({
  sectionType: { required: true, type: String },
  assessment: { required: true, type: Schema.ObjectId, ref: AssessmentModel },
  vendor: { required: true, type: Schema.ObjectId, ref: CompanyModel },
  user: { required: true, type: Schema.ObjectId, ref: UserModel },
  submission: {
    required: true,
    type: new Schema({
      question: { required: true, type: Schema.ObjectId, ref: QuestionModel },
      answer: Schema.Types.Mixed,
      files: [{ type: Schema.ObjectId, ref: FileModel }],
    }),
  },
  cvssComparison: {
    type: Schema.Types.ObjectId,
    ref: CVSSSavingModel.modelName,
  },
  vulnerability: new Schema<Vulnerability>({
    analysisBy: { required: true, type: Schema.ObjectId, ref: UserModel },
    remarks: String,
    score: { required: true, type: String },
    cvssScore: { required: true, type: String },
    criticalityLevel: String,
    expiry: Date,
    vulnerabilityEvidence: { type: Schema.ObjectId, ref: FileModel },

    resolveStatus: String,
    vulnerabilityResolvedOrExceptionEvidence: {
      type: Schema.ObjectId,
      ref: FileModel,
    },
    resolveDescription: String,
    resolvedDate: Date,

    vulnerabilityResolvedUpdateDateBySoc: Date,

    companyExceptionApproval: String,
    companyExceptionApprovalEvidence: { type: Schema.ObjectId, ref: FileModel },
    companyExceptionApprovalDescripton: String,
    companyExceptionEndDate: Date,
    companyExceptionApprovedDate: Date,

    vulnerabilityExceptionApprovalDescriptionBySoc: String,
    vulnerabilityExceptionApprovalEvidenceBySoc: {
      type: Schema.ObjectId,
      ref: FileModel,
    },

    vulnerabilityClosureStatus: String,
    vulnerabilityClosureEvidenceByCompany: {
      type: Schema.ObjectId,
      ref: FileModel,
    },
    vulnerabilityClosureDescriptionBySoc: String,
    vulnerabilityClosureDescriptionByCompany: String,
    vulnerabilityClosureEvidenceBySoc: {
      type: Schema.ObjectId,
      ref: FileModel,
    },

    vulnerabilityExceptionStatusDateSocToVendor: Date,
    vulnerabilityExceptionStatusSocToVendor: String,

    vulnerabilityClosureCompanyApprovalStatus: String,
    vulnerabilityClosureDateByCompany: Date,
    vulnerabilityClosureDateBySoc: Date,
    vulnerabilityClosedDescriptionBySoc: String,
  }),

  vendorAcceptanceStatus: String,
  vendorAcceptanceOrRejectionDate: Date,
  rejectionEvidence: { type: Schema.ObjectId, ref: FileModel },
  vendorRejectReason: String,

  audit: new Schema({
    score: Number,
    remarks: String,
    auditedBy: { type: Schema.ObjectId, ref: UserModel },
  }),
});

// for boolean questions, calculate score automatically
assessmentSubmissionSchema.pre("save", async function (next) {
  if (this.sectionType === SECTIONTYPE.INVENTORY) return next();

  const question = await QuestionModel.findById(
    this.submission.question
  ).lean();
  if (!question) return next();

  if (question.input.inputType !== "switch") return next();
  this.submission.answer ? AuditorScore.ACCEPT : AuditorScore.REJECT;
  this.audit = {
    score: this.submission.answer ? AuditorScore.ACCEPT : AuditorScore.REJECT,
  };

  next();
});

export const AssessmentSubmissionModel = model(
  "assessment-submission",
  assessmentSubmissionSchema
);

const questionsSubmissionStateSchema = new Schema({
  assessment: { required: true, type: Schema.ObjectId, ref: AssessmentModel },
  user: { required: true, type: Schema.ObjectId, ref: UserModel },
  questionsState: { type: Schema.Types.Mixed },
});

export const QuestionsSubmissionStateModel = model(
  "questions-submission-state",
  questionsSubmissionStateSchema
);
