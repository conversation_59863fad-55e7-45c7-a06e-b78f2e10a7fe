import { Schema, model } from 'mongoose';
import { Manage } from '../../shared/types/Manage';
import { UserModel } from './user.model';
import { CompanyModel } from './company.model';

const schema = new Schema<Manage>({
  user: { required: true, type: Schema.ObjectId, ref: UserModel },
  companies: [{ required: true, type: Schema.ObjectId, ref: CompanyModel }],
});

export const ManageModel = model('manage', schema);
