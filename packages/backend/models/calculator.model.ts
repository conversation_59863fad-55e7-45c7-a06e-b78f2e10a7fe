import { Schema, model } from "mongoose";
import { UserModel } from "./user.model";
import { CompanyModel } from "./company.model";
import { AssessmentModel } from "./assessment.model";
import { MID } from "../@types";

export interface CVSSMetrics {
  AV: string; // Attack Vector
  AC: string; // Attack Complexity
  AT: string; // Attack Requirements
  PR: string; // Privileges Required
  UI: string; // User Interaction
  MC: string; // Modified Confidentiality
  MI: string; // Modified Integrity
  MA: string; // Modified Availability
  S: string; // Safety
  R: string; // Recovery
  AU: string; // Automation
}

export interface RiskInputs {
  baseScore: number;
  exploitAvailable: boolean;
  exploitedInWild: boolean;
  assetCriticality: number;
  businessImpact: number;
  exposureLevel: number;
  mitigationStrength: number;
}

export interface RiskResults {
  baseScore: number;
  envScoreBefore: number;
  envScoreAfter: number;
  deltaScore: number;
  riskLevelBefore: string;
  riskLevelAfter: string;
  likelihoodBefore: number;
  likelihoodAfter: number;
  impactBefore: number;
  impactAfter: number;
  residualVector: string;
}

export interface MitigationControl {
  name: string;
  checked: boolean;
}

export interface MitigationControls {
  Network: MitigationControl[];
  Application: MitigationControl[];
  Database: MitigationControl[];
  Encryption: MitigationControl[];
}

export interface CVEData {
  cveId: string;
  cvssScore: string | number;
  published: string;
  description: string;
  source: string;
  metrics: any;
}

export interface CalculatorComparisonData {
  calculatorId: string;
  baseScore: number;
  envScoreBefore: number;
  envScoreAfter: number;
  deltaScore: number;
  riskLevelBefore: string;
  riskLevelAfter: string;
}

export interface CVEComparison {
  assessment: string;
  vendor: string;
  company: string;
  cveData: CVEData;
  calculatorData: CalculatorComparisonData;
  comment: string;
  comparisonDate: Date;
}

export interface CalculatorData {
  company: MID; // Company ID
  assessment: MID; // Assessment ID
  vendor: MID; // Vendor ID
  user: MID; // User ID who created the calculation
  cvssMetrics: CVSSMetrics;
  riskInputs: RiskInputs;
  mitigationControls: MitigationControls;
  cvssVector: string;
  riskResults: RiskResults;
  securityRiskOverview: string;
  vendorAIScore: string;
  calculationDate: Date;
  name?: string; // Optional name for the calculation
  cveComparison?: CVEComparison; // Optional CVE comparison data
}

const cvssMetricsSchema = new Schema<CVSSMetrics>({
  AV: { type: String, default: "" },
  AC: { type: String, default: "" },
  AT: { type: String, default: "" },
  PR: { type: String, default: "" },
  UI: { type: String, default: "" },
  MC: { type: String, default: "" },
  MI: { type: String, default: "" },
  MA: { type: String, default: "" },
  S: { type: String, default: "" },
  R: { type: String, default: "" },
  AU: { type: String, default: "" },
});

const riskInputsSchema = new Schema<RiskInputs>({
  baseScore: { type: Number, required: true },
  exploitAvailable: { type: Boolean, default: false },
  exploitedInWild: { type: Boolean, default: false },
  assetCriticality: { type: Number, required: true },
  businessImpact: { type: Number, required: true },
  exposureLevel: { type: Number, required: true },
  mitigationStrength: { type: Number, default: 0 },
});

const riskResultsSchema = new Schema<RiskResults>({
  baseScore: { type: Number, required: true },
  envScoreBefore: { type: Number, required: true },
  envScoreAfter: { type: Number, required: true },
  deltaScore: { type: Number, required: true },
  riskLevelBefore: { type: String, required: true },
  riskLevelAfter: { type: String, required: true },
  likelihoodBefore: { type: Number, required: true },
  likelihoodAfter: { type: Number, required: true },
  impactBefore: { type: Number, required: true },
  impactAfter: { type: Number, required: true },
  residualVector: { type: String, required: true },
});

const mitigationControlSchema = new Schema<MitigationControl>({
  name: { type: String, required: true },
  checked: { type: Boolean, default: false },
});

const mitigationControlsSchema = new Schema<MitigationControls>({
  Network: [mitigationControlSchema],
  Application: [mitigationControlSchema],
  Database: [mitigationControlSchema],
  Encryption: [mitigationControlSchema],
});

const calculatorSchema = new Schema({
  assessment: {
    required: true,
    type: Schema.Types.ObjectId,
    ref: AssessmentModel,
  },
  vendor: { required: true, type: Schema.Types.ObjectId, ref: CompanyModel },
  company: { required: true, type: Schema.Types.ObjectId, ref: CompanyModel },
  user: { required: true, type: Schema.Types.ObjectId, ref: UserModel },
  cvssMetrics: { required: true, type: cvssMetricsSchema },
  riskInputs: { required: true, type: riskInputsSchema },
  mitigationControls: { required: true, type: mitigationControlsSchema },
  cvssVector: { required: true, type: String },
  riskResults: { required: true, type: riskResultsSchema },
  securityRiskOverview: { type: String, default: "" },
  vendorAIScore: { type: String, default: "" },
  calculationDate: { type: Date, default: Date.now },
  name: { type: String },
  cveComparison: {
    type: {
      assessment: { type: String, required: true },
      vendor: { type: String, required: true },
      company: { type: String, required: true },
      cveData: {
        type: {
          cveId: { type: String, required: true },
          cvssScore: { type: Schema.Types.Mixed, required: true },
          published: { type: String, required: true },
          description: { type: String, required: true },
          source: { type: String, required: true },
          metrics: { type: Schema.Types.Mixed },
        },
        required: true,
      },
      calculatorData: {
        type: {
          calculatorId: { type: String, required: true },
          baseScore: { type: Number, required: true },
          envScoreBefore: { type: Number, required: true },
          envScoreAfter: { type: Number, required: true },
          deltaScore: { type: Number, required: true },
          riskLevelBefore: { type: String, required: true },
          riskLevelAfter: { type: String, required: true },
        },
        required: true,
      },
      comment: { type: String, required: true },
      comparisonDate: { type: Date, required: true },
    },
    required: false,
  },
});

export const CalculatorModel = model("calculator", calculatorSchema);
