import mongoose, { Schema } from 'mongoose';
import { Standard, Section, SubSection } from '../../shared/types/Standard';

/**
 * Standard
 */
const standardSchema = new Schema<Standard>({
  standard: { required: true, type: String },
  description: { required: true, type: String },
});
export const StandardModel = mongoose.model('standard', standardSchema);

/**
 * Sections
 */
const sectionSchema = new Schema<Section>({
  standard: [
    {
      required: true,
      type: Schema.Types.ObjectId,
      ref: StandardModel,
    },
  ],
  section: { required: true, type: String },
  sectionType: String,
  description: { required: true, type: String },
  sectionLable: String,
});
export const SectionModel = mongoose.model('section', sectionSchema);

/**
 * Sub Sections
 */
const subSectionSchema = new Schema<SubSection>({
  section: { required: true, type: Schema.Types.ObjectId, ref: SectionModel },
  subSection: { required: true, type: String },
  description: { required: true, type: String },
});
export const SubSectionModel = mongoose.model('subSection', subSectionSchema);

/**
 * Concepts
 */
// const conceptSchema = new Schema<Concept>({
//   subSection: {
//     required: true,
//     type: Schema.Types.ObjectId,
//     ref: SubSectionModel,
//   },
//   concept: { required: true, type: String },
//   description: { required: true, type: String },
// });

// export const ConceptModel = mongoose.model('concept', conceptSchema);
