import { Schema, model } from 'mongoose';
import { CreditReport } from '../../shared/types/CreditReport';
import { UserModel } from './user.model';
import { CompanyModel } from './company.model';

const creditReportSchema = new Schema<CreditReport>({
  subCategory: { required: true, type: String },
  name: { required: true, type: String },
  quarter: { required: true, type: String },
  remarks: String,
  reportedBy: { required: true, type: Schema.ObjectId, ref: UserModel },
  vendor: { required: true, type: Schema.ObjectId, ref: CompanyModel },
  rating: { required: true, type: Number },
  date: { required: true, type: Date },
  category: { required: true, type: String },
});

export const CreditReportModel = model('credit-report', creditReportSchema);
