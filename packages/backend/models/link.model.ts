import { Schema, model } from 'mongoose';
import { <PERSON> } from '../../shared/types/Link';
import { AssessmentModel } from './assessment.model';
import { addDays } from 'date-fns';
import { QuestionModel } from './questions.model';
import { FileModel } from './file.model';

export const LinkModel = model(
  'link',
  new Schema<Link>({
    expiryDate: {
      required: true,
      type: Date,
      default: addDays(new Date(), 15),
    },
    metadata: new Schema({
      type: { required: true, type: String },
      assessment: { type: Schema.ObjectId, ref: AssessmentModel },
      questions: [{ type: Schema.ObjectId, ref: QuestionModel }],
      role: String,
    }),
    token: String,
  })
);
