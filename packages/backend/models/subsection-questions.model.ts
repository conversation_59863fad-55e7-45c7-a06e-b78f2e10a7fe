import { Schema, model } from 'mongoose';
import { SubSectionQuestion } from '../../shared/types/SubSectionQuestion';
import { QuestionModel } from './questions.model';
import { SubSectionModel } from './standard.model';

const schema = new Schema<SubSectionQuestion>({
  subSection: { required: true, type: Schema.ObjectId, ref: SubSectionModel },
  questions: {
    required: true,
    type: [
      new Schema({
        question: { required: true, type: Schema.ObjectId, ref: QuestionModel },
        rating: { required: true, type: String },
      }),
    ],
  },
});

export const SubSectionQuestionModel = model('sub-section-questions', schema);
