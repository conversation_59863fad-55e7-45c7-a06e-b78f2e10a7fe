import mongoose, { Schema } from "mongoose";
import { z } from "zod";
import { MID } from "../@types";

export type CVSSSaving = {
  assessment: MID;
  vendor: MID;
  company: MID;
  cvssCalculator: MID;
  nvdData: any;
  comments: string;
  asset: string;
  isVulnerability: boolean;
};
export const schema = new Schema<CVSSSaving>(
  {
    assessment: {
      type: Schema.Types.ObjectId,
      ref: "assessment",
      required: true,
    },
    vendor: {
      type: Schema.Types.ObjectId,
      ref: "company",
      required: true,
    },
    company: {
      type: Schema.Types.ObjectId,
      ref: "company",
      required: true,
    },
    cvssCalculator: {
      type: Schema.Types.ObjectId,
      ref: "calculator",
      required: true,
    },
    nvdData: {
      type: Schema.Types.Mixed,
      required: true,
    },
    comments: {
      type: String,
      required: true,
    },
    asset: {
      type: String,
      required: true,
    },
    isVulnerability: {
      type: Boolean,
      required: true,
    },
  },
  { timestamps: true }
);

export const CVSSSavingModel = mongoose.model("cvss-saving", schema);
