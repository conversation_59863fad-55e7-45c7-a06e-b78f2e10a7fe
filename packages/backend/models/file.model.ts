import { Schema, model } from 'mongoose';
import { UserModel } from './user.model';
import { File } from '../../shared/types/File';
import { QuestionModel } from './questions.model';

export const schema = new Schema<File>({
  name: { required: true, type: String },
  base64: { required: true, type: String },
  ext: { required: true, type: String },
  mimeType: { required: true, type: String },
  uploadedBy: { required: true, type: Schema.ObjectId, ref: UserModel },
  question: { type: Schema.ObjectId, ref: QuestionModel },
});

export const FileModel = model('file', schema);
