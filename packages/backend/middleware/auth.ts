import { TRPCError } from '@trpc/server';
import { t } from '../trpc';
import { verifyJwt } from '../utils/jwt';
import { USER_ROLE } from '../../shared/types/User';

const authProcedure = (roles?: USER_ROLE | USER_ROLE[]) => {
  const _roles = typeof roles === 'string' ? [roles] : roles;

  const authMiddleWare = t.middleware((opts) => {
    try {
      const { req } = opts.ctx;
      const authToken = req.headers['x-auth-token'];
      if (!authToken) throw new TRPCError({ code: 'FORBIDDEN' });

      const decoded = verifyJwt(authToken as string);

      if (!_roles?.length)
        return opts.next({ ctx: { ...opts.ctx, user: decoded } });
      else {
        if (_roles.includes(decoded.role))
          return opts.next({ ctx: { ...opts.ctx, user: decoded } });
        else
          throw new TRPCError({ code: 'FORBIDDEN', message: 'unauthorized' });
      }
    } catch (ex) {
      if (ex instanceof Error) {
        throw new TRPCError({ code: 'FORBIDDEN', message: ex.message });
      }
      return opts.next();
    }
  });

  return t.procedure.use(authMiddleWare);
};

export default authProcedure;
