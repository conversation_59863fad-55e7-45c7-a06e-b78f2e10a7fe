import { FilterQuery } from "mongoose";
import {
  saveCalculatorDataValidator,
  getCalculatorDataValidator,
  updateCalculatorDataValidator,
  deleteCalculatorDataValidator,
} from "../../shared/validators/calculator.validator";
import { ATC } from "../@types/Trpc";
import { CalculatorModel, CalculatorData } from "../models/calculator.model";
import { TRPCError } from "@trpc/server";

export const saveCalculatorData = async ({
  input,
  ctx,
}: ATC<typeof saveCalculatorDataValidator>) => {
  try {
    const calculatorData = new CalculatorModel({
      ...input,
      user: ctx.user._id,
      calculationDate: new Date(),
    });

    const savedData = await calculatorData.save();
    return {
      success: true,
      data: savedData,
      message: "Calculator data saved successfully",
    };
  } catch (error) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to save calculator data",
      cause: error,
    });
  }
};

export const getCalculatorData = async ({
  input,
  ctx,
}: ATC<typeof getCalculatorDataValidator>) => {
  try {
    const filter: FilterQuery<CalculatorData> = {};

    if (input.calculatorId) {
      filter._id = input.calculatorId;
    }

    if (input.assessment) {
      filter.assessment = input.assessment;
    }

    if (input.vendor) {
      filter.vendor = input.vendor;
    }

    if (input.company) {
      filter.company = input.company;
    }

    // Add user filter based on role
    if (ctx.user.role !== "superadmin") {
      filter.user = ctx.user._id;
    }

    const calculatorData = await CalculatorModel.find(filter)
      .populate("assessment", "name")
      .populate("vendor", "company")
      .populate("user", "contact.firstName contact.lastName email")
      .sort({ calculationDate: -1 })
      .lean();

    return {
      success: true,
      data: calculatorData,
    };
  } catch (error) {
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to retrieve calculator data",
      cause: error,
    });
  }
};

export const updateCalculatorData = async ({
  input: { calculatorId, ...updateData },
  ctx,
}: ATC<typeof updateCalculatorDataValidator>) => {
  try {
    const filter: FilterQuery<CalculatorData> = { _id: calculatorId };

    // Non-superadmin users can only update their own data
    if (ctx.user.role !== "superadmin") {
      filter.user = ctx.user._id;
    }

    const updatedData = await CalculatorModel.findOneAndUpdate(
      filter,
      { ...updateData },
      { new: true }
    )
      .populate("assessment", "name")
      .populate("vendor", "company")
      .populate("user", "contact.firstName contact.lastName email");

    if (!updatedData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message:
          "Calculator data not found or you do not have permission to update it",
      });
    }

    return {
      success: true,
      data: updatedData,
      message: "Calculator data updated successfully",
    };
  } catch (error) {
    if (error instanceof TRPCError) {
      throw error;
    }
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to update calculator data",
      cause: error,
    });
  }
};

export const deleteCalculatorData = async ({
  input: { calculatorId },
  ctx,
}: ATC<typeof deleteCalculatorDataValidator>) => {
  try {
    const filter: FilterQuery<CalculatorData> = { _id: calculatorId };

    // Non-superadmin users can only delete their own data
    if (ctx.user.role !== "superadmin") {
      filter.user = ctx.user._id;
    }

    const deletedData = await CalculatorModel.findOneAndDelete(filter);

    if (!deletedData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message:
          "Calculator data not found or you do not have permission to delete it",
      });
    }

    return {
      success: true,
      message: "Calculator data deleted successfully",
    };
  } catch (error) {
    if (error instanceof TRPCError) {
      throw error;
    }
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to delete calculator data",
      cause: error,
    });
  }
};
