import { ATC } from '../@types/Trpc';
import { getAssessmentHistoryValidator } from '../../shared/validators/assessment-history.validator';
import { AsessmentSubmissionHistoryModel } from '../models/assessment-submission-history.model';
import { User } from '../../shared/types/User';
import { Vulnerability } from '../../shared/types/AssessmentSubmission';
import mongoose from 'mongoose';

export const getAssessmentHistory = ({
  input: { question },
}: ATC<typeof getAssessmentHistoryValidator>) =>
  AsessmentSubmissionHistoryModel.aggregate([
    {
      $match: {
        question: new mongoose.Types.ObjectId('question'),
      },
    },
    {
      $lookup: {
        from: 'assessments',
        localField: 'assessment',
        foreignField: '_id',
        as: 'assessment',
      },
    },
    {
      $unwind: {
        path: '$assessment',
      },
    },
    {
      $lookup: {
        from: 'companies',
        localField: 'vendor',
        foreignField: '_id',
        as: 'vendor',
      },
    },
    {
      $unwind: {
        path: '$vendor',
      },
    },
    {
      $lookup: {
        from: 'companies',
        localField: 'submissionSnapshot.vendor',
        foreignField: '_id',
        as: 'submissionSnapshot.vendor',
      },
    },
    {
      $unwind: {
        path: '$submissionSnapshot.vendor',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'submissionSnapshot.user',
        foreignField: '_id',
        as: 'submissionSnapshot.user',
      },
    },
    {
      $unwind: {
        path: '$submissionSnapshot.user',
      },
    },
    {
      $lookup: {
        from: 'assessments',
        localField: 'submissionSnapshot.assessment',
        foreignField: '_id',
        as: 'submissionSnapshot.assessment',
      },
    },
    {
      $unwind: {
        path: '$submissionSnapshot.assessment',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'actionTaker',
        foreignField: '_id',
        as: 'actionTaker',
      },
    },
    {
      $unwind: {
        path: '$actionTaker',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'actionReciver',
        foreignField: '_id',
        as: 'actionReciver',
      },
    },
    {
      $unwind: {
        path: '$actionReciver',
      },
    },
    {
      $lookup: {
        from: 'questions',
        localField: 'submissionSnapshot.submission.question',
        foreignField: '_id',
        as: 'submissionSnapshot.submission.question',
      },
    },
    {
      $unwind: {
        path: '$submissionSnapshot.submission.question',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'submissionSnapshot.vulnerability.analysisBy',
        foreignField: '_id',
        as: 'submissionSnapshot.vulnerability.analysisBy',
      },
    },
    {
      $unwind: {
        path: '$submissionSnapshot.vulnerability.analysisBy',
      },
    },
    {
      $lookup: {
        from: 'companies',
        localField: 'vendor.reportsTo',
        foreignField: '_id',
        as: 'vendor.reportsTo',
      },
    },
    {
      $unwind: {
        path: '$vendor.reportsTo',
      },
    },
  ]);
// AsessmentSubmissionHistoryModel.find({ question })
//   .populate<{
//     actionTaker?: User;
//     actionReciver?: User;
//     submissionSnapshot?: { vulnerability: Vulnerability };
//   }>([
//     { path: 'actionTaker', select: 'email' },
//     { path: 'actionReciver', select: 'email' },
//     { path: 'submissionSnapshot.vulnerability' },
//   ])
//   .sort({ createdAt: -1 });
