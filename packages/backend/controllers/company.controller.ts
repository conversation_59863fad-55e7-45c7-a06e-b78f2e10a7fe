import { FilterQuery, Schema, Types } from 'mongoose';
import {
  manageCompanyValidator,
  createLicenseValidator,
  manageVendorValidator,
  getCompaniesValidator,
} from '../../shared/validators/company.validator';
import { ATC, TC } from '../@types/Trpc';
import { CompanyModel, LicenseModel } from '../models/company.model';
import { TRPCError } from '@trpc/server';
import { Company, CompanyType, Vendor } from '../../shared/types/Company';

export const manageCompany = async ({
  input: { companyId, ...rest },
}: ATC<typeof manageCompanyValidator>) =>
  companyId
    ? await CompanyModel.findByIdAndUpdate(companyId, { ...rest })
    : await new CompanyModel(rest).save();

export const manageVendor = async ({
  input: { companyId, ...rest },
}: ATC<typeof manageVendorValidator>) =>
  companyId
    ? await CompanyModel.findByIdAndUpdate(companyId, { ...rest })
    : await new CompanyModel(rest).save();

export const getCompanies = ({ input }: ATC<typeof getCompaniesValidator>) => {
  const filter: FilterQuery<Company | Vendor> = {};

  if (input) {
    const { reportsTo, type, companies } = input;
    if (companies?.length) filter._id = { $in: companies };
    if (reportsTo) filter.reportsTo = reportsTo;
    if (type?.length) filter.type = { $in: type };
  }

  return CompanyModel.find(filter)
    .populate<{ reportsTo?: { company: string } }>([
      {
        path: 'reportsTo',
        select: 'company',
      },
    ])
    .sort({ createdAt: -1 })
    .lean();
};

export const createLicense = async ({
  input,
}: ATC<typeof createLicenseValidator>) => {
  const { company, from, to } = input;
  if (!Types.ObjectId.isValid(company)) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Requested company is invalid',
    });
  }
  const companyDoc = await CompanyModel.findById(company);

  if (!companyDoc) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'Company not found in DB',
    });
  }
  if (companyDoc.type === CompanyType.COMPANY) {
    return new LicenseModel({ company, from, to }).save();
  }
};
