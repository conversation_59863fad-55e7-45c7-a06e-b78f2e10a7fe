import { TRPCError } from '@trpc/server';
import { Question } from '../../shared/types/Question';
import {
  createSubSectionsQuestionsValidator,
  getSubSectionsQuestionsValidator,
} from '../../shared/validators/subsection-questions.validator';
import { MD } from '../@types';
import { ATC } from '../@types/Trpc';
import { SubSectionQuestionModel } from '../models/subsection-questions.model';

export const createSubSectionQuestion = async ({
  input,
}: ATC<typeof createSubSectionsQuestionsValidator>) => {
  const subsectionQuestionModelExists = await SubSectionQuestionModel.exists({
    subSection: input.subSection,
  });

  if (subsectionQuestionModelExists) {
    throw new TRPCError({
      code: 'CONFLICT',
      message: 'Questions already assigned to this subsection',
    });
  }
  return new SubSectionQuestionModel(input).save();
};

export const getSubSectionsQuestion = ({
  input,
}: ATC<typeof getSubSectionsQuestionsValidator>) => {
  let subSections;
  if (input?.subSection) {
    subSections = Array.isArray(input.subSection)
      ? input.subSection
      : [input?.subSection];
  }

  return SubSectionQuestionModel.find({
    ...input,
    ...(subSections && { subSection: { $in: subSections } }),
  })
    .populate<{
      questions: { question: MD<Question>; _id: string }[];
      subSection: { subSection: string };
    }>([
      { path: 'questions.question', select: 'name' },
      { path: 'subSection', select: 'subSection' },
    ])
    .lean();
};
