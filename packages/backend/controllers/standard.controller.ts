import {
  createSectionValidator,
  createStandardValidator,
  createSubSectionValidator,
  updateSectionValidator,
  updateStandardValidator,
  updateSubSectionValidator,
  getSectionValidator,
  getSubSectionValidator,
} from '../../shared/validators/standard.validator';
import { ATC } from '../@types/Trpc';
import {
  SectionModel,
  StandardModel,
  SubSectionModel,
} from '../models/standard.model';

export const createStandard = ({
  input,
}: ATC<typeof createStandardValidator>) => new StandardModel(input).save();

export const createSection = ({ input }: ATC<typeof createSectionValidator>) =>
  new SectionModel(input).save();

export const createSubSection = ({
  input,
}: ATC<typeof createSubSectionValidator>) => new SubSectionModel(input).save();

// export const createConcept = ({ input }: ATC<typeof createConceptValidator>) =>
//   new ConceptModel(input).save();

//   Update
export const updateStandard = ({
  input: { id, ...others },
}: ATC<typeof updateStandardValidator>) =>
  StandardModel.findByIdAndUpdate(id, others);

export const updateSection = ({
  input: { id, ...others },
}: ATC<typeof updateSectionValidator>) => {
  return SectionModel.findByIdAndUpdate(id, others);
};

export const updateSubSection = ({
  input: { id, ...others },
}: ATC<typeof updateSubSectionValidator>) =>
  SubSectionModel.findByIdAndUpdate(id, others);

// export const updateConcept = ({
//   input: { id, ...others },
// }: ATC<typeof updateConceptValidator>) =>
//   ConceptModel.findByIdAndUpdate(id, others);

//   get
export const getStandard = () =>
  StandardModel.find({}).sort({ createdAt: -1 }).lean();

export const getSections = ({ input }: ATC<typeof getSectionValidator>) =>
  SectionModel.find({ ...input })
    .sort({ createdAt: -1 })
    .lean();

export const getSubSections = ({ input }: ATC<typeof getSubSectionValidator>) =>
  SubSectionModel.find(
    input
      ? {
          section: Array.isArray(input.section)
            ? { $in: input.section }
            : input.section,
        }
      : {}
  )
    .sort({ createdAt: -1 })
    .lean();

// export const getConcept = ({ input }: ATC<typeof getConceptValidator>) =>
//   ConceptModel.find(input).sort({ createdAt: -1 }).lean();
