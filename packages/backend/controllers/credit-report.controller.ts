import { ATC } from '../@types/Trpc';
import {
  createCreditReportValidator,
  getAllCreditReportsValidator,
  getCreditReportValidator,
} from '../../shared/validators/credit-report.validator';
import { CreditReportModel } from '../models/credit-report.model';
import { MD } from '../@types';
import _ from 'lodash';
import { FilterQuery } from 'mongoose';
import { CreditReport } from '../../shared/types/CreditReport';

export const createCreditReport = ({
  input,
  ctx,
}: ATC<typeof createCreditReportValidator>) =>
  new CreditReportModel({ ...input, reportedBy: ctx.user._id }).save();

export const getCreditReports = ({
  input,
}: ATC<typeof getCreditReportValidator>) => {
  const filter: FilterQuery<CreditReport> = {};
  if (input.vendor) filter.vendor = input.vendor;
  if (input.fromDate && input.toDate)
    filter.date = { $gte: input.fromDate, $lte: input.toDate };
  if (input.category) filter.category = input.category;
  if (input.subCategory) filter.subCategory = input.subCategory;

  return CreditReportModel.find(filter)
    .populate<{
      vendor?: MD<{ company: string; reportsTo: { company: string } }>;
    }>([
      {
        path: 'vendor',
        select: 'company reportsTo',
        populate: { path: 'reportsTo', select: 'company' },
      },
    ])
    .sort({ createdAt: -1 })
    .lean();
};
export const getAllCreditReports = ({
  input,
}: ATC<typeof getAllCreditReportsValidator>) => {
  const filter: FilterQuery<CreditReport> = {};
  if (input.vendor) filter.vendor = input.vendor;
  if (input.fromDate && input.toDate)
    filter.date = { $gte: input.fromDate, $lte: input.toDate };
  if (input.category) filter.category = input.category;

  return CreditReportModel.find(filter)
    .populate<{
      vendor?: {
        company: string;
        reportsTo: { company: string };
      };
    }>([
      {
        path: 'vendor',
        select: 'company reportsTo',
        populate: { path: 'reportsTo', select: 'company' },
      },
    ])
    .sort({ createdAt: -1 })
    .lean();
};
