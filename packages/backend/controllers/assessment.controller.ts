import { ATC } from '../@types/Trpc';
import {
  manageAssessmentValidator,
  getAssessmentsValidator,
  uploadAssignmentFilevalidator,
  deleteAssignmentFileValidator,
} from '../../shared/validators/assessment.validator';
import { AssessmentModel } from '../models/assessment.model';
import { ASSIGNMENT_STATUS, Assessment } from '../../shared/types/Assesment';
import { FilterQuery } from 'mongoose';
import { CriticalityLevel } from '../../shared/types/Company';
import { User } from '../../shared/types/User';
import { MD } from '../@types';
import { Section } from '../../shared/types/Standard';
import { TRPCError } from '@trpc/server';
import { UserModel } from '../models/user.model';
import { sendEmail } from '../utils/email';
import { render } from '@react-email/render';
import AssessmentCreatedMail from '../../transactional/emails/assessment.created.mail';
import { CompanyModel } from '../models/company.model';

export const manageAssessment = async ({
  input: { assessmentId, assignments, ...rest },
}: ATC<typeof manageAssessmentValidator>) => {
  if (assessmentId) {
    const assessment = await AssessmentModel.findById(assessmentId);
    if (!assessment) return null;

    const assignmentsToUpdate = assignments.map((assignment) => {
      const savedAssignment = assessment.assignments.find((a) => {
        if (String(a.employee) === String(assignment.employee)) {
          a.sections = assignment.sections;
          a.questions = assignment.questions;
        }
        return a;
      });

      if (savedAssignment) return savedAssignment;
      return assignment;
    });
    await AssessmentModel.findByIdAndUpdate(assessmentId, {
      ...rest,
      assignments: assignmentsToUpdate,
    });
  } else {
    await new AssessmentModel({ ...rest, assignments }).save();
  }

  const assigmentUsers = assignments.map((a) => a.employee);

  const usersMail = await UserModel.find({ _id: { $in: assigmentUsers } });

  const vendorDetails = await CompanyModel.findById(rest.company);

  if (usersMail.length) {
    usersMail.forEach(async (user) => {
      await sendEmail({
        to: user.email,
        subject: 'ASSESSMENT ASSIGNED',
        html: render(
          AssessmentCreatedMail({
            assessment: rest.name,
            vendor: vendorDetails?.company,
            emailId: user.email,
          })
        ),
      });
    });
  }

  return null;
};

export const getAssessments = ({
  input,
}: ATC<typeof getAssessmentsValidator>) => {
  const filter: FilterQuery<Assessment> = {};
  if (input) {
    if (input.vendor) {
      Array.isArray(input.vendor)
        ? (filter.vendor = { $in: input.vendor })
        : (filter.vendor = input.vendor);
    }

    if (input.employee) {
      filter['assignments.employee'] = input.employee;
    }

    input.assessmentStatus &&
      (filter.assessmentStatus = input.assessmentStatus);
  }

  return AssessmentModel.find(filter)
    .populate<{
      vendor?: {
        _id: string;
        company: string;
        criticalityLevels?: CriticalityLevel[];
      };
      company?: { _id: string; company: string };
      sections?: { _id: string; section: string }[];
      assignments: {
        _id: string;
        employee: MD<User>;
        sections: MD<Section>[];
        assignmentStatus: ASSIGNMENT_STATUS;
        questions?: string[];
        assignmentCompletedDate?: Date;
      }[];
    }>([
      { path: 'vendor', select: 'company criticalityLevels' },
      { path: 'company', select: 'company' },
      { path: 'sections', select: 'section' },
      { path: 'assignments.employee', select: 'email contact' },
      { path: 'assignments.sections' },
    ])
    .lean();
};

// Upload Files Assignment
export const uploadAssignmentFile = async ({
  input,
  ctx,
}: ATC<typeof uploadAssignmentFilevalidator>) => {
  const { files, assessmentId, assignmentIndex } = input;
  const assessment = await AssessmentModel.findById(assessmentId);
  if (!assessment) throw new TRPCError({ code: 'NOT_FOUND' });

  if (!assessment.assignments[assignmentIndex])
    throw new TRPCError({ code: 'NOT_FOUND', message: 'assignment not found' });

  const compianceFiles = assessment.assignments[assignmentIndex].files ?? [];
  compianceFiles.push(
    ...files.map((file) => ({ ...file, userId: ctx.user._id }))
  );

  // @ts-expect-error
  assessment.assignments[assignmentIndex].files = files;

  await assessment.save();
};

// Delete Files Assignment
export const deleteAssignmentFile = async ({
  input,
}: ATC<typeof deleteAssignmentFileValidator>) => {
  const { assessmentId, assignmentIndex, fileIndex } = input;
  const assessment = await AssessmentModel.findById(assessmentId);
  if (!assessment) throw new TRPCError({ code: 'NOT_FOUND' });

  if (!assessment.assignments[assignmentIndex])
    throw new TRPCError({ code: 'NOT_FOUND', message: 'assignment not found' });

  const files = assessment.assignments[assignmentIndex].files ?? [];
  files.splice(fileIndex, 1);

  assessment.assignments[assignmentIndex].files = files;

  await assessment.save();
};
