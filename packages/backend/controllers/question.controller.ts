import { FilterQuery } from 'mongoose';
import {
  manageQuestionsValidator,
  getQuestionsValidator,
} from '../../shared/validators/question.validator';
import { ATC } from '../@types/Trpc';
import { QuestionModel } from '../models/questions.model';
import { Question } from '../../shared/types/Question';
import { Section } from '../../shared/types/Standard';
import { MD } from '../@types';

export const manageQuestion = async ({
  input: { questionId, ...other },
}: ATC<typeof manageQuestionsValidator>) => {
  console.log(other, 'file');

  if (questionId)
    await QuestionModel.findByIdAndUpdate(questionId, { ...other });
  else await new QuestionModel({ ...other }).save();
};

export const getQuestions = ({ input }: ATC<typeof getQuestionsValidator>) => {
  const filter: FilterQuery<Question> = {};
  if (input) {
    const { questions, section } = input;
    if (questions?.length) filter._id = { $in: questions };
    if (section?.length) filter.section = { $in: section };
  }

  return QuestionModel.find(filter)
    .populate<{
      section: { _id: string; section: string; sectionType: string };
    }>({
      path: 'section',
      select: 'section sectionType',
    })
    .sort({ createdAt: -1 })
    .lean();
};
