import { ATC } from '../@types/Trpc';
import { File } from '../../shared/types/File';
import {
  createFileValidator,
  createQuestionFileValidator,
  deleteFileValidator,
  getFileValidator,
  getFilesValidator,
} from '../../shared/validators/file.validator';
import { FileModel } from '../models/file.model';
import { FilterQuery } from 'mongoose';
import { TRPCError } from '@trpc/server';

export const createFile = ({ input, ctx }: ATC<typeof createFileValidator>) =>
  new FileModel({ ...input, uploadedBy: ctx.user._id }).save();

export const createQuestionFile = ({
  input,
  ctx,
}: ATC<typeof createQuestionFileValidator>) =>
  FileModel.findOneAndUpdate(
    { uploadedBy: ctx.user._id, question: input.question },
    { ...input, uploadedBy: ctx.user._id },
    { upsert: true }
  );

export const getFiles = ({ input }: ATC<typeof getFilesValidator>) => {
  const filter: FilterQuery<File> = {};
  if (input) {
    const { fileId, uploadedBy, questions } = input;

    if (fileId) filter._id = fileId;
    if (uploadedBy) filter.uploadedBy = input.uploadedBy;
    if (questions) filter.question = { $in: questions };
  }

  return FileModel.find(filter)
    .sort({
      createdAt: input?.sort?.date === 'asc' ? 1 : -1,
    })
    .select('-base64')
    .lean();
};

export const getFile = async ({ input }: ATC<typeof getFileValidator>) => {
  const file = await FileModel.findById(input.fileId).lean();
  if (!file) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'file not found/deleted by user',
    });
  }

  return file;
};

export const deleteFile = ({
  input: { fileId },
}: ATC<typeof deleteFileValidator>) => FileModel.findByIdAndDelete(fileId);
