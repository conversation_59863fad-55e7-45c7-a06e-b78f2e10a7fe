import _ from 'lodash';
import {
  manageUserValidator,
  getUsersValidator,
  loginValidator,
  getSingleSignonLinkValidator,
  resetAccountValidator,
  loginWithTokenValidator,
} from '../../shared/validators/user.validator';
import { ATC, TC } from '../@types/Trpc';
import { AccountResetModel, UserModel } from '../models/user.model';
import bcrypt from 'bcrypt';
import { TRPCError } from '@trpc/server';
import { signJwt, verifyJwt } from '../utils/jwt';
import { Query } from 'mongoose';
import { addDays, addMinutes, isPast } from 'date-fns';
import { sendEmail } from '../utils/email';
import config from '../utils/config';
import axios from 'axios';
import { LinkModel } from '../models/link.model';
import { USER_ROLE } from '../../shared/types/User';
import { render } from '@react-email/render';
import UserCreatedMail from '../../transactional/emails/user.create.mail';

export const createUser = async ({
  input: { password, user: userId, ...other },
}: TC<typeof manageUserValidator>) => {
  const hashPassword = await bcrypt.hash(password, 10);
  if (userId) {
    await UserModel.findOneAndUpdate(
      { _id: userId },
      { ...other, hashPassword }
    );
  } else {
    await new UserModel({ ...other, hashPassword }).save();
  }
  const employeeEmail = other.email; // assuming 'other' includes the user's email address

  const emails = [employeeEmail];
  if (emails.length) {
    emails.forEach(async (email) => {
      await sendEmail({
        to: email,
        subject: 'ACCOUNT CREATION AND DETAILS',
        html: render(
          UserCreatedMail({ emailId: employeeEmail, password: password })
        ),
      });
    });
  }
};

export const login = async ({ input }: TC<typeof loginValidator>) => {
  // verify recaptcha token
  if (!input.recaptchaToken)
    return new TRPCError({
      code: 'FORBIDDEN',
      message: 'recaptcha token not found',
    });

  const res = await axios.post<{
    success: boolean;
    challenge_ts: string;
    hostname: 'localhost';
    score: number;
    action: 'form_submit';
  }>(
    'https://www.google.com/recaptcha/api/siteverify',
    {
      secret: config.RECAPTCHA_SECRET_KEY,
      response: input.recaptchaToken,
    },
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  );

  if (!res.data.success || res.data.score < 0.5)
    return new TRPCError({
      code: 'FORBIDDEN',
      message: 'not secure',
    });

  const user = await UserModel.findOne({ email: input.email });
  if (!user)
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'user not found in database',
    });

  if ((user?.loginRetries || 0) >= 5) {
    const accountReset = await AccountResetModel.findOneAndUpdate(
      { user: user._id },
      {
        expires: addMinutes(new Date(), 5),
        user: user._id,
      },
      { upsert: true, new: true }
    );
    await sendEmail({
      to: user.email,
      subject: 'VendorAI | Account Reset',
      text: `Reset account | ${config.FRONTEND_URL}/reset/${accountReset._id}`,
    });

    throw new TRPCError({
      code: 'FORBIDDEN',
      message:
        'too many retries, reset your accout by clicking a link sent to your email',
    });
  }

  const comparePassword = await bcrypt.compare(
    input.password,
    user.hashPassword
  );

  if (!comparePassword) {
    user.loginRetries = (user.loginRetries || 0) + 1;
    user.save();

    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'wrong email or password',
    });
  }

  const tokenPayload = _.omit((await user).toJSON(), 'hashPassword');
  return { token: signJwt(tokenPayload), ...tokenPayload };
};

export const getUsers = async ({ input }: ATC<typeof getUsersValidator>) => {
  return UserModel.find({ ..._.omitBy(input, _.isUndefined) })
    .populate<{
      companies: {
        _id: string;
        company: string;
        type: string;
        reportsTo: { _id: string; company: string };
      }[];
    }>({
      path: 'companies',
      select: 'company type reportsTo',
      populate: { path: 'reportsTo', select: 'company' },
    })
    .select('-password')
    .lean();
};

export const singleSignOnLink = async ({
  input,
}: ATC<typeof getSingleSignonLinkValidator>) => {
  const { userId } = input;
  const user = await UserModel.findById(userId);
  if (!user)
    throw new TRPCError({ code: 'NOT_FOUND', message: 'user not found' });

  const tokenPayload = _.omit(user.toJSON(), 'hashPassword');
  return { token: signJwt(tokenPayload), ...tokenPayload };
};

export const resetAccount = async ({
  input,
}: TC<typeof resetAccountValidator>) => {
  const { accountResetId } = input;
  const accountReset = await AccountResetModel.findById(accountResetId);
  if (!accountReset)
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'invalid account reset id',
    });

  if (isPast(accountReset.expires))
    throw new TRPCError({
      code: 'CONFLICT',
      message: 'account reset id expired',
    });

  await UserModel.findByIdAndUpdate(accountReset.user, { loginRetries: 0 });
};

export const loginWithToken = async ({
  input,
}: TC<typeof loginWithTokenValidator>) => {
  const decoded = verifyJwt(input.token);

  const user = await UserModel.findById(decoded._id);
  if (!user) throw new TRPCError({ code: 'NOT_FOUND' });

  const tokenPayload = _.omit(user.toJSON(), 'hashPassword');
  return { token: signJwt(tokenPayload), ...tokenPayload };
};

export const me = ({ ctx }: ATC<any>) => UserModel.findById(ctx.user._id);
