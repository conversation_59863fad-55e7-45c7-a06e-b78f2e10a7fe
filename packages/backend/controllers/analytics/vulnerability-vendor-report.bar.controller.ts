import { z } from 'zod';
import { Company, CRITICALITY_LEVELS } from '../../../shared/types/Company';
import { AssessmentSubmissionModel } from '../../models/asessment-submission.model';
import { ASSIGNMENT_STATUS } from '../../../shared/types/Assesment';
import { ATC } from '../../@types/Trpc';
import _ from 'lodash';

const chartData = [
  {
    vendor: 'January',
    [CRITICALITY_LEVELS.CRITICAL]: 0,
    [CRITICALITY_LEVELS.HIGH]: 0,
    [CRITICALITY_LEVELS.MEDIUM]: 0,
    [CRITICALITY_LEVELS.LOW]: 0,
    [CRITICALITY_LEVELS.VERYLOW]: 0,
  },
];

export const vulnerabilityVendorsChartSchema = z.object({
  vendors: z.array(z.string()),
  criticalityLevels: z.array(z.nativeEnum(CRITICALITY_LEVELS)),
  from: z.date(),
  to: z.date(),
});

export const getVulnerabilityReportChartBar = async ({
  input,
}: ATC<typeof vulnerabilityVendorsChartSchema>) => {
  const { from, to, vendors, criticalityLevels } = input;
  const submissions = await AssessmentSubmissionModel.find({
    createdAt: { $gte: from, $lte: to },
    vendor: { $in: vendors },
    'vulnerability.criticalityLevel': { $in: criticalityLevels },
    'assessment.completed': { $ne: ASSIGNMENT_STATUS.PENDING },
  }).populate<{ vendor: Company }>('vendor');

  const groupByVendors = _.groupBy(submissions, (item) => item.vendor.company);

  const chartData = Object.entries(groupByVendors).map(([vendor, data]) => ({
    vendor,
    [CRITICALITY_LEVELS.CRITICAL]: 0,
    [CRITICALITY_LEVELS.HIGH]: 0,
    [CRITICALITY_LEVELS.MEDIUM]: 0,
    [CRITICALITY_LEVELS.LOW]: 0,
    [CRITICALITY_LEVELS.VERYLOW]: 0,
  }));

  submissions.forEach((submission) => {
    const chartDataIndex = chartData.findIndex(
      (data) => data.vendor === submission.vendor.company
    );
    if (chartDataIndex !== -1) {
      chartData[chartDataIndex][
        submission.vulnerability?.criticalityLevel
      ] += 1;
    }
  });

  return chartData;
};
