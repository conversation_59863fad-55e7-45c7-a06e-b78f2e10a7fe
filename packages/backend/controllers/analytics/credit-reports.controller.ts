import * as z from 'zod';
import { ATC } from '../../@types/Trpc';
import { CreditReportModel } from '../../models/credit-report.model';
import { eachMonthOfInterval, format } from 'date-fns';
import { CreditRatingCategory } from '../../../shared/types/CreditReport';

export const creditReportsChartSchema = z.object({
  vendors: z.array(z.string()),
  from: z.date(),
  to: z.date(),
  category: z.array(z.nativeEnum(CreditRatingCategory)),
});

export const getCreditReportsChart = async ({
  input,
}: ATC<typeof creditReportsChartSchema>) => {
  const { from, to, vendors, category } = input;
  const creditReports = await CreditReportModel.find({
    date: { $gte: from, $lte: to },
    vendor: { $in: vendors },
    category: { $in: category },
  });

  // Group by date
  const chartData = eachMonthOfInterval({ start: from, end: to }).map(
    (date) => ({
      month: format(date, 'MMM/yy'),
      [CreditRatingCategory.BUSINESS_CREDIT_RATING]: 0,
      [CreditRatingCategory.EMPLOYEE_SATISFACTION_RATING]: 0,
      [CreditRatingCategory.SOCIALMEDIA_RATING]: 0,
    })
  );

  // Calculate total for each category
  creditReports.forEach((creditReport) => {
    const month = format(creditReport.date, 'MMM/yy');
    const chartDataIndex = chartData.findIndex((data) => data.month === month);
    if (chartDataIndex !== -1)
      chartData[chartDataIndex][creditReport.category] += creditReport.rating;
  });

  return chartData;
};
