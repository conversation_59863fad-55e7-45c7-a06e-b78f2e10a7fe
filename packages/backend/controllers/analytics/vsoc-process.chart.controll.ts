import { z } from 'zod';
import {
  VendorAcceptanceStatus,
  VulnerabilityResolveStatus,
  CompanyExceptionApproval,
  VulnerabilityClosureStatus,
} from '../../../shared/types/AssessmentSubmission';
import { AssessmentSubmissionModel } from '../../models/asessment-submission.model';
import { ASSIGNMENT_STATUS } from '../../../shared/types/Assesment';
import { ATC } from '../../@types/Trpc';
import _ from 'lodash';
import { Types } from 'mongoose';

export const allVendorsVsocChartSchema = z.object({
  from: z.date(),
  to: z.date(),
  vendors: z.array(
    z.string().refine((val) => Types.ObjectId.isValid(val), {
      message: 'Invalid ObjectId',
    })
  ),
});

export const getAllVendoraVsocProcessChart = async ({
  input,
}: ATC<typeof allVendorsVsocChartSchema>) => {
  const { from, to, vendors } = input;

  const submissions = await AssessmentSubmissionModel.aggregate([
    {
      $match: {
        createdAt: { $gte: from, $lte: to },
        vendor: { $in: vendors.map((id) => new Types.ObjectId(id)) },
        'assessment.completed': { $ne: ASSIGNMENT_STATUS.PENDING },
      },
    },
    {
      $facet: {
        // Non-acknowledgement counts
        totalVulnerabilitiesRaised: [
          {
            $match: {
              'vulnerability.criticalityLevel': {
                $in: ['critical', 'high', 'medium', 'low', 'very-low'],
              },
            },
          },
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          },
        ],
        nonAcknowledgementCounts: [
          {
            $match: {
              'vulnerability.criticalityLevel': {
                $in: ['critical', 'high', 'medium', 'low', 'very-low'],
              },
              vendorAcceptanceStatus: {
                $in: [
                  VendorAcceptanceStatus.ACCEPT,
                  VendorAcceptanceStatus.REJECT,
                ],
              },
            },
          },
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          },
        ],
        acceptCounts: [
          {
            $match: {
              vendorAcceptanceStatus: VendorAcceptanceStatus.ACCEPT,
            },
          },
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          },
        ],
        rejectCounts: [
          {
            $match: {
              vendorAcceptanceStatus: VendorAcceptanceStatus.REJECT,
            },
          },
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          },
        ],
        resolvedCounts: [
          {
            $match: {
              'vulnerability.resolveStatus':
                VulnerabilityResolveStatus.RESOLVED,
            },
          },
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          },
        ],
        exceptionCounts: [
          {
            $match: {
              'vulnerability.resolveStatus':
                VulnerabilityResolveStatus.EXCEPTION,
            },
          },
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          },
        ],
        exceptionApprovalCounts: [
          {
            $match: {
              'vulnerability.companyExceptionApproval':
                CompanyExceptionApproval.ACCEPTED,
            },
          },
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          },
        ],
        exceptionRejectedCounts: [
          {
            $match: {
              'vulnerability.companyExceptionApproval':
                CompanyExceptionApproval.REJECTED,
            },
          },
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          },
        ],
        vulnerabilityClosedCounts: [
          {
            $match: {
              'vulnerability.vulnerabilityClosureStatus':
                VulnerabilityClosureStatus.CLOSED,
            },
          },
          {
            $group: {
              _id: null,
              count: { $sum: 1 },
            },
          },
        ],
      },
    },
  ]);
  const totalVulnerabilitiesRaised =
    submissions[0].totalVulnerabilitiesRaised[0]?.count || 0;
  const nonAcknowledgementCounts =
    submissions[0].nonAcknowledgementCounts[0]?.count || 0;

  const nonAcknowledgementTotal =
    totalVulnerabilitiesRaised - nonAcknowledgementCounts;

  const statusCounts = {
    totalVulnerabilitiesRaised: totalVulnerabilitiesRaised,
    nonAcknowledgement: nonAcknowledgementTotal,
    accept: submissions[0].acceptCounts[0]?.count || 0,
    reject: submissions[0].rejectCounts[0]?.count || 0,
    resolved: submissions[0].resolvedCounts[0]?.count || 0,
    exception: submissions[0].exceptionCounts[0]?.count || 0,
    exceptionApproval: submissions[0].exceptionApprovalCounts[0]?.count || 0,
    exceptionRejected: submissions[0].exceptionRejectedCounts[0]?.count || 0,
    vulnerabilityClosed:
      submissions[0].vulnerabilityClosedCounts[0]?.count || 0,
  };

  const chartData = Object.entries(statusCounts).map(([status, count]) => ({
    status,
    value: count,
  }));

  return chartData;
};
