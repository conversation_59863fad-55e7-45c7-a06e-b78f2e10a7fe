import { z } from 'zod';
import { CRITICALITY_LEVELS } from '../../../shared/types/Company';
import { ATC } from '../../@types/Trpc';
import { AssessmentSubmissionModel } from '../../models/asessment-submission.model';
import { ASSIGNMENT_STATUS } from '../../../shared/types/Assesment';
import _ from 'lodash';

export const vulnerabilityChartSchema = z.object({
  vendor: z.string(),
  criticalityLevels: z.array(z.nativeEnum(CRITICALITY_LEVELS)),
  from: z.date(),
  to: z.date(),
});

export const getVulnerabilityReportChartRadial = async ({
  input,
}: ATC<typeof vulnerabilityChartSchema>) => {
  const { from, to, vendor, criticalityLevels } = input;
  const submissions = await AssessmentSubmissionModel.find({
    createdAt: { $gte: from, $lte: to },
    vendor,
    'vulnerability.criticalityLevel': { $in: criticalityLevels },
    'assessment.completed': { $ne: ASSIGNMENT_STATUS.PENDING },
  });

  const chartData = [
    {
      criticality: CRITICALITY_LEVELS.CRITICAL,
      value: 0,
      fill: '#FF0000',
    },
    {
      criticality: CRITICALITY_LEVELS.HIGH,
      value: 0,
      fill: '#FFA500',
    },

    {
      criticality: CRITICALITY_LEVELS.MEDIUM,
      value: 0,
      fill: '#ffff00',
    },

    {
      criticality: CRITICALITY_LEVELS.LOW,
      value: 0,
      fill: '#00FF00',
    },
    {
      criticality: CRITICALITY_LEVELS.VERYLOW,
      value: 0,
      fill: '#0000FF',
    },
  ];

  submissions.forEach((submission) => {
    const chartDataIndex = chartData.findIndex(
      (data) => data.criticality === submission.vulnerability?.criticalityLevel
    );
    if (chartDataIndex !== -1) chartData[chartDataIndex].value += 1;
  });
  return chartData;
};
