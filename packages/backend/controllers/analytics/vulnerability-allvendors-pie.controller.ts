import { z } from 'zod';
import {
  Company,
  CRITICALITY_LEVELS,
  PRIORITY_LEVEL,
} from '../../../shared/types/Company';
import { AssessmentSubmissionModel } from '../../models/asessment-submission.model';
import { ASSIGNMENT_STATUS } from '../../../shared/types/Assesment';
import { ATC } from '../../@types/Trpc';
import _ from 'lodash';
import { Types } from 'mongoose';

export const allVendorvulnerabilityVendorsChartSchema = z.object({
  priority: z.array(z.nativeEnum(PRIORITY_LEVEL)), // Define priority as an array of PRIORITY_LEVEL enum values
  criticalityLevels: z.array(z.nativeEnum(CRITICALITY_LEVELS)),
  from: z.date(),
  to: z.date(),
  vendors: z.array(
    z.string().refine((val) => Types.ObjectId.isValid(val), {
      message: 'Invalid ObjectId',
    })
  ),
});

const chartConfig = {
  [CRITICALITY_LEVELS.CRITICAL]: '#FF0000',
  [CRITICALITY_LEVELS.HIGH]: '#FFA500',
  [CRITICALITY_LEVELS.MEDIUM]: '#ffff00',
  [CRITICALITY_LEVELS.LOW]: '#00FF00',
  [CRITICALITY_LEVELS.VERYLOW]: '#0000FF',
};

// Define the order of criticality levels
const criticalityOrder = [
  CRITICALITY_LEVELS.CRITICAL,
  CRITICALITY_LEVELS.HIGH,
  CRITICALITY_LEVELS.MEDIUM,
  CRITICALITY_LEVELS.LOW,
  CRITICALITY_LEVELS.VERYLOW,
];

export const getAllVendorVulnerabilityReportChartBar = async ({
  input,
}: ATC<typeof allVendorvulnerabilityVendorsChartSchema>) => {
  const { from, to, criticalityLevels, vendors, priority } = input;

  const submissions = await AssessmentSubmissionModel.aggregate([
    {
      $match: {
        createdAt: { $gte: from, $lte: to },
        vendor: { $in: vendors.map((id) => new Types.ObjectId(id)) },
        'vulnerability.criticalityLevel': { $in: criticalityLevels },
        'assessment.completed': { $ne: ASSIGNMENT_STATUS.PENDING },
      },
    },
    {
      $lookup: {
        from: 'companies',
        localField: 'vendor',
        foreignField: '_id',
        as: 'vendor',
      },
    },
    {
      $unwind: {
        path: '$vendor',
      },
    },
    {
      $match: {
        'vendor.priorityLevel': { $in: priority },
      },
    },
  ]);

  const criticalityCounts = _.countBy(
    submissions,
    (item) => item.vulnerability.criticalityLevel
  );

  const chartData = criticalityOrder.map((level) => ({
    criticality: level,
    value: criticalityCounts[level] || 0,
    fill: chartConfig[level],
  }));

  return chartData;
};
