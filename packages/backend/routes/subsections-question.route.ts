import authProcedure from '../middleware/auth';
import { t } from '../trpc';
import {
  createSubSectionsQuestionsValidator,
  getSubSectionsQuestionsValidator,
} from '../../shared/validators/subsection-questions.validator';
import {
  createSubSectionQuestion,
  getSubSectionsQuestion,
} from '../controllers/subsection-question.controller';
import { USER_ROLE } from '../../shared/types/User';

export const subSectionsQuestionsRouter = t.router({
  createSubSectionQuestion: authProcedure(USER_ROLE.SUPERADMIN)
    .input(createSubSectionsQuestionsValidator)
    .mutation(createSubSectionQuestion),
  getSubSectionsQuestion: authProcedure(USER_ROLE.SUPERADMIN)
    .input(getSubSectionsQuestionsValidator)
    .query(getSubSectionsQuestion),
});
