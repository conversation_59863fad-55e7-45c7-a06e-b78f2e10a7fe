import {
  createCreditReportValidator,
  getAllCreditReportsValidator,
  getCreditReportValidator,
} from '../../shared/validators/credit-report.validator';
import {
  createCreditReport,
  getAllCreditReports,
  getCreditReports,
} from '../controllers/credit-report.controller';
import authProcedure from '../middleware/auth';
import { t } from '../trpc';
import { USER_ROLE } from '../../shared/types/User';

export const creditReportRouter = t.router({
  manageCreditReport: authProcedure([
    USER_ROLE.SUPERADMIN,
    USER_ROLE.LEAD_SOC_ANALYST,
  ])
    .input(createCreditReportValidator)
    .mutation(createCreditReport),

  getCreditReports: authProcedure()
    .input(getCreditReportValidator)
    .query(getCreditReports),

  getAllCreditReports: authProcedure()
    .input(getAllCreditReportsValidator)
    .query(getAllCreditReports),
});
