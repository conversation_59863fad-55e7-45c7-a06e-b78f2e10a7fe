import {
  createUser,
  getUsers,
  login,
  loginWithToken,
  me,
  resetAccount,
  singleSignOnLink,
} from '../controllers/user.controller';
import {
  manageUserValidator,
  getUsersValidator,
  loginValidator,
  getSingleSignonLinkValidator,
  resetAccountValidator,
  loginWithTokenValidator,
} from '../../shared/validators/user.validator';
import { t } from '../trpc';
import authProcedure from '../middleware/auth';
import { USER_ROLE } from '../../shared/types/User';

export const userRouter = t.router({
  me: authProcedure().query(me),
  createUser: authProcedure([USER_ROLE.SUPERADMIN])
    .input(manageUserValidator)
    .mutation(createUser),
  getUsers: authProcedure([USER_ROLE.SUPERADMIN])
    .input(getUsersValidator)
    .query(getUsers),
  login: t.procedure.input(loginValidator).mutation(login),
  singleSignOnLink: authProcedure(USER_ROLE.SUPERADMIN)
    .input(getSingleSignonLinkValidator)
    .mutation(singleSignOnLink),
  resetAccount: t.procedure.input(resetAccountValidator).mutation(resetAccount),
  loginWithToken: t.procedure
    .input(loginWithTokenValidator)
    .mutation(loginWithToken),
});
