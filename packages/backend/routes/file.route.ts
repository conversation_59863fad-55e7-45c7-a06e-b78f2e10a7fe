import {
  createFileValidator,
  createQuestionFileValidator,
  deleteFileValidator,
  getFileValidator,
  getFilesValidator,
} from '../../shared/validators/file.validator';
import {
  createFile,
  createQuestionFile,
  deleteFile,
  getFile,
  getFiles,
} from '../controllers/file.controller';
import authProcedure from '../middleware/auth';
import { t } from '../trpc';

export const fileRouter = t.router({
  createFile: authProcedure().input(createFileValidator).mutation(createFile),
  createQuestionFile: authProcedure()
    .input(createQuestionFileValidator)
    .mutation(createQuestionFile),
  getFiles: authProcedure().input(getFilesValidator).query(getFiles),
  getFile: authProcedure().input(getFileValidator).mutation(getFile),
  deleteFile: authProcedure().input(deleteFileValidator).mutation(deleteFile),
});
