import { USER_ROLE } from '../../shared/types/User';
import authProcedure from '../middleware/auth';
import { t } from '../trpc';
import {
  manageAssessmentValidator,
  getAssessmentValidator,
  getAssessmentsValidator,
  uploadAssignmentFilevalidator,
  deleteAssignmentFileValidator,
} from '../../shared/validators/assessment.validator';
import {
  manageAssessment,
  getAssessments,
  uploadAssignmentFile,
  deleteAssignmentFile,
} from '../controllers/assessment.controller';
import { AssessmentModel } from '../models/assessment.model';
import { MD } from '../@types';
import { Question } from '../../shared/types/Question';
import _ from 'lodash';
import { ASSIGNMENT_STATUS } from '../../shared/types/Assesment';
import { Section } from '../../shared/types/Standard';
import { AsessmentSubmissionHistoryModel } from '../models/assessment-submission-history.model';
import {
  userAssessmentSubmissionValidator,
  getAssessmentSubmissionsValidator,
  manageVulnerabilityScoreValidator,
  manageAuditorScoreValdator,
  sendSubmissionScoreCardValidator,
  vendorAcceptanceValidator,
  getAssessmentSubmissionHistoryValidator,
  approveVulnerabilityClosureRequestByCompanyToSocValidator,
  requestVulnerabilityClosureBySocToCompanyValidator,
  requestVulnerabilityExceptionCompanyToSocApprovalValidator,
  updateVulnerabilityClosureBySocValidator,
  updateVulnerabilityResolveStatusByEmployeeValidator,
  requestVulnerabilityExceptionSocToCompanyValidator,
  updateQuestionsSubmissionStateValidator,
  getUpdateQuestionsSubmissionStateValidator,
  approvedVulnerabilityExceptionSocToVendorValidator,
} from '../../shared/validators/assessment-submission.validator';
import {
  userAssessmentSubmission,
  manageVulnerabilityScoreBySocAnalyst,
  manageAuditScoreByAuditor,
  sendSubmissionsScoreCard,
  vendorAcceptance,
  getAssessmentSubmissions,
  approveVulnerabilityClosureRequestByCompanyToSoc,
  requestVulnerabilityClosureBySocToCompany,
  requestVulnerabilityExceptionSocToCompany,
  updateVulnerabilityClosureBySoc,
  updateVulnerabilityResolveStatusByEmployee,
  requestVulnerabilityExceptionCompanyToSocApproval,
  updateQuestionsSubmissionState,
  getUpdateQuestionsSubmissionState,
  requestVulnerabilityExceptionSocToVendorApproved,
} from '../controllers/assessment-submission.controller';
import { getAssessmentHistory } from '../controllers/assessment-history.controller';
import { getAssessmentHistoryValidator } from '../../shared/validators/assessment-history.validator';
import mongoose from 'mongoose';

export const assessmentRouter = t.router({
  createAssessment: authProcedure(USER_ROLE.SUPERADMIN)
    .input(manageAssessmentValidator)
    .mutation(manageAssessment),

  getAssessments: authProcedure()
    .input(getAssessmentsValidator)
    .query(getAssessments),

  getAssessment: authProcedure()
    .input(getAssessmentValidator)
    .query(({ input }) =>
      AssessmentModel.findById(input.assessment)
        .populate<{
          assignments: {
            sections?: MD<Section>[];
            questions?: MD<Question & { section: Section }>[];
            assignmentStatus: ASSIGNMENT_STATUS;
            employee: String;
          }[];
        }>([
          { path: 'assignments.sections' },
          { path: 'assignments.questions', populate: 'section' },
        ])
        .lean()
    ),

  // Submissions
  createAssessmentSubmission: authProcedure()
    .input(userAssessmentSubmissionValidator)
    .mutation(userAssessmentSubmission),

  getAssessmentSubmissions: authProcedure()
    .input(getAssessmentSubmissionsValidator)
    .query(getAssessmentSubmissions),

  manageVulnerabilityScore: authProcedure([
    USER_ROLE.SUPERADMIN,
    USER_ROLE.SOC_ANALYST,
  ])
    .input(manageVulnerabilityScoreValidator)
    .mutation(manageVulnerabilityScoreBySocAnalyst),

  manageAuditScoreByAuditor: authProcedure([
    USER_ROLE.AUDITOR,
    USER_ROLE.SUPERADMIN,
  ])
    .input(manageAuditorScoreValdator)
    .mutation(manageAuditScoreByAuditor),

  sendVulnerabilityScoreCard: authProcedure([
    USER_ROLE.SUPERADMIN,
    USER_ROLE.SOC_ANALYST,
  ])
    .input(sendSubmissionScoreCardValidator)
    .mutation(sendSubmissionsScoreCard),

  vendorAcceptanceStatus: authProcedure([USER_ROLE.VENDOR, USER_ROLE.EMPLOYEE])
    .input(vendorAcceptanceValidator)
    .mutation(vendorAcceptance),

  getAssessmentSubmissionHistory: authProcedure()
    .input(getAssessmentSubmissionHistoryValidator)
    .query(({ input: { question } }) =>
      AsessmentSubmissionHistoryModel.aggregate([
        {
          $match: {
            question: new mongoose.Types.ObjectId(question),
          },
        },
        {
          $lookup: {
            from: 'assessments',
            localField: 'assessment',
            foreignField: '_id',
            as: 'assessment',
          },
        },
        {
          $unwind: {
            path: '$assessment',
          },
        },
        {
          $lookup: {
            from: 'companies',
            localField: 'vendor',
            foreignField: '_id',
            as: 'vendor',
          },
        },
        {
          $unwind: {
            path: '$vendor',
          },
        },
        {
          $lookup: {
            from: 'companies',
            localField: 'submissionSnapshot.vendor',
            foreignField: '_id',
            as: 'submissionSnapshot.vendor',
          },
        },
        {
          $unwind: {
            path: '$submissionSnapshot.vendor',
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'submissionSnapshot.user',
            foreignField: '_id',
            as: 'submissionSnapshot.user',
          },
        },
        {
          $unwind: {
            path: '$submissionSnapshot.user',
          },
        },
        {
          $lookup: {
            from: 'assessments',
            localField: 'submissionSnapshot.assessment',
            foreignField: '_id',
            as: 'submissionSnapshot.assessment',
          },
        },
        {
          $unwind: {
            path: '$submissionSnapshot.assessment',
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'actionTaker',
            foreignField: '_id',
            as: 'actionTaker',
          },
        },
        {
          $unwind: {
            path: '$actionTaker',
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'actionReciver',
            foreignField: '_id',
            as: 'actionReciver',
          },
        },
        {
          $unwind: {
            path: '$actionReciver',
          },
        },
        {
          $lookup: {
            from: 'questions',
            localField: 'submissionSnapshot.submission.question',
            foreignField: '_id',
            as: 'submissionSnapshot.submission.question',
          },
        },
        {
          $unwind: {
            path: '$submissionSnapshot.submission.question',
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'submissionSnapshot.vulnerability.analysisBy',
            foreignField: '_id',
            as: 'submissionSnapshot.vulnerability.analysisBy',
          },
        },
        {
          $unwind: {
            path: '$submissionSnapshot.vulnerability.analysisBy',
          },
        },
        {
          $lookup: {
            from: 'companies',
            localField: 'vendor.reportsTo',
            foreignField: '_id',
            as: 'vendor.reportsTo',
          },
        },
        {
          $unwind: {
            path: '$vendor.reportsTo',
          },
        },
      ]).sort({ createdAt: -1 })
    ),

  /** Vulnerability Resolve */
  updateVulnerabilityResolveStatusByEmployee: authProcedure()
    .input(updateVulnerabilityResolveStatusByEmployeeValidator)
    .mutation(updateVulnerabilityResolveStatusByEmployee),

  requestVulnerabilityExceptionSocToCompany: authProcedure()
    .input(requestVulnerabilityExceptionSocToCompanyValidator)
    .mutation(requestVulnerabilityExceptionSocToCompany),

  requestVulnerabilityExceptionCompanyToSocApproval: authProcedure()
    .input(requestVulnerabilityExceptionCompanyToSocApprovalValidator)
    .mutation(requestVulnerabilityExceptionCompanyToSocApproval),

  /** Vulnerability Closure */
  requestVulnerabilityClosureBySocToCompany: authProcedure()
    .input(requestVulnerabilityClosureBySocToCompanyValidator)
    .mutation(requestVulnerabilityClosureBySocToCompany),
  approveVulnerabilityClosureRequestByCompanyToSoc: authProcedure()
    .input(approveVulnerabilityClosureRequestByCompanyToSocValidator)
    .mutation(approveVulnerabilityClosureRequestByCompanyToSoc),
  updateVulnerabilityClosureBySoc: authProcedure()
    .input(updateVulnerabilityClosureBySocValidator)
    .mutation(updateVulnerabilityClosureBySoc),
  // VulnerabilityExceptionSocToVendor
  requestVulnerabilityExceptionSocToVendorApproved: authProcedure()
    .input(approvedVulnerabilityExceptionSocToVendorValidator)
    .mutation(requestVulnerabilityExceptionSocToVendorApproved),

  // Save Questions State
  updateQuestionsSubmissionState: authProcedure()
    .input(updateQuestionsSubmissionStateValidator)
    .mutation(updateQuestionsSubmissionState),

  getUpdateQuestionsSubmissionState: authProcedure()
    .input(getUpdateQuestionsSubmissionStateValidator)
    .query(getUpdateQuestionsSubmissionState),

  getAssessmentHistory: authProcedure()
    .input(getAssessmentHistoryValidator)
    .query(getAssessmentHistory),

  // Assignment Files
  uploadAssignmentFile: authProcedure()
    .input(uploadAssignmentFilevalidator)
    .mutation(uploadAssignmentFile),

  deleteAssignmentFile: authProcedure()
    .input(deleteAssignmentFileValidator)
    .mutation(deleteAssignmentFile),
});
