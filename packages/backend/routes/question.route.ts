import {
  manageQuestionsValidator,
  getQuestionsValidator,
} from '../../shared/validators/question.validator';
import {
  manageQuestion,
  getQuestions,
} from '../controllers/question.controller';
import authProcedure from '../middleware/auth';
import { QuestionModel } from '../models/questions.model';
import { t } from '../trpc';
import { USER_ROLE } from '../../shared/types/User';

export const questionRouter = t.router({
  manageQuestion: authProcedure(USER_ROLE.SUPERADMIN)
    .input(manageQuestionsValidator)
    .mutation(manageQuestion),

  getQuestions: authProcedure()
    .input(getQuestionsValidator)
    .query(getQuestions),
});
