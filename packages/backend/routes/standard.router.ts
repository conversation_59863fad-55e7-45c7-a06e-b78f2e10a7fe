import { t } from '../trpc';
import {
  createSectionValidator,
  createStandardValidator,
  createSubSectionValidator,
  updateSectionValidator,
  updateStandardValidator,
  updateSubSectionValidator,
  getSectionValidator,
  getSubSectionValidator,
} from '../../shared/validators/standard.validator';
import authProcedure from '../middleware/auth';
import {
  createSection,
  createStandard,
  createSubSection,
  updateSection,
  updateStandard,
  updateSubSection,
  getSections,
  getStandard as getStandards,
  getSubSections,
} from '../controllers/standard.controller';
import { USER_ROLE } from '../../shared/types/User';

export const standardRouter = t.router({
  createStandard: authProcedure([USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN])
    .input(createStandardValidator)
    .mutation(createStandard),
  createSection: authProcedure([USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN])
    .input(createSectionValidator)
    .mutation(createSection),
  createSubSection: authProcedure([
    USER_ROLE.SUPERADMIN,
    USER_ROLE.COMPANY_ADMIN,
  ])
    .input(createSubSectionValidator)
    .mutation(createSubSection),

  // Update
  updateStandard: authProcedure([USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN])
    .input(updateStandardValidator)
    .mutation(updateStandard),
  updateSection: authProcedure([USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN])
    .input(updateSectionValidator)
    .mutation(updateSection),
  updateSubSection: authProcedure([
    USER_ROLE.SUPERADMIN,
    USER_ROLE.COMPANY_ADMIN,
  ])
    .input(updateSubSectionValidator)
    .mutation(updateSubSection),

  // get
  getStandards: authProcedure([
    USER_ROLE.SUPERADMIN,
    USER_ROLE.COMPANY_ADMIN,
  ]).query(getStandards),
  getSections: authProcedure([USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN])
    .input(getSectionValidator)
    .query(getSections),
  getSubSections: authProcedure([USER_ROLE.SUPERADMIN, USER_ROLE.COMPANY_ADMIN])
    .input(getSubSectionValidator)
    .query(getSubSections),
});
