import {
  saveCalculatorDataValidator,
  getCalculatorDataValidator,
  updateCalculatorDataValidator,
  deleteCalculatorDataValidator,
} from '../../shared/validators/calculator.validator';
import {
  saveCalculatorData,
  getCalculatorData,
  updateCalculatorData,
  deleteCalculatorData,
} from '../controllers/calculator.controller';
import authProcedure from '../middleware/auth';
import { t } from '../trpc';
import { USER_ROLE } from '../../shared/types/User';

export const calculatorRouter = t.router({
  saveCalculatorData: authProcedure()
    .input(saveCalculatorDataValidator)
    .mutation(saveCalculatorData),

  getCalculatorData: authProcedure()
    .input(getCalculatorDataValidator)
    .query(getCalculatorData),

  updateCalculatorData: authProcedure()
    .input(updateCalculatorDataValidator)
    .mutation(updateCalculatorData),

  deleteCalculatorData: authProcedure()
    .input(deleteCalculatorDataValidator)
    .mutation(deleteCalculatorData),
});
