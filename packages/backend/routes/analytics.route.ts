import {
  creditReportsChartSchema,
  getCreditReportsChart,
} from '../controllers/analytics/credit-reports.controller';
import {
  allVendorsVsocChartSchema,
  getAllVendoraVsocProcessChart,
} from '../controllers/analytics/vsoc-process.chart.controll';
import {
  allVendorvulnerabilityVendorsChartSchema,
  getAllVendorVulnerabilityReportChartBar,
} from '../controllers/analytics/vulnerability-allvendors-pie.controller';
import {
  getVulnerabilityReportChartRadial,
  vulnerabilityChartSchema,
} from '../controllers/analytics/vulnerability-report-radial.controller';
import {
  getVulnerabilityReportChartBar,
  vulnerabilityVendorsChartSchema,
} from '../controllers/analytics/vulnerability-vendor-report.bar.controller';
import authProcedure from '../middleware/auth';
import { t } from '../trpc';

export const analyticsRouter = t.router({
  creditReportsChart: authProcedure()
    .input(creditReportsChartSchema)
    .query(getCreditReportsChart),
  vulnerabilityChart: authProcedure()
    .input(vulnerabilityChartSchema)
    .query(getVulnerabilityReportChartRadial),
  vulnerabilityVendorsChart: authProcedure()
    .input(vulnerabilityVendorsChartSchema)
    .query(getVulnerabilityReportChartBar),
  allVendorVulnerabilityChartByPriority: authProcedure()
    .input(allVendorvulnerabilityVendorsChartSchema)
    .query(getAllVendorVulnerabilityReportChartBar),
  allVsocProcessChartByvendors: authProcedure()
    .input(allVendorsVsocChartSchema)
    .query(getAllVendoraVsocProcessChart),
});
