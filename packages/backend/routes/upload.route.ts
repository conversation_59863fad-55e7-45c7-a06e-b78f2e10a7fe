import { Router } from 'express';
import fileUpload from 'express-fileupload';
import createHttpError from 'http-errors';
import { uploadFileToS3 } from '../utils/s3';

const uploadRoute = Router();

// send s3FilePath field in reqbody

uploadRoute.post('/', async (req, res) => {
  if (!req.body.s3FilePath)
    return createHttpError.BadRequest('s3FilePath is required');

  const file = req.files?.file as fileUpload.UploadedFile | undefined;
  let filePath;

  if (file) {
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/bmp',
      'audio/mpeg',
      'audio/wav',
      'audio/ogg',
      'video/mp4',
      'video/webm',
      'video/ogg',
      'application/pdf',
    ];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      return createHttpError.BadRequest(
        'Only jpeg, png , bmp, mpeg, wav, ogg, mp4, webm, ogg, pdf files are allowed'
      );
    }

    filePath = await uploadFileToS3({ file, s3Path: req.body.s3FilePath });
  }

  res.send({ filePath });
});

export default uploadRoute;
