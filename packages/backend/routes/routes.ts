import { t } from "../trpc";
import { companyRouter } from "./company.route";
import { questionRouter } from "./question.route";
import { standardRouter } from "./standard.router";
import { assessmentRouter } from "./assessment.route";
import { userRouter } from "./user.route";
import { creditReportRouter } from "./credit-report.route";
import { fileRouter } from "./file.route";
import { analyticsRouter } from "./analytics.route";
import { linkRouter } from "./link.route";
import { calculatorRouter } from "./calculator.route";
import { saveNVDRoute } from "./save-nvd.route";

export const appRouter = t.router({
  health: t.procedure.query(() => ({ health: "OK" })),
  user: userRouter,
  company: companyRouter,
  standard: standardRouter,
  questions: questionRouter,
  assessment: assessmentRouter,
  creditReport: creditReportRouter,
  file: fileRouter,
  analytics: analyticsRouter,
  link: linkRouter,
  calculator: calculatorRouter,
  saveNVD: saveNVDRoute,
});
