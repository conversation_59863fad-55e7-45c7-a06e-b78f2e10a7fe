import { t } from '../trpc';
import {
  manageCompanyValidator,
  createLicenseValidator,
  getCompaniesValidator,
  manageVendorValidator,
} from '../../shared/validators/company.validator';
import authProcedure from '../middleware/auth';
import {
  manageCompany,
  createLicense,
  manageVendor,
  getCompanies,
} from '../controllers/company.controller';
import { CompanyModel } from '../models/company.model';
import { USER_ROLE } from '../../shared/types/User';

export const companyRouter = t.router({
  manageCompany: authProcedure(USER_ROLE.SUPERADMIN)
    .input(manageCompanyValidator)
    .mutation(manageCompany),
  manageVendor: authProcedure(USER_ROLE.SUPERADMIN)
    .input(manageVendorValidator)
    .mutation(manageVendor),
  createLicense: authProcedure(USER_ROLE.SUPERADMIN)
    .input(createLicenseValidator)
    .mutation(createLicense),

  getCompanies: authProcedure()
    .input(getCompaniesValidator)
    .query(getCompanies),
});
