import { S3 } from 'aws-sdk';
import { nanoid } from 'nanoid';
import mime from 'mime-types';
import { UploadedFile } from 'express-fileupload';
import path from 'path';

const s3 = new S3({
  region: 'us-east-2',
});

interface UploadFileToS3Params {
  bucket?: string;
  file: UploadedFile | undefined | null;
  s3Path: 'questions';
}

export const uploadFileToS3 = async ({
  bucket = 'vendorai--dev',
  file,
  s3Path,
}: UploadFileToS3Params) => {
  if (!file) return null;

  const fName = await nanoid();
  const ext = path.extname(file.name);

  const fileName = `${s3Path}/${fName}${ext}`;

  await s3
    .putObject({
      Bucket: bucket,
      Key: fileName,
      Body: file.data,
      ACL: 'public-read',
    })
    .promise();

  return fileName;
};
