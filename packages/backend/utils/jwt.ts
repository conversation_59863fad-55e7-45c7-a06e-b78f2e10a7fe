import _ from 'lodash';
import jwt from 'jsonwebtoken';
import { JwtUser } from '../../shared/types/User';
import { UserModel } from '../models/user.model';
import { TRPCError } from '@trpc/server';
import config from './config';

export const signJwt = (payload: any) => {
  return jwt.sign(payload, config.JWT_SECRET);
};

export const verifyJwt = (token: string) => {
  return jwt.verify(token, config.JWT_SECRET) as JwtUser;
};

export const createJwtByUserEmail = async (email: string) => {
  const user = await UserModel.findOne({ email });
  if (!user) throw new TRPCError({ code: 'NOT_FOUND' });

  const tokenPayload = _.omit(user.toJSON(), 'hashPassword');
  return signJwt(tokenPayload);
};
