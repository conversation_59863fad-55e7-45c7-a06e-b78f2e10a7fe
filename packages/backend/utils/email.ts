import * as msal from '@azure/msal-node';
import axios from 'axios';
import { SendMailOptions } from 'nodemailer';
// import nodemailer, { SendMailOptions } from 'nodemailer';
// import config from './config';

// const transporter = nodemailer.createTransport({
//   // @ts-ignore
//   host: 'smtp.office365.com',
//   secure: false,
//   port: '587',
//   tls: {
//     ciphers: 'SSLv3',
//     rejectUnauthorized: false,
//   },
//   auth: {
//     user: config.ADMIN_EMAIL,
//     pass: config.ADMIN_EMAIL_APP_PASSWORD,
//   },

//   debug: true,
//   logger: true,
// });

// transporter.verify(function (error, success) {
//   if (error) {
//     console.log(error);
//   } else {
//     console.log(success);
//     console.log('Server is ready to take our messages');
//   }
// });
// export const sendEmail = (mailOptions: SendMailOptions) =>
//   transporter.sendMail({ from: config.ADMIN_EMAIL, ...mailOptions });

const msalConfig: msal.Configuration = {
  auth: {
    clientId: process.env.CLIENT_ID!,
    authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
    clientSecret: process.env.CLIENT_SECRET,
  },
};
const cca = new msal.ConfidentialClientApplication(msalConfig);

const getAccessToken = async () => {
  const tokenRequest = {
    scopes: ['https://graph.microsoft.com/.default'],
  };

  const authResponse = await cca.acquireTokenByClientCredential(tokenRequest);
  return authResponse?.accessToken;
};

export const sendEmail = async (mailOptions: SendMailOptions) => {
  try {
    const accessToken = await getAccessToken();

    const emailData = {
      message: {
        subject: mailOptions.subject,
        body: {
          contentType: 'HTML',
          content: mailOptions.html,
        },
        toRecipients: [
          {
            emailAddress: {
              address: mailOptions.to,
            },
          },
        ],
      },
      saveToSentItems: 'true',
    };

    const userPrincipalName = process.env.SENDER_EMAIL; // The email of the sending user in your organization
    const url = `https://graph.microsoft.com/v1.0/users/${userPrincipalName}/sendMail`;

    await axios.post(url, emailData, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.log(error, 'abcd');
  }
};
