import { MID } from '../../backend/@types';

// export enum Category {
//   BUSINESS_CREDIT_RATING = 'BUSINESS CREDIT RATING',
//   EMPLOYEE_SATISFACTION_RATING = '<PERSON>MPL<PERSON><PERSON><PERSON> SATISFACTION RATING',
//   SOCIALMEDIA_RATING = 'SOCIALMEDIA RATING',
// }
export enum Business_Credit_sub_rating {
  DB_PAYDEX = 'D&B PAYDEX SCORE',
  EXPERIAN_INTELLISCORE = 'EXPERIAN INTELLISCORE',
  EQUIFAX_CREDIT_RISK = 'EQUIFAX BUSINESS CREDIT RISK',
  EQUIFAX_FAILURE_SCORE = 'EQUIFAX BUSINESS FAILURE SCORES',
  FICO_CREDIT_SCORE = 'FICO CREDIT SCORE',
}

export enum Employee_satisfaction_sub_rating {
  GLASSDOOR = 'GLASS DOOR',
}
export enum Socialmedia_sub_rating {
  SOCIALMEDIA = 'SOCIAL MEDIA',
}

export enum Quarter {
  Q1 = 'Q1',
  Q2 = 'Q2',
  Q3 = 'Q3',
  Q4 = 'Q4',
}

export type CreditRatingSubCategory =
  | Business_Credit_sub_rating
  | Employee_satisfaction_sub_rating
  | Socialmedia_sub_rating;

export enum CreditRatingCategory {
  BUSINESS_CREDIT_RATING = 'BUSINESS_CREDIT_RATING',
  EMPLOYEE_SATISFACTION_RATING = 'EMPLOYEE_SATISFACTION_RATING',
  SOCIALMEDIA_RATING = 'SOCIALMEDIA_RATING',
}

export type CreditReport = {
  name: string;
  vendor: MID;
  category: CreditRatingCategory;
  subCategory: CreditRatingSubCategory;
  reportedBy: MID;
  quarter: Quarter;
  remarks?: string;
  rating: number;
  date: Date;
};
