import { MID } from '../../backend/@types';
import { Contact } from './Company';

export enum USER_ROLE {
  SUPERADMIN = 'superadmin',
  COMPANY = 'company',
  COMPANY_ADMIN = 'company-admin',
  VENDOR = 'vendor',
  SOC_ANALYST = 'soc-analyst',
  LEAD_SOC_ANALYST = 'lead-soc-analyst',
  EMPLOYEE = 'employee',
  AUDITOR = 'auditor',
}

export enum USER_ACCOUNT_STATUS {
  ACTIVE = 'active',
  DISABLED = 'disabled',
}

export type User = {
  contact: {
    firstName: string;
    lastName: string;
    title?: string | undefined;
    countryCode?: string;
    phone: string;
  };
  email: string;
  hashPassword: string;
  role: USER_ROLE;
  status: USER_ACCOUNT_STATUS;
  companies: MID[];
  loginRetries?: number;
};

export type JwtUser = {
  _id: string;
  role: USER_ROLE;
};

export type AccountReset = {
  user: MID;
  expires: Date;
};
