import { MID } from '../../backend/@types';
import { Contact } from './Company';
import { COMPLIENCES } from './Question';

export enum ASSIGNMENT_STATUS {
  PENDING = 'pending',
  COMPLETED = 'completed',
}
export type AssignmentFile = {
  userId: MID;
  file: string;
  compliance: string;
};

export type Assignment = {
  sections: MID[];
  questions: MID[];
  employee: MID;
  assignmentStatus: ASSIGNMENT_STATUS;
  assignmentCompletedDate?: Date;
  files?: AssignmentFile[];
};

export type Assessment = {
  company: MID;
  vendor: MID;

  name: string;
  expiresAt: Date;
  companyEngagementManager: Contact;
  vendorAssessmentManager: Contact;

  sections: MID[];
  assignments: Assignment[];
};
