import { MID } from '../../backend/@types';

export enum MimeTypes {
  JSON = 'application/json',
  XML = 'application/xml',
  JPEG = 'image/jpeg',
  JPG = 'image/jpeg',
  PNG = 'image/png',
  GIF = 'image/gif',
  MP4 = 'video/mp4',
  MP3 = 'audio/mpeg',
  PDF = 'application/pdf',
  ZIP = 'application/zip',
  ZIP_COMPRESSED = 'application/x-zip-compressed',
}

export enum FileExtensions {
  JSON = 'json',
  XML = 'xml',
  JPEG = 'jpeg',
  JPG = 'jpg',
  PNG = 'png',
  GIF = 'gif',
  MP4 = 'mp4',
  MP3 = 'mp3',
  PDF = 'pdf',
  ZIP = 'zip',
  ZIP_COMPRESSED = 'zip',
}

export type File = {
  name: string;
  base64: string;
  ext: FileExtensions;
  mimeType: string;
  uploadedBy?: MID;
  question?: string;
};
