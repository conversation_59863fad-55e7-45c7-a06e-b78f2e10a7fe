import { MID } from '../../backend/@types';

// Standards by country : India
export type Standard = {
  standard: string;
  description: string;
};

export enum SECTIONTYPE {
  GENERAL = 'general',
  INVENTORY = 'inventory',
}

// Ex: IT
export type Section = {
  standard: MID[];
  section: string;
  description: string;
  sectionType: SECTIONTYPE;
  sectionLable?: string;
};

// Ex: Networking
export type SubSection = {
  section: MID;
  subSection: string;
  description: string;
};

// export type Concept = {
//   subSection: MID;
//   concept: string;
//   description: string;
// };
