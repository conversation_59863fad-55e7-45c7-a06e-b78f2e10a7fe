import { MID } from '../../backend/@types';

export enum CompanyType {
  COMPANY = 'COMPANY',
  VENDOR = 'VENDOR',
}

export type Contact = {
  email: string;
  countryCode?: string;
  phone: string;
  title?: string;
  firstName: string;
  lastName: string;
};

export enum CRITICALITY_LEVELS {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  VERYLOW = 'very-low',
}

export type CriticalityLevel = {
  timeDuration: number;
  level: CRITICALITY_LEVELS;
};

export type License = {
  from: Date;
  to: Date;
  company: MID;
};

export enum PRIORITY_LEVEL {
  P1 = 'P1',
  P2 = 'P2',
  P3 = 'P3',
  P4 = 'P4',
}

type BaseCompany = {
  company: string;
  sector: string;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
};

export type Company = BaseCompany & {
  type: CompanyType.COMPANY;
  primaryContact: Contact[];
  numOfLicensedVendors: number;
};

export type Vendor = BaseCompany & {
  type: CompanyType.VENDOR;
  vendorAssesmentManager: Contact[];
  companyEngagementManager: Contact[];
  priorityLevel: PRIORITY_LEVEL;
  criticalityLevels: CriticalityLevel[];
  reportsTo: MID;
  location?: string;
};
