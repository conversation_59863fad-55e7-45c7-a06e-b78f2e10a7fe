import { MID } from '../../backend/@types';
import { ASSIGNMENT_STATUS } from './Assesment';
import { AssessmentSubmission } from './AssessmentSubmission';

export interface AssesmentSubmissionHistory {
  assessment: MID;
  submission?: MID;
  vendor: MID;

  message?: string;
  htmlContent?: string;
  question?: string;

  submissionSnapshot: AssessmentSubmission & { _id: string };
  actionTaker?: MID;
  actionReciver?: MID;
}
