import { MID } from "../../backend/@types";
import { CRITICALITY_LEVELS } from "./Company";
import { SECTIONTYPE } from "./Standard";

export enum VendorAcceptanceStatus {
  ACCEPT = "accept",
  REJECT = "reject",
  PENDING = "pending",
}

export enum AuditorScore {
  ACCEPT = "accept",
  REJECT = "reject",
  PENDING = "pending",
  NOT_APPLICABLE = "not-applicable",
}

export enum VulnerabilityResolveStatus {
  PENDING = "pending",
  RESOLVED = "resolved",
  EXCEPTION = "exception",
}

export enum CompanyExceptionApproval {
  PENDING = "pending",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
}

export enum VulnerabilityClosureStatus {
  PENDING = "pending",
  CLOSED = "closed",
  OPEN = "open",
}

export type Vulnerability = {
  analysisBy: MID;
  score: string;
  cvssScore: string;
  remarks?: string;
  criticalityLevel: CRITICALITY_LEVELS;
  expiry?: Date;
  vulnerabilityEvidence?: MID;

  resolveStatus?: VulnerabilityResolveStatus;
  vulnerabilityResolvedOrExceptionEvidence?: MID;
  resolveDescription?: string;
  resolvedDate?: Date;

  vulnerabilityResolvedUpdateDateBySoc?: Date;

  companyExceptionApproval?: CompanyExceptionApproval;
  companyExceptionApprovalEvidence?: MID;
  companyExceptionApprovalDescripton?: string;
  companyExceptionEndDate?: Date;
  companyExceptionApprovedDate?: Date;

  vulnerabilityExceptionApprovalDescriptionBySoc?: string;
  vulnerabilityExceptionApprovalEvidenceBySoc?: MID;

  vulnerabilityClosureCompanyApprovalStatus?: VulnerabilityClosureStatus;
  vulnerabilityClosureEvidenceBySoc?: MID;
  vulnerabilityClosureEvidenceByCompany?: MID;
  vulnerabilityClosureDescriptionBySoc?: string;
  vulnerabilityClosureDescriptionByCompany?: string;
  vulnerabilityClosureDateByCompany?: Date;

  vulnerabilityExceptionStatusSocToVendor?: string;
  vulnerabilityExceptionStatusDateSocToVendor?: Date;

  vulnerabilityClosureStatus?: VulnerabilityClosureStatus;
  vulnerabilityClosedDescriptionBySoc?: string;
  vulnerabilityClosureDateBySoc?: Date;
};

export type Audit = {
  score: string;
  remarks?: string;
  auditedBy?: MID;
};

export type AssessmentSubmission = {
  assessment: MID;
  vendor: MID;
  user: MID;
  submission: { question: MID; answer: any; files?: MID[] };
  vulnerability: Vulnerability;
  cvssComparison?: MID;

  vendorAcceptanceStatus?: VendorAcceptanceStatus;
  rejectionEvidence?: MID;
  vendorRejectReason?: string;
  vendorAcceptanceOrRejectionDate?: Date;
} & (
  | { sectionType: SECTIONTYPE.INVENTORY; vulnerability?: Vulnerability }
  | {
      sectionType: SECTIONTYPE.GENERAL;
      audit?: Audit;
    }
);
