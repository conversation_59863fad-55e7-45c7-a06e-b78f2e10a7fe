import { MID } from '../../backend/@types';

// Validation
export type BaseSchema = { required?: boolean };

export type SwitchSchema = BaseSchema;

export enum QUESTIONS_WEIGHTAGE {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
}
export type TextSchema = BaseSchema & {
  // type: 'string';
  min?: {
    val?: number;
    message?: string;
  };
  max?: {
    val?: number;
    message?: string;
  };
};

export type SelectSchema = BaseSchema;
export type RadioSchema = BaseSchema;

export type CheckboxSchema = BaseSchema & {
  // type: 'multiple';
  min?: {
    val: number;
    message: string;
  };
  max?: {
    val: number;
    message: string;
  };
};

// export type QuestionSchema = BooleanSchema | StringSchema | MultipleSchema;

// Questions
// Question inputs
export type BaseQuestionInput = {
  label: string;
  placeholder?: string;
};

export type TextInput = BaseQuestionInput & {
  inputType: 'text';
  schema: TextSchema;
};
// export type TextFieldInput = BaseQuestionInput & {
//   inputType: 'text';
//   schema: StringSchema;
// };
export type SelectInput = BaseQuestionInput & {
  inputType: 'select';
  options: string[];
  schema: SelectSchema;
  answer?: string;
};
export type RadioInput = BaseQuestionInput & {
  inputType: 'radio';
  options: string[];
  schema: RadioSchema;
  answer?: string;
};
export type CheckboxInput = BaseQuestionInput & {
  inputType: 'checkbox';
  options: string[];
  schema: CheckboxSchema;
  answer?: string[];
};
export type SwitchInput = BaseQuestionInput & {
  inputType: 'switch';
  schema: SwitchSchema;
  answer?: boolean;
};

export enum COMPLIENCES {
  PCI_DSS = 'PCI_DSS',
  ISO_27001 = 'ISO_27001',
  HIPAA = 'HIPAA',
  GDPR = 'GDPR',
  SOX_1 = 'SOX_1',
  SOX_2 = 'SOX_2',
  GLBA = 'GLBA',
  FISMA = 'FISMA',
}

export type QuestionInput =
  | TextInput
  | SelectInput
  | RadioInput
  | CheckboxInput
  | SwitchInput;

export type Question = {
  input: QuestionInput;
  weightage: QUESTIONS_WEIGHTAGE;
  file?: string;
  standard: MID;
  section: MID;
  subSection: MID;
  canAttachDocument?: boolean;
  complience?: [COMPLIENCES];
};
