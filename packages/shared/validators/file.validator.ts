import * as z from 'zod';
import { zString } from '.';
import { FileExtensions, MimeTypes } from '../types/File';

export const createFileValidator = z.object({
  base64: zString,
  name: zString,
  ext: z.nativeEnum(FileExtensions),
  mimeType: z.nativeEnum(MimeTypes),
});

export const createQuestionFileValidator = createFileValidator.merge(
  z.object({
    question: zString,
  })
);

export const getFilesValidator = z
  .object({
    fileId: z.string().optional(),
    uploadedBy: z.string().optional(),
    questions: z.array(z.string()).optional(),
    sort: z
      .object({
        date: z.enum(['asc', 'dsc']).default('dsc'),
      })
      .optional(),
  })
  .optional();

export const getFileValidator = z.object({ fileId: zString });

export const deleteFileValidator = z.object({ fileId: zString });
