import * as z from 'zod';
import { zNumber, zString } from '.';
import { COMPLIENCES, QUESTIONS_WEIGHTAGE } from '../types/Question';

// validation schemas
const baseValidationSchema = z.object({ required: z.boolean().optional() });
const textSchema = baseValidationSchema.merge(
  z.object({
    // type: z.literal('string'),
    min: z
      .object({ val: zNumber.optional(), message: zString.optional() })
      .optional(),
    max: z
      .object({ val: zNumber.optional(), message: zString.optional() })
      .optional(),
  })
);
const selectSchema = baseValidationSchema;
const radioSchema = baseValidationSchema;
const CheckboxSchema = baseValidationSchema.merge(
  z.object({
    // type: z.literal('multiple'),
    min: z
      .object({ val: zNumber.optional(), message: zString.optional() })
      .optional(),
    max: z
      .object({ val: zNumber.optional(), message: zString.optional() })
      .optional(),
  })
);

// Question Schemas
export const baseQuestionInputSchema = z.object({
  label: zString,
  placeholder: zString.optional(),
});

export const textInputSchema = baseQuestionInputSchema.merge(
  z.object({
    inputType: z.literal('text'),
    schema: textSchema,
  })
);
export const selectInputSchema = baseQuestionInputSchema.merge(
  z.object({
    inputType: z.literal('select'),
    options: z.array(zString),
    schema: selectSchema,
    answer: zString.optional(),
  })
);

export const radioInputSchema = baseQuestionInputSchema.merge(
  z.object({
    inputType: z.literal('radio'),
    options: z.array(zString),
    schema: radioSchema,
    answer: zString.optional(),
  })
);

export const checkboxInputSchema = baseQuestionInputSchema.merge(
  z.object({
    inputType: z.literal('checkbox'),
    options: z.array(zString),
    schema: CheckboxSchema,
    answer: z.array(zString).optional(),
  })
);

export const switchInputSchema = baseQuestionInputSchema.merge(
  z.object({
    inputType: z.literal('switch'),
    schema: baseValidationSchema,
    answer: z.boolean().optional(),
    // schema: booleanValidationSchema,
  })
);

export const manageQuestionsValidator = z.object({
  questionId: z.string().optional(),
  standard: zString,
  section: zString,
  subSection: zString,
  file: z.string().optional(),
  weightage: z.nativeEnum(QUESTIONS_WEIGHTAGE),
  canAttachDocument: z.boolean().optional(),
  complience: z.array(z.nativeEnum(COMPLIENCES)).optional(),
  input: z.discriminatedUnion('inputType', [
    textInputSchema,
    selectInputSchema,
    radioInputSchema,
    checkboxInputSchema,
    switchInputSchema,
  ]),
});

export const getQuestionsValidator = z
  .object({
    questions: z.array(zString).optional(),
    section: z.array(zString).optional(),
  })
  .optional();
