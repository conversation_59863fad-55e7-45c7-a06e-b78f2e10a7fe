import * as z from 'zod';
import { zContact, zString, zNumber } from '.';

import { ASSIGNMENT_STATUS } from '../types/Assesment';

const assignmentValidator = z.object({
  sections: z.array(zString),
  questions: z.array(zString),
  employee: zString,
  assignmentStatus: z.nativeEnum(ASSIGNMENT_STATUS).optional(),
});

export const manageAssessmentValidator = z.object({
  assessmentId: z.string().optional(),
  vendor: zString,
  company: zString,

  name: zString,
  expiresAt: z.date(),
  companyEngagementManager: zContact,
  vendorAssessmentManager: zContact,

  sections: z.array(zString),
  assignments: z.array(assignmentValidator),
});

export const getAssessmentsValidator = z
  .object({
    vendor: z.string().or(z.array(zString)).optional(),
    employee: z.string().optional(),
    assessmentStatus: z.nativeEnum(ASSIGNMENT_STATUS).optional(),
  })
  .optional();

export const getAssessmentValidator = z.object({
  assessment: zString,
});

export const uploadAssignmentFilevalidator = z.object({
  assessmentId: zString,
  assignmentIndex: zNumber,
  files: z.array(z.object({ file: zString, compliance: zString })),
});

export const deleteAssignmentFileValidator = z.object({
  assessmentId: zString,
  assignmentIndex: zNumber,
  fileIndex: zNumber,
});
