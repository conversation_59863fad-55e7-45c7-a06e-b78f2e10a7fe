import * as z from 'zod';
import {
  zContact,
  zString,
  zNumber,
  zOnlyText,
  ZNonScript,
  zTextNumber,
} from '.';
import {
  CRITICALITY_LEVELS,
  CompanyType,
  PRIORITY_LEVEL,
} from '../types/Company';

export const createLicenseValidator = z.object({
  from: z.date(),
  to: z.date(),
  company: zString,
});

const baseCompanyValidator = z.object({
  type: z.nativeEnum(CompanyType),
  company: zString,
  sector: zString,
  address: zString,
  country: zOnlyText,
  state: zOnlyText,
  city: zOnlyText,
  zipcode: zTextNumber,
});

export const manageCompanyValidator = baseCompanyValidator.merge(
  z.object({
    companyId: zString.optional(), //Passonly if you want to update
    numOfLicensedVendors: zNumber.min(1),
    primaryContact: z.array(zContact),
  })
);

export const manageVendorValidator = baseCompanyValidator.merge(
  z.object({
    companyId: zString.optional(), //Passonly if you want to update
    vendorAssessmentManager: z.array(zContact),
    companyEngagementManager: z.array(zContact),
    priorityLevel: z.nativeEnum(PRIORITY_LEVEL),
    reportsTo: zString,
    location: zString.optional(),
    criticalityLevels: z.array(
      z.object({
        level: z.nativeEnum(CRITICALITY_LEVELS),
        timeDuration: zNumber.min(1),
      })
    ),
  })
);

export const getCompaniesValidator = z
  .object({
    type: z.array(z.nativeEnum(CompanyType)).optional(),
    reportsTo: z.string().optional(),
    companies: z.array(zString).optional(),
  })
  .optional();
