import * as z from "zod";
import { zString } from ".";

const cvssMetricsValidator = z.object({
  AV: z.string().optional().default(""),
  AC: z.string().optional().default(""),
  AT: z.string().optional().default(""),
  PR: z.string().optional().default(""),
  UI: z.string().optional().default(""),
  MC: z.string().optional().default(""),
  MI: z.string().optional().default(""),
  MA: z.string().optional().default(""),
  S: z.string().optional().default(""),
  R: z.string().optional().default(""),
  AU: z.string().optional().default(""),
});

const riskInputsValidator = z.object({
  baseScore: z.number().min(0).max(10),
  exploitAvailable: z.boolean().default(false),
  exploitedInWild: z.boolean().default(false),
  assetCriticality: z.number().min(1).max(5),
  businessImpact: z.number().min(1).max(5),
  exposureLevel: z.number().min(1).max(5),
  mitigationStrength: z.number().min(0).max(1).default(0),
});

const riskResultsValidator = z.object({
  baseScore: z.number(),
  envScoreBefore: z.number(),
  envScoreAfter: z.number(),
  deltaScore: z.number(),
  riskLevelBefore: z.string(),
  riskLevelAfter: z.string(),
  likelihoodBefore: z.number(),
  likelihoodAfter: z.number(),
  impactBefore: z.number(),
  impactAfter: z.number(),
  residualVector: z.string(),
});

const mitigationControlValidator = z.object({
  name: z.string(),
  checked: z.boolean().default(false),
});

const mitigationControlsValidator = z.object({
  Network: z.array(mitigationControlValidator),
  Application: z.array(mitigationControlValidator),
  Database: z.array(mitigationControlValidator),
  Encryption: z.array(mitigationControlValidator),
});

export const saveCalculatorDataValidator = z.object({
  assessment: zString,
  vendor: zString,
  cvssMetrics: cvssMetricsValidator,
  riskInputs: riskInputsValidator,
  mitigationControls: mitigationControlsValidator,
  cvssVector: z.string(),
  riskResults: riskResultsValidator,
  securityRiskOverview: z.string().optional().default(""),
  vendorAIScore: z.string().optional().default(""),
  name: z.string().optional(),
  company: z.string(),
});

export const getCalculatorDataValidator = z.object({
  assessment: zString.optional(),
  vendor: zString.optional(),
  company: zString.optional(),
  calculatorId: zString.optional(),
});

// CVE comparison data schema
export const cveDataValidator = z.object({
  cveId: z.string(),
  cvssScore: z.union([z.string(), z.number()]),
  published: z.string(),
  description: z.string(),
  source: z.string(),
  metrics: z.any(),
});

export const calculatorComparisonDataValidator = z.object({
  calculatorId: z.string(),
  baseScore: z.number(),
  envScoreBefore: z.number(),
  envScoreAfter: z.number(),
  deltaScore: z.number(),
  riskLevelBefore: z.string(),
  riskLevelAfter: z.string(),
});

export const cveComparisonValidator = z.object({
  assessment: z.string(),
  vendor: z.string(),
  company: z.string(),
  cveData: cveDataValidator,
  calculatorData: calculatorComparisonDataValidator,
  comment: z.string(),
  comparisonDate: z.date(),
});

export const updateCalculatorDataValidator = z.object({
  calculatorId: zString,
  cvssMetrics: cvssMetricsValidator.optional(),
  riskInputs: riskInputsValidator.optional(),
  mitigationControls: mitigationControlsValidator.optional(),
  cvssVector: z.string().optional(),
  riskResults: riskResultsValidator.optional(),
  securityRiskOverview: z.string().optional(),
  vendorAIScore: z.string().optional(),
  name: z.string().optional(),
  cveComparison: cveComparisonValidator.optional(),
});

export const deleteCalculatorDataValidator = z.object({
  calculatorId: zString,
});
