import * as z from 'zod';
import { zString } from '.';
import { SECTIONTYPE } from '../types/Standard';

// Create
export const createStandardValidator = z.object({
  standard: zString,
  description: zString,
});
export const createSectionValidator = z.object({
  standard: z.array(zString),
  section: zString,
  description: zString,
  sectionType: z.nativeEnum(SECTIONTYPE),
  sectionLable: zString.optional(),
});

export const createSubSectionValidator = z.object({
  section: zString,
  subSection: zString,
  description: zString,
});
// export const createConceptValidator = z.object({
//   subSection: zString,
//   concept: zString,
//   description: zString,
// });

// Update
export const updateStandardValidator = z.object({
  id: zString,
  standard: zString.optional(),
  description: zString.optional(),
});
export const updateSectionValidator = z.object({
  id: zString,
  standard: z.array(zString).optional(),
  section: zString.optional(),
  description: zString.optional(),
  sectionType: z.nativeEnum(SECTIONTYPE).optional(),
  sectionLable: zString.optional(),
});
export const updateSubSectionValidator = z.object({
  id: zString,
  section: zString.optional(),
  subSection: zString.optional(),
  description: zString.optional(),
});
// export const updateConceptValidator = z.object({
//   id: zString,
//   subSection: zString.optional(),
//   concept: zString.optional(),
//   description: zString.optional(),
// });

// Get

export const getSectionValidator = z
  .object({
    standard: zString,
    sectionType: z.nativeEnum(SECTIONTYPE).optional(),
  })
  .optional();
export const getSubSectionValidator = z
  .object({
    section: zString.or(z.array(zString)),
  })
  .optional();
// export const getConceptValidator = z.object({
//   subSection: zString,
// });
