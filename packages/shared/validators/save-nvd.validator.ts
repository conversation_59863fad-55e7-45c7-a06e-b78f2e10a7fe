import * as z from "zod";

// Define the structure for CVE data from NVD
const cveDataSchema = z.object({
  cveId: z.string(),
  cvssScore: z.union([z.string(), z.number()]),
  published: z.string(),
  description: z.string(),
  source: z.string(),
  metrics: z.any(), // Can be complex object from NVD API
});

// Define the structure for calculator comparison data
const calculatorDataSchema = z.object({
  baseScore: z.number(),
  envScoreBefore: z.number(),
  envScoreAfter: z.number(),
  deltaScore: z.number(),
  riskLevelBefore: z.string(),
  riskLevelAfter: z.string(),
});

// Define the complete NVD data structure
const nvdDataSchema = z.object({
  cveId: z.string(),
  cvssScore: z.union([z.string(), z.number()]),
  published: z.string(),
  description: z.string(),
  source: z.string(),
  metrics: z.any(),
  calculatorData: calculatorDataSchema,
});

const validate = z.object({
  assessment: z.string(),
  vendor: z.string(),
  company: z.string(),
  cvssCalculator: z.string(),
  nvdData: nvdDataSchema,
  comments: z.string(),
  asset: z.string(),
  isVulnerability: z.boolean(),
});

export const saveNVDDataValidator = validate;
