import { z } from 'zod';
import {
  Business_Credit_sub_rating,
  CreditRatingCategory,
  Employee_satisfaction_sub_rating,
  Quarter,
  Socialmedia_sub_rating,
} from '../types/CreditReport';
import { zNumber, zString } from '.';

export const createCreditReportValidator = z.object({
  name: zString,
  category: zString,
  subCategory: zString,
  vendor: zString,
  quarter: z.nativeEnum(Quarter),
  remarks: z.string().optional(),
  rating: zNumber,
  date: z.date(),
});

export const getCreditReportValidator = z.object({
  vendor: zString,
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
  category: z.nativeEnum(CreditRatingCategory).optional(),
  subCategory: z
    .nativeEnum(Business_Credit_sub_rating)
    .or(z.nativeEnum(Employee_satisfaction_sub_rating))
    .or(z.nativeEnum(Socialmedia_sub_rating))
    .optional(),
});

export const getAllCreditReportsValidator = z.object({
  vendor: z.array(zString),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
  category: z.array(z.nativeEnum(CreditRatingCategory)).optional(),
});
