import * as z from 'zod';
import { zString, zPassword, zRegex } from '.';
import { USER_ROLE } from '../types/User';

const userContact = z.object({
  phone: zString.min(1),
  countryCode: zString.min(1).optional(),
  firstName: zRegex,
  lastName: zRegex,
  title: zString.optional(),
});
export const manageUserValidator = z.object({
  user: z.string().optional(),
  email: zString.email(),
  password: zPassword,
  companies: z.array(zString),
  role: z.nativeEnum(USER_ROLE),
  contact: userContact,
});

export const loginValidator = z.object({
  email: zString.email(),
  password: zPassword,
  recaptchaToken: z.string().optional(),
});

export const getUsersValidator = z
  .object({
    role: z.nativeEnum(USER_ROLE).optional(),
    companies: zString,
  })
  .optional();

export const getSingleSignonLinkValidator = z.object({ userId: zString });
export const resetAccountValidator = z.object({ accountResetId: zString });
export const loginWithTokenValidator = z.object({ token: zString });
