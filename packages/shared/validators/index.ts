import { z } from 'zod';

export const zPassword = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[@$!%*?&]/, 'Password must contain at least one special character');
export const zString = z.string().min(1, 'required');

export const zDate = z.date();
export const zOnlyText = z.string().regex(/^[a-zA-Z]+$/, {
  message: 'Only letters are allowed',
});

export const zOnlyNumber = z.string().regex(/^[0-9]+$/, {
  message: 'Only numbers are allowed',
});

export const zTextNumber = z.string().regex(/^[a-zA-Z0-9]+$/, {
  message: 'Only letters and numbers are allowed',
});

export const nonScriptTagRegex = /<(?!\/?script\b)[^>]+>/i;

export const ZNonScript = z.string().refine(
  (value) => {
    const containsHtmlTags = /<[^>]+>/i.test(value);
    const containsNonScriptTags = nonScriptTagRegex.test(value);
    return containsHtmlTags && containsNonScriptTags;
  },
  {
    message: 'Input contains <script> tags or invalid HTML tags',
  }
);

export const zPhone = z
  .string()
  .refine((val) => /^\d{3}-\d{3}-\d{4}$/.test(val), {
    message: 'Invalid phone number format. Expected format: ************',
  });

export const zRegex = zString
  .min(1)
  .regex(/^[A-Za-z\s]+$/, { message: 'only characters allowed' });

export const zNumber = z.coerce.number();

export const zContact = z.object({
  phone: zString.min(1),
  email: zString.email().min(1),
  countryCode: zString.min(1).optional(),
  firstName: zRegex,
  lastName: zRegex,
  title: zString.optional(),
});
