import {
  <PERSON><PERSON>,
  Container,
  <PERSON><PERSON>,
  Head,
  Html,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import * as React from 'react';

type Props = {
  link: string;
  exceptionApprovalOrRejectStatus?: string;
  exceptionApprovalOrRejectDescription?: string;
  token?: string;
  vulnerabilityQuestion?: string;
  vulnerabilityAnswer?: string;
  vulnerability?: string;
};

export const VulnerabilityExceptionApprovalStatusByCompanyMail = ({
  link,
  exceptionApprovalOrRejectStatus,
  exceptionApprovalOrRejectDescription,
  vulnerabilityAnswer,
  vulnerabilityQuestion,
  vulnerability,
  token,
}: Props) => {
  return (
    <Html>
      <Container>
        <Tailwind>
          <Head>
            <Font
              fontFamily='Roboto'
              fallbackFontFamily='Verdana'
              webFont={{
                url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                format: 'woff2',
              }}
              fontWeight={400}
              fontStyle='normal'
            />
          </Head>
          <Section className='w-[1500px]  border rounded-lg flex flex-col p-6 gap-3'>
            <h2>Dear VSOC Team:</h2>
            <Text>
              <p className='text-xl text-black  font-semibold mt-4'>
                Inventory: &nbsp;
                <span className='text-lg font-medium '>
                  {vulnerabilityAnswer}
                </span>
              </p>
              <p className='text-xl text-black  font-semibold mt-4'>
                Inventory Description: &nbsp;
                <span className='text-lg font-medium '>
                  {vulnerabilityQuestion}
                </span>
              </p>
              <p className='text-lg text-black  font-semibold mt-4'>
                Vulnerability description:&nbsp;
                <div
                  dangerouslySetInnerHTML={{
                    __html: vulnerability as string,
                  }}
                  className=' text-lg'
                />
              </p>
            </Text>
            <Text>
              <p className='text-black text-xl font-semibold'>
                Exception Status: &nbsp;
                <span className='text-lg font-medium '>
                  {exceptionApprovalOrRejectDescription?.toUpperCase()}
                </span>
              </p>
            </Text>
            <Text className='text-xl text-black font-medium'>
              {exceptionApprovalOrRejectStatus}
            </Text>
            <Button
              href={`${process.env.FRONTEND_URL}/links/${link}?token=${token}`}
              className='bg-cyan-700 px-6 py-3 rounded-xl text-white font-semibold '
            >
              Click Here
            </Button>
            <Text className='text-xl'>
              If you have any questions, please do not hesitate to contact for
              further reviev.
            </Text>
            <Text className='text-xl'>Thank you for your cooperation.</Text>
            <Text className='text-xl'>Best regards,</Text>
            <Text className='text-xl '>(NR Trust Holdings LLC)</Text>
            <Text className='text-xl'>
              <p className='text-xl'>
                <span className='text-xl font-semibold underline'>
                  Contact:
                </span>
                <br />
                <EMAIL> <br />
                <span>PH: 555-555-5555 </span>
              </p>
            </Text>
          </Section>
        </Tailwind>
      </Container>
    </Html>
  );
};

export default VulnerabilityExceptionApprovalStatusByCompanyMail;
