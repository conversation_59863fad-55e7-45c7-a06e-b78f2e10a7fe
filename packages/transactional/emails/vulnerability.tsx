import {
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON><PERSON>,
  Head,
  Html,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import * as React from 'react';
export type Props = {
  link: string;
  vulnerabilityDescription?: string;
  severityLevel?: string;
  daysToResolve?: string;
  token?: string;
};
export const VulnerabilityTemplate = ({
  link,
  daysToResolve,
  severityLevel,
  vulnerabilityDescription,
  token,
}: Props) => {
  return (
    <Html>
      <Container>
        <Tailwind>
          <Head>
            <Font
              fontFamily='Roboto'
              fallbackFontFamily='Verdana'
              webFont={{
                url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                format: 'woff2',
              }}
              fontWeight={400}
              fontStyle='normal'
            />
          </Head>
          <Section className='w-[1500px]  border rounded-lg flex flex-col p-6 gap-3 '>
            <Text className='font-semibold text-xl'>
              Dear Vulnerability Management team:
            </Text>
            <Text className='text-xl'>
              I hope this email finds you well. I am writing to bring to your
              attention a potential security vulnerability that has been
              identified during our recent review of vendor equipment, which
              supports your organization.
            </Text>
            <Text className='text-xl'>
              Our team has conducted a thorough analysis and uncovered potential
              vulnerabilities that require immediate attention to ensure the
              integrity and security of your systems. Please note that the
              vendor is also part of this communication to ensure that the risk
              associated with the vulnerability is remediated within the
              agreed-upon timelines.
            </Text>

            <h2 className='text-black text-xl font-semibold'>
              Summary of Vulnerabilities:
            </h2>
            <Text>
              <p className='text-lg text-black  font-semibold mt-4'>
                Severity level:&nbsp;
                <span className='text-lg font-medium '>{severityLevel}</span>
              </p>
              <p className='text-lg text-black  font-semibold mt-4'>
                Resolve by this date:&nbsp;
                <span className='text-lg font-medium  '>{daysToResolve}</span>
              </p>
              <p className='text-lg text-black  font-semibold mt-4'>
                Vulnerability description:&nbsp;
                <div
                  dangerouslySetInnerHTML={{
                    __html: vulnerabilityDescription as string,
                  }}
                  className=' text-lg'
                />
              </p>
            </Text>
            <Text className='text-xl'>
              We look forward to your prompt response and collaboration in
              addressing this matter.
            </Text>

            <Text className='text-xl'>
              By clicking the button below, you will be redirected to
              vulnerability findings. This will allow you to review the
              vulnerabilities identified and take necessary actions to mitigate
              them.
            </Text>
            <Button
              href={`${process.env.FRONTEND_URL}/links/${link}?token=${token}`}
              className='bg-cyan-700 px-6 py-3 rounded-xl text-white font-semibold '
            >
              Click Here
            </Button>
            <Text className='text-xl'>
              If you have any questions or encounter any issues, please do not
              hesitate to contact us immediately.
            </Text>
            <Text className='text-xl'>Thank you for your cooperation.</Text>
            <Text className='text-xl'>Best regards,</Text>
            <Text className='text-xl '>(VendorAi VSOC Team)</Text>
            <img
              src={`https://staging.vendorsecurity.ai/images/logo.png`}
              alt='logo'
              className='w-32'
            />
            <Text className='text-xl'>
              <p className='text-xl'>
                <span className='text-xl font-semibold underline'>
                  Contact:
                </span>
                <br />
                <EMAIL>
                <br />
                <EMAIL> <br />
                <span>PH: ************ </span>
              </p>
            </Text>
          </Section>
        </Tailwind>
      </Container>
    </Html>
  );
};

export default VulnerabilityTemplate;
