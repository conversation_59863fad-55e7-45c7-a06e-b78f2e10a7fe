import {
  <PERSON><PERSON>,
  Container,
  <PERSON><PERSON>,
  Head,
  Html,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import * as React from 'react';
export type Props = {
  emailId?: string;
  password?: string;
  token?: string;
  assessment?: string;
  vendor?: string;
};
export const AssessmentCreatedMail = ({
  emailId,
  assessment,
  vendor,
}: Props) => {
  return (
    <Html>
      <Container>
        <Tailwind>
          <Head>
            <Font
              fontFamily='Roboto'
              fallbackFontFamily='Verdana'
              webFont={{
                url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                format: 'woff2',
              }}
              fontWeight={400}
              fontStyle='normal'
            />
          </Head>
          <Section className='w-[1500px]  border rounded-lg flex flex-col p-6 gap-3 '>
            <Text className='font-semibold text-xl'>Dear User:</Text>
            <Text className='text-xl'>
              We are pleased to inform that, An assignment has been successfully
              created.
            </Text>
            <h2 className='text-black text-xl font-semibold'>
              ASSESSMENT DETAILS:
            </h2>
            <Text>
              <p className='text-black text-xl'>
                Vendor:
                <span>{vendor}</span>
              </p>
              <p className='text-black text-xl'>
                Assignment-Details:
                <span>{assessment}</span>
              </p>
            </Text>
            <Button
              href={`${process.env.FRONTEND_URL}`}
              className='bg-cyan-700 px-6 py-3 rounded-xl text-white font-semibold '
            >
              Click here to login
            </Button>

            <Text className='text-xl'>
              If you have any questions or encounter any issues, please do not
              hesitate to contact us immediately.
            </Text>
            <Text className='text-xl'>Thank you for your cooperation.</Text>
            <Text className='text-xl'>Best regards,</Text>
            <Text className='text-xl '>(VendorAi VSOC Team)</Text>
            <img
              src={`https://staging.vendorsecurity.ai/images/logo.png`}
              alt='logo'
              className='w-32'
            />
            <Text className='text-xl'>
              <p className='text-xl'>
                <span className='text-xl font-semibold underline'>
                  Contact:
                </span>
                <br />
                <EMAIL> <br /> <EMAIL> <br />
                <span>ph: ************ </span>
              </p>
            </Text>
          </Section>
        </Tailwind>
      </Container>
    </Html>
  );
};

export default AssessmentCreatedMail;
