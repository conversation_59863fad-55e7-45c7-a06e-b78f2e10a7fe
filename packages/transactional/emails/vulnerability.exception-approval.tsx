import {
  <PERSON><PERSON>,
  Container,
  <PERSON>ont,
  Head,
  Html,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import * as React from 'react';

type Props = {
  link: string;
  exception?: string;
  exceptionDescription?: string;
  token?: string;
  vendorExceptionDescription?: string;
  vulnerability?: string;
  vulnerabilityQuestion?: string;
  vulnerabilityAnswer?: string;
};

export const VulnerabilityExceptionApprovalMail = ({
  link,
  exception,
  exceptionDescription,
  token,
  vulnerabilityAnswer,
  vulnerabilityQuestion,
  vendorExceptionDescription,
  vulnerability,
}: Props) => {
  return (
    <Html>
      <Container>
        <Tailwind>
          <Head>
            <Font
              fontFamily='Roboto'
              fallbackFontFamily='Verdana'
              webFont={{
                url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                format: 'woff2',
              }}
              fontWeight={400}
              fontStyle='normal'
            />
          </Head>
          <Section className='w-[1500px]  border rounded-lg flex flex-col p-6 gap-3 '>
            <h2>Dear Customer,</h2>
            <Text className='text-xl'>
              I hope this message finds you well. I am writing to formally
              inform you that the Vendor is requesting an exception to the
              resolve below finding(s).
            </Text>
            <Text>
              <pre className='text-xl text-black font-semibold'>
                Note: &nbsp;
                <br />
                <span className='text-lg text-black font-normal'>
                  To facilitate continuous monitoring, implementation and
                  inventory information is collected for vendor-provided
                  hardware, software, operating systems, libraries,
                  and related assets.
                </span>
              </pre>
            </Text>
            <Text>
              <p className='text-lg text-black  font-semibold mt-4'>
                Inventory: &nbsp;
                <span className='text-lg font-medium '>
                  {vulnerabilityAnswer}
                </span>
              </p>
              <p className='text-lg text-black  font-semibold mt-4'>
                Inventory Description: &nbsp;
                <span className='text-lg font-medium '>
                  {vulnerabilityQuestion}
                </span>
              </p>
              <p className='text-lg text-black  font-semibold mt-4'>
                Vulnerability description:&nbsp;
                <div
                  dangerouslySetInnerHTML={{
                    __html: vulnerability as string,
                  }}
                  className=' text-lg'
                />
              </p>
            </Text>
            <Text>
              <p className='text-lg text-black  font-semibold mt-4'>
                Status: &nbsp;
                <span className='text-lg font-medium '>
                  Vendor Requesting for {exception}
                </span>
              </p>
              <p className='text-lg text-black  font-semibold mt-4'>
                Vendor Exception Description: &nbsp;
                <span className='text-lg font-medium '>
                  {vendorExceptionDescription}
                </span>
              </p>
              <p className='text-lg text-black  font-semibold mt-4'>
                VSOC Recommendation:&nbsp;
                <span className='text-lg font-medium '>
                  {exceptionDescription}
                </span>
              </p>
            </Text>
            <Button
              href={`${process.env.FRONTEND_URL}/links/${link}?token=${token}`}
              className='bg-cyan-700 px-6 py-3 rounded-xl text-white font-semibold '
            >
              Click Here
            </Button>
            <Text className='text-xl'>
              We are open to any additional requirements or conditions you might
              impose to grant this exception and if necessary, we will set up a
              call with vendor to discuss this matter further to provide any
              necessary clarifications you might need.
            </Text>
            <Text className='text-xl'>
              Thank you for considering this request. I look forward to your
              feedback to take further steps.
            </Text>
            <Text className='text-xl'>Best regards,</Text>
            <Text className='text-xl '>(VendorAi VSOC Team)</Text>
            <img
              src={`https://staging.vendorsecurity.ai/images/logo.png`}
              alt='logo'
              className='w-32'
            />
            <Text className='text-xl'>
              <p className='text-xl'>
                <span className='text-xl font-semibold underline'>
                  Contact:
                </span>
                <br />
                <EMAIL> <br />
                <EMAIL> <br />
                <span>PH: 609-843-0415 </span>
              </p>
            </Text>
          </Section>
        </Tailwind>
      </Container>
    </Html>
  );
};

export default VulnerabilityExceptionApprovalMail;
