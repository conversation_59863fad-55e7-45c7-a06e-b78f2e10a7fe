import {
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON><PERSON>,
  Head,
  Html,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import * as React from 'react';

export type Props = {
  link: string;
  vulnerabilityAccceptanceDescription?: string;
  vulnerabilityAcceptanceStatus?: string;
  vulnerabilityQuestion?: string;
  vulnerabilityAnswer?: string;
  vulnerability?: string;
  token?: string;
};

export const VulnerabilityAcceptanceMail = ({
  link,
  vulnerabilityAccceptanceDescription,
  vulnerabilityAcceptanceStatus,
  vulnerabilityAnswer,
  vulnerabilityQuestion,
  vulnerability,
  token,
}: Props) => {
  return (
    <Html>
      <Container>
        <Tailwind>
          <Head>
            <Font
              fontFamily='Roboto'
              fallbackFontFamily='Verdana'
              webFont={{
                url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                format: 'woff2',
              }}
              fontWeight={400}
              fontStyle='normal'
            />
          </Head>
          <Section className='w-[1500px]  border rounded-lg flex flex-col p-6 gap-3'>
            <h2>Dear VSOC,</h2>
            <Text>
              <p className='text-lg text-black  font-semibold mt-4'>
                Inventory: &nbsp;
                <span className='text-base font-medium '>
                  {vulnerabilityAnswer}
                </span>
              </p>
            </Text>
            <Text>
              <p className='text-lg text-black  font-semibold mt-4'>
                Inventory Description: &nbsp;
                <span className='text-base font-medium '>
                  {vulnerabilityQuestion}
                </span>
              </p>
            </Text>
            <Text>
              <p className='text-lg text-black  font-semibold mt-4'>
                Vulnerability description:&nbsp;
                <div
                  dangerouslySetInnerHTML={{
                    __html: vulnerability as string,
                  }}
                  className=' text-lg'
                />
              </p>
            </Text>
            <Text>
              <p className='text-lg text-black  font-semibold mt-4'>
                Vulnerability &nbsp;
                <span className='text-base font-medium'>
                  {vulnerabilityAcceptanceStatus}
                </span>
                &nbsp;by vendor
              </p>
            </Text>
            <Text>
              <p className='text-base text-black  font-semibold mt-4'>
                {vulnerabilityAccceptanceDescription}
              </p>
            </Text>
            <Button
              href={`${process.env.FRONTEND_URL}/links/${link}?token=${token}`}
              className='bg-cyan-700 px-6 py-3 rounded-xl text-white font-semibold '
            >
              Click Here
            </Button>
            {/* <Text>
              If you have any questions or encounter any issues, please do not
              hesitate to contact us immediately.
            </Text>
            <Text>Thank you for your cooperation.</Text>
            <Text>Best regards,</Text>
            <Text className='text-base underline'>Vendor AI Team</Text>
            <Text>
              <p>
                <span className='text-base font-semibold underline'>
                  Contact:
                </span>
                <br />
                <EMAIL> <br />
                <span> *************</span>
              </p>
            </Text> */}
          </Section>
        </Tailwind>
      </Container>
    </Html>
  );
};

export default VulnerabilityAcceptanceMail;
