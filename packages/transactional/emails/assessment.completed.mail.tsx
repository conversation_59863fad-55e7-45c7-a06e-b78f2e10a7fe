import {
  <PERSON><PERSON>,
  Container,
  <PERSON><PERSON>,
  Head,
  Html,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import * as React from 'react';
export type Props = {
  link: string;
  vendor?: string;
  assessment?: string;
  vendorRespondent?: string;
  company?: string;
  token?: string;
};
export const AssessmentCompletedMail = ({
  link,
  vendorRespondent,
  assessment,
  vendor,
  company,
  token,
}: Props) => {
  return (
    <Html>
      <Container>
        <Tailwind>
          <Head>
            <Font
              fontFamily='Roboto'
              fallbackFontFamily='Verdana'
              webFont={{
                url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                format: 'woff2',
              }}
              fontWeight={400}
              fontStyle='normal'
            />
          </Head>
          <Section className='w-[1500px]  border rounded-lg flex flex-col p-6 gap-3 '>
            <Text className='font-semibold text-xl'>Dear VSOC team:</Text>
            <Text className='text-xl'>
              We are pleased to inform you that the assessment requested from
              {vendor} has been successfully completed.
            </Text>
            <h2 className='text-black text-xl font-semibold'>
              ASSESSMENT DETAILS:
            </h2>
            <Text>
              <p className='text-lg text-black  font-semibold mt-4'>
                VENDOR NAME:&nbsp;
                <span className='text-lg font-medium '>{vendor}</span>
              </p>
              <p className='text-lg text-black  font-semibold mt-4'>
                CUSTOMER:&nbsp;
                <span className='text-lg font-medium '>{company}</span>
              </p>
              <p className='text-lg text-black  font-semibold mt-4'>
                ASSESSMENT NAME:&nbsp;
                <span className='text-lg font-medium  '>{assessment}</span>
              </p>
              {/* <p className='text-lg text-black  font-semibold mt-4'>
                VENDOR RESPONDENT:&nbsp;
                <span className='text-lg font-medium '>{vendorRespondent}</span>
              </p> */}
            </Text>
            <Text className='text-xl'>
              By clicking the button below, you will be redirected to
              assessments page. This will allow you to review the assessments.
            </Text>
            <Button
              href={`${process.env.FRONTEND_URL}/links/${link}?token=${token}`}
              className='bg-cyan-700 px-6 py-3 rounded-xl text-white font-semibold '
            >
              Click Here
            </Button>
            <Text className='text-xl'>
              If you have any questions or encounter any issues, please do not
              hesitate to contact us immediately.
            </Text>
            <Text className='text-xl'>Thank you for your cooperation.</Text>
            <Text className='text-xl'>Best regards,</Text>
            <Text className='text-xl '>(VendorAi VSOC Team)</Text>
            <img
              src={`https://staging.vendorsecurity.ai/images/logo.png`}
              alt='logo'
              className='w-32'
            />
            <Text className='text-xl'>
              <p className='text-xl'>
                <span className='text-xl font-semibold underline'>
                  Contact:
                </span>
                <br />
                <EMAIL> <br /> <EMAIL> <br />
                <span>ph: ************ </span>
              </p>
            </Text>
          </Section>
        </Tailwind>
      </Container>
    </Html>
  );
};

export default AssessmentCompletedMail;
