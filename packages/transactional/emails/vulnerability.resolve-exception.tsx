import {
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON><PERSON>,
  <PERSON>,
  Html,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import { VulnerabilityResolveStatus } from '../../shared/types/AssessmentSubmission';
import * as React from 'react';
export type Props = {
  link: string;
  vulnerabilityResolveOrExceptionStatus?: string;
  vulnerabilityResolveOrExceptionDescription?: string;
  token?: string;
  vulnerabilityQuestion?: string;
  vulnerabilityAnswer?: string;
  vulnerability?: string;
};

export const VulnerabilityResolveExceptionMail = ({
  link,
  vulnerabilityResolveOrExceptionDescription,
  vulnerabilityResolveOrExceptionStatus,
  vulnerabilityAnswer,
  vulnerabilityQuestion,
  vulnerability,
  token,
}: Props) => {
  return (
    <Html>
      <Container>
        <Tailwind>
          <Head>
            <Font
              fontFamily='Roboto'
              fallbackFontFamily='Verdana'
              webFont={{
                url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                format: 'woff2',
              }}
              fontWeight={400}
              fontStyle='normal'
            />
          </Head>
          <Section className='w-[1500px]  border rounded-lg flex flex-col p-6 gap-3 '>
            <Text className='text-xl text-black font-semibold'>
              Inventory: &nbsp;
              <span className='text-lg font-medium '>
                {vulnerabilityAnswer}
              </span>
            </Text>
            <Text className='text-xl text-black font-semibold'>
              Inventory information: &nbsp;
              <span className='text-lg font-medium '>
                {vulnerabilityQuestion}
              </span>
            </Text>
            <Text>
              <p className='text-lg text-black  font-semibold mt-4'>
                Vulnerability description:&nbsp;
                <div
                  dangerouslySetInnerHTML={{
                    __html: vulnerability as string,
                  }}
                  className=' text-lg'
                />
              </p>
            </Text>
            <Text className='text-xl text-black font-semibold capitalize'>
              Status: &nbsp;
              <span className='text-lg font-medium '>
                {vulnerabilityResolveOrExceptionStatus ===
                VulnerabilityResolveStatus.RESOLVED
                  ? `Vendor  ${vulnerabilityResolveOrExceptionStatus} 
              Vulnerability`
                  : vulnerabilityResolveOrExceptionStatus ===
                    VulnerabilityResolveStatus.EXCEPTION
                  ? `Vendor Requested ${vulnerabilityResolveOrExceptionStatus} For
              Vulnerability`
                  : null}
              </span>
            </Text>
            <Text className='text-xl'>
              <p className='text-xl text-black font-semibold'>
                Vulnerability Description:&nbsp;
                <span className='text-lg font-medium'>
                  {vulnerabilityResolveOrExceptionDescription}
                </span>
              </p>
            </Text>
            <Button
              href={`${process.env.FRONTEND_URL}/links/${link}?token=${token}`}
              className='bg-cyan-700 px-6 py-3 rounded-xl text-white font-semibold '
            >
              Click Here
            </Button>
            <Text className='text-xl'>
              If you have any questions or encounter any issues, please do not
              hesitate to contact us immediately.
            </Text>
            <Text className='text-xl'>Thank you for your cooperation.</Text>
            <Text className='text-xl'>Best regards,</Text>
            <Text className='text-xl '>(VendorAi VSOC Team)</Text>
            <img
              src={`https://staging.vendorsecurity.ai/images/logo.png`}
              alt='logo'
              className='w-32'
            />
            <Text className='text-xl'>
              <p className='text-xl'>
                <span className='text-xl font-semibold underline'>
                  Contact:
                </span>
                <br />
                <EMAIL> <br /> <EMAIL> <br />
                <span>ph: ************ </span>
              </p>
            </Text>
          </Section>
        </Tailwind>
      </Container>
    </Html>
  );
};

export default VulnerabilityResolveExceptionMail;
